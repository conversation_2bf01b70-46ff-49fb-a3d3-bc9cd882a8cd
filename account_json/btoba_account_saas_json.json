{"schema": {"change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status", "is_handling_name_from_json": true}, "employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "precinct": "Precinct Details", "marital_statuses": "Marital Status", "birthday": "DOB", "age": "Age", "social_security_number": "SS #", "veteran_status": "Veteran Status", "shield_number": "Shield #", "a_number": "United Healthcare ID #", "payroll_id": "Araya #", "previous_shield_number": "AMERITAS Policy #", "payroll_auto_align": true, "member_id_autoload": true, "title_code": "Employee ID #", "primary_work_location": "B&T ID", "placard_number": "BSC ID", "rdo": "Tax ID", "start_date": "Hire Date", "notes": "Notes", "janus_card": "<PERSON><PERSON>", "janus_card_opt_out_date": "<PERSON><PERSON>t Out Date", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "social_security_number_format": "9", "marital_status_date": "Marital Status Date", "username": "Username", "app_downloaded": "App Downloaded", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode", "birthday", "social_security_number"], "unique_fields": ["username", "payroll_id", "previous_shield_number", "a_number"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "suffix", "shield_number", "birthday"], "associated_model": {"contacts": ["value"], "benefit_coverages": ["name", "first_name", "last_name", "suffix"], "beneficiaries": ["name"]}}, "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "required_fields": []}}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Work", "personal_phone": "Mobile", "home_phone": "Home", "required_fields": []}, "email_address": {"key_name": "Email Address", "personal_email": "Personal", "required_fields": []}}, "genders": {"key_name": "Gender", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "offices": {"key_name": "Command", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id"]}, "employment_statuses": {"key_name": "Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id", "start_date"]}, "positions": {"key_name": "Union Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "delegate_employees": {"key_name": "Delegate Name"}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "office_id", "employee_id", "start_date"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "firearm_type": "Type", "firearm_test_type": "Test Type", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"], "customization": true, "required_tables": ["firearm_statuses"]}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "leaves": {"started_at": "From", "ended_at": "To", "hours_used": "No. of hours used", "notes": "Notes", "required_fields": ["started_at", "leave_type", "hours_used"]}, "employee_analytics": {"key_name": "Analytics", "customization": true, "dashboard_stats": true, "required_tables": ["lodis", "vacation", "personal", "comp_time"]}, "lodis": {"key_name": "IOD", "incident_date": "Date of incident", "return_date": "Date of return", "office_name": "Location", "notes": "Notes", "required_fields": ["office_id", "employee_id", "incident_date"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "discipline_settings": {"key_name": "Disciplines", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_discipline_settings": {"discipline_setting_id": "Type", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Grievances", "date": "Date", "description": "Description", "files": "Files", "required_fields": ["grievance_id"]}, "meeting_types": {"key_name": "Meeting Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"], "benefits_name": ["Optical", "Dental", "Prescription"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "serviced_expiration": "Serviced Expiration", "description": "Description", "benefit_customization": true, "auto_expire_benefits": true, "deceased_dependents_expire_on_next_month": true, "employee_status": {"section_I": ["Active", "Retired"], "section_II": ["Cobra Active", "Cobra Retiree"], "section_III": ["Deceased", "Terminated", "Inactive", "Promoted", "Resigned", "Bad Standing", "Provisional Sgt"]}, "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "gender_id": "Gender", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "phone": "Phone Number", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "required_fields": ["name", "employee_id", "percentage"], "required_auto_fields": ["address"]}, "benefit_coverages": {"key_name": "Benefit Coverages", "first_name": "First Name", "last_name": "Last Name", "suffix": "Suffix", "gender_id": "Gender", "relationship": "Relationship", "dependent": "Dependent", "social_security_number": "employees.social_security_number", "address": "Address", "phone": "Phone Number", "birthday": "DOB", "age": "Age", "effective_date": "Effective Date", "expires_at": "Expiration", "serviced_expiration": "Serviced Expiration", "marital_status_divorced": true, "auto_dependent_code": true, "araya_person_code_autoupdate": true, "disabled_child_edit": true, "coverage_expire_age": 26, "optical_coverage_expire_age": 19, "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "first_name", "last_name", "social_security_number", "relationship"], "relationship_value": [{"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Step Child", "key": "step_child"}, {"value": "Disabled <PERSON> Child", "key": "disabled_step_child"}]}, "benefit_disbursements": {"key_name": "Benefit Disbursements", "year": "Year", "date": "Date", "relationship": "Relationship", "benefit_coverage_id": "Person Serviced", "reference_number": "Reference Number", "optical_coverage_expiration": true, "calendar_year_expiration": false, "amount": "Amount", "notes": "Notes", "order_by_date": true, "required_fields": ["employee_id", "employee_benefit_id", "date"], "relationship_value": [{"value": "Self", "key": "self"}, {"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}]}, "payment_types": {"key_name": "Payment Types", "name": "Name", "description": "Description", "required_fields": ["name"]}, "pacfs": {"key_name": "PAF", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_pacfs": {"date": "Date", "amount": "Amount", "notes": "Notes", "required_fields": ["pacf_id", "employee_id"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Single Member", "benefit_coverage_name": "Dependent Name", "benefit_coverages": "Benefit Dependents", "eligible_coverages_only": "Eligible Dependents Only", "employee_name": "Member Name", "relationship": "Relationship", "benefit_coverage_ssn": "Dependent SSN#", "benefit_coverage_birthday": "Birthday", "birthday": "DOB", "lodi": "IOD", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "show_dependents": "Show Dependents", "delegates": "Delegates", "meetings": "Meetings", "employment_statuses_from": "Status From", "employment_statuses_to": "Status To", "dependent_count": "Dependent Count", "beneficiary": "Beneficiary", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "show_coverages": "Show Dependents", "app_downloaded": "App Downloaded", "benefit_coverages_options": [{"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}]}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "push": "false", "push_message": "Push Message", "sms_no_reply_text": "Please note, this is a no-reply text.", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "sendgrid_templates": true, "change_request_notification": true}, "common_terms": {"vacation": "Vacation", "personal": "Personal", "comp_time": "Comp Time"}, "devices": {"key_name": "Push notification"}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}}, "ui": {"notes_timestamps": true, "notes_disabled_fields": ["notes", "description", "recommended_notes"], "employees": {"key_name": "Member List", "is_search": ["employees"], "table_headers": ["name", "employment_status_name", "shield_number", "personal_phone", "birthday"], "tabs": ["profile", "benefits", "uploads", "employee_analytics", "awards", "discipline_settings", "grievances", "meeting_types", "pacfs", "firearm_statuses", "legislative_addresses"], "profile": {"key_name": "Profile", "multiple_notes": true, "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "employees": ["name", "address", "precinct", "birthday", "age", "social_security_number", "genders", "marital_statuses", "marital_status_date", "veteran_status", "shield_number", "a_number", "payroll_id", "previous_shield_number", "title_code", "primary_work_location", "placard_number", "rdo", "start_date", "notes"], "others": ["employee_offices", "employee_employment_statuses", "employee_positions", "delegate_assignments", "employees.janus_card", "employees.janus_card_opt_out_date", "employees.app_downloaded"], "contacts": [{"contact_number": ["personal_phone", "home_phone", "work_phone"]}, {"email_address": ["personal_email"]}], "login_credentials": ["username", "send_credentials", "enable_mobile_access"]}}, "employee_grievances": {"key_name": "Grievances", "table_headers": ["date", "description", "files"]}, "settings": {"key_name": "Settings", "tabs": ["genders", "marital_statuses", "offices", "employment_statuses", "positions", "firearm_statuses", "discipline_settings", "grievances", "meeting_types", "benefits", "pacfs", "payment_types"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "lodi", "disciplines", "grievances", "employee_delegate_assignment", "union_meetings", "benefits", "benefit_coverages", "janus", "beneficiary"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["employment_statuses", "marital_statuses"], ["reports.employment_statuses_from", "reports.employment_statuses_to"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.suffix", "offices", "employment_statuses", "employees.email", "marital_statuses", "positions", "employees.shield_number", "employees.social_security_number", "employees.a_number", "employees.payroll_id", "employees.placard_number", "employees.birthday", "employees.home_phone", "employees.cellphone", "employees.work_phone", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "genders", "pacfs", "reports.dependent_count", "reports.congress_district_id", "reports.assembly_district_id", "reports.senate_district_id", "reports.council_district_id", "employees.app_downloaded"], "default_columns": ["employees"], "actions": ["mailing_label", "excel_report", "pdf_report"]}, "lodi": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "employee_delegate_assignment": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["pdf_report"]}, "union_meetings": {"primary_filters": [["meeting_types"], ["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "benefits": {"primary_filters": [["benefits"], ["reports.active_date", "reports.inactive_date"], ["payment_types", "reports.disbursement_year"], ["reports.payment_date_from", "reports.payment_date_to"], ["reports.show_disbursements", "reports.show_dependents"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["pdf_report"]}, "benefit_coverages": {"primary_filters": [["employees"], ["benefits"], ["reports.show_coverages"], ["reports.eligible_coverages_only"]], "secondary_filters": [["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "janus": {"primary_filters": [["employees.janus_card"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report", "pdf_report"]}, "beneficiary": {"primary_filters": [], "secondary_filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["reports.congress_district_id", "reports.assembly_district_id"], ["reports.senate_district_id", "reports.council_district_id"], ["employees.app_downloaded", ""]], "actions": ["excel_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["firearm_statuses", "positions"], ["employees.shield_number", "employees.a_number"], ["employees.payroll_id", "employees.placard_number"], ["employees.birthday_from", "employees.birthday_to"], ["employees.start_date_from", "employees.start_date_to"], ["employees.home_phone", "employees.cellphone"], ["employees.work_phone", "employees.city"], ["employees.state", "employees.zipcode"], ["notifications.congress_district_id", "notifications.assembly_district_id"], ["notifications.senate_district_id", "notifications.council_district_id"], ["employees.app_downloaded", ""]]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}}, "mobile": {"employees": {"key_name": "Profile", "tabs": ["profile", "contacts", "employee_employment_statuses"], "profile": {"key_name": "General Info", "request_type": "employee", "widget_type": "Info", "api": "employees/profile", "actions": ["view", "edit"], "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "required": true, "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "suffix": {"type": "SingleLineText", "name": "Suffix", "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "required": true, "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apartment", "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "required": true, "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "required": true, "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "ZipCode", "required": true, "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "Date of Birth", "required": true, "actions": ["view", "edit"]}, "age": {"type": "Label", "name": "Age", "actions": ["view"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "hint": {"caption": "PLEASE NOTE*", "caption_color": "#d93025", "text": "For a change in Marital Status to be valid and finalized please upload a marriage certificate."}, "actions": ["view", "edit"]}, "shield_number": {"type": "SingleLineText", "name": "Shield Number", "actions": ["view", "edit"]}, "a_number": {"type": "SingleLineText", "name": "United Healthcare ID #", "actions": ["view", "edit"]}, "payroll_id": {"type": "SingleLineText", "name": "Araya #", "actions": ["view", "edit"]}, "placard_number": {"type": "SingleLineText", "name": "BSC ID", "actions": ["view", "edit"]}, "rdo": {"type": "SingleLineText", "name": "Tax ID", "actions": ["view", "edit"]}, "title_code": {"type": "SingleLineText", "name": "Employee ID", "actions": ["view", "edit"]}, "marital_status_date": {"type": "Date", "name": "Marital Status Date", "actions": ["view", "edit"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"personal_phone": {"type": "PhoneNumber", "name": "Mobile", "contact_for": "personal", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "home_phone": {"type": "PhoneNumber", "name": "Home", "contact_for": "home", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}, "work_phone": {"type": "PhoneNumber", "name": "Work", "contact_for": "work", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"personal_email": {"type": "Email", "name": "Personal", "contact_for": "personal", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}}, "employee_employment_statuses": {"key_name": "Status", "request_type": "employee_employment_status", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_employment_statuses?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"employment_status_name": {"type": "DropDown", "name": "Status", "api": "employment_statuses", "api_key": "employment_status_id", "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}}, "benefits": {"key_name": "Benefits", "tabs": ["employee_benefits", "beneficiaries"], "employee_benefits": {"key_name": "Benefits", "request_type": "employee_benefit", "empty_message": "There are no Benefits posted.", "actions": ["view"], "api": "employee_benefits?employee_id=[EMPLOYEE_ID]", "widget_type": "Action", "attributes": {"name": {"type": "SingleLineText", "name": "Type", "actions": ["view"]}, "action_items": [{"benefit_coverages": {"key_name": "Coverages", "request_type": "benefit_coverage", "required_key": "employee_benefit_id", "empty_message": "There aren't any Benefit Coverages.", "actions": ["view", "new", "edit"], "api": "benefit_coverages?employee_id=[EMPLOYEE_ID]&employee_benefit_id=[EMPLOYEE_BENEFIT_ID]", "widget_type": "Analysis", "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "suffix": {"type": "SingleLineText", "name": "Suffix", "actions": ["view", "edit"]}, "address": {"type": "SingleLineText", "name": "Address", "required": true, "actions": ["view", "edit"]}, "social_security_number": {"type": "Number", "name": "SS #", "required": true, "actions": ["view", "edit"]}, "relationship": {"type": "DropDown", "name": "Relationship", "required": true, "value": {"spouse": "Spouse", "child": "Child", "disabled_child": "Disabled Child", "step_child": "Step Child", "disabled_step_child": "Disabled <PERSON> Child"}, "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "required": true, "actions": ["view", "edit"]}, "age": {"type": "Label", "name": "Age", "actions": ["view"]}, "expires_at": {"type": "Disabled", "name": "Expires At", "actions": ["view"]}}}}]}}, "beneficiaries": {"key_name": "Beneficiaries", "request_type": "beneficiary", "required_key": "beneficiary_type", "empty_message": "There are no Beneficiaries posted.", "actions": ["view", "new", "edit"], "api": "beneficiaries?employee_id=[EMPLOYEE_ID]", "widget_type": "Details", "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "For a change in Beneficiary to be valid and finalized please upload a beneficiary card.", "show_links_in_add": true, "links": {"Ameritas Beneficiary Form": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/btoba/Ameritas+Beneficiary+Form.pdf", "Symetra Beneficiary Form": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/btoba/Symetra+Beneficiary+Form.pdf"}}, "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view", "edit"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "actions": ["view", "edit"]}, "address": {"type": "SingleLineText", "name": "Address", "actions": ["view", "edit"]}, "birthday": {"type": "Date", "name": "DOB", "actions": ["view", "edit"]}, "phone": {"type": "PhoneNumber", "name": "Phone Number", "actions": ["view", "edit"]}, "beneficiary_type": {"type": "DropDown", "name": "Type", "required": true, "value": {"Primary": "Primary", "Contingent": "Contingent"}, "actions": ["view", "edit"]}, "percentage": {"type": "Number", "name": "Percentage", "required": true, "actions": ["view", "edit"]}, "file": {"type": "FileField", "name": "Upload", "selection_type": "single", "max_file_size": 10, "total_files": 1, "actions": ["view", "edit"]}}}}, "uploads": {"key_name": "Uploads", "empty_message": "No Uploads", "actions": ["view", "new"], "api": "uploads?employee_id=[EMPLOYEE_ID]", "widget_type": "FileData", "attributes": {"file": {"type": "FileField", "name": "File", "selection_type": "single", "max_file_size": 10, "total_files": 1, "required": true, "actions": ["view", "edit"]}, "date": {"type": "FromAPI", "name": "Date", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "notification": {"key_name": "Notifications", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "to": "<EMAIL>", "subject": "BTOBA: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "api": "employees/update_password"}}}