{"schema": {"change_requests": {"key_name": "Change Requests", "employee_name": "Member", "request_type": "Request Type", "status": "Status", "is_handling_name_from_json": true}, "contact_persons": {"key_name": "Contact List", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city"]}}, "legislative_contacts": {"key_name": "Legislative Contacts", "avatar": "Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "primary_work_location": "Company", "notes": "Notes", "cellphone": "Cell phone", "home_phone": "Home phone", "work_phone": "Work phone", "email": "Email", "rdo": "Payee", "required_fields": ["name", "first_name", "last_name", "address", "city", "state", "street", "zipcode"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "street", "city"]}}, "employees": {"key_name": "Members", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip", "precinct": "Precinct Details", "birthday": "DOB", "age": "Age", "do_not_mail": "Do Not Mail", "title_code": "Spouse Name", "shield_number": "Shield #", "a_number": "Reference #", "social_security_number": "SSN", "responder_911": "Plaque Ordered", "start_date": "Date of Appointment", "previous_shield_number": "Pension #", "veteran_status": "Veteran Status", "notes": "Notes", "cellphone": "Alternate Cell", "email": "Alternate Email", "work_email": "Personal Email", "work_phone": "Cell", "social_security_number_format": "9", "janus_card": "<PERSON><PERSON>", "janus_card_opt_out_date": "<PERSON><PERSON>t Out Date", "allow_multiple_delegate_assignments": "true", "username": "Username", "grievance_case_auto_generation": true, "register_vote": "Registered to Vote", "visible_social_security_number": true, "safeguard_notes_alert": true, "ssn_unique_search": true, "notes_search_filter": true, "app_downloaded": "App Downloaded", "personal_emergency": "Personal Emergency", "colleague_emergency": "Colleague Emergency", "sendgrid_templates": true, "required_fields_in_others": ["employment_statuses", "offices"], "required_fields": ["name", "first_name", "last_name", "social_security_number", "gender_id", "start_date", "address", "employee_employment_statuses", "employee_offices"], "unique_fields": ["username", "social_security_number"], "permitted_editable_fields": {"write_placard_only": ["responder_911", "notes"]}, "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "shield_number", "a_number", "street", "city", "state", "zipcode"], "associated_model": {"contacts": ["value"], "benefit_coverages": ["first_name", "last_name", "suffix"]}}, "exact_search_fields": ["shield_number"], "login_credentials": {"key_name": "Login Information", "username": "Username", "send_credentials": "Update & Send Credentials", "enable_mobile_access": "Allow Access to Mobile app", "sent_credentials_to_work_email": true, "required_fields": []}}, "notes": {"notes": "Notes"}, "contacts": {"contact_number": {"key_name": "Contact Number", "work_phone": "Cell", "required_fields": []}, "email_address": {"key_name": "Email address", "work_email": "Personal", "required_fields": []}, "emergency_contacts": {"key_name": "Emergency Contact", "personal_emergency": "Personal", "colleague_emergency": "Colleague", "required_fields": []}}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"], "revoke_app_access": ["resigned", "deceased", "terminated", "transferred"]}, "employment_statuses": {"key_name": "Union Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "firearm_statuses": {"key_name": "Firearm Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "affiliations": {"key_name": "Tier", "name": "Name", "description": "Description", "required_fields": ["name"]}, "tour_of_duties": {"key_name": "Political Party", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_firearm_statuses": {"status_date": "Date", "firearm_type": "Type", "notes": "Notes", "required_fields": ["firearm_status_id", "employee_id"], "customization": true, "required_tables": ["firearm_statuses"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "position_id"]}, "ranks": {"key_name": "Ranks", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id", "start_date"]}, "positions": {"key_name": "Union Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "genders": {"key_name": "Genders", "name": "Name", "description": "Description", "required_fields": ["name"]}, "type_of_incidents": {"key_name": "Type of Incident", "name": "Name", "description": "Description", "required_fields": ["name"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "restrict_login_out_of_office": "Login Restrictions", "restrict_type": "Type", "allow_login_from_time": "Allow From Time", "allow_login_to_time": "Allow To Time", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"], "restrict_type_values": [{"value": "Allow Access for all days", "key": "allow_login_in_weekends"}, {"value": "Allow Access in weekdays only", "key": "login_only_in_weekdays"}]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "notes": "Notes", "required_fields": ["name"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "serviced_expiration": "Serviced Expiration", "description": "Description", "benefit_customization": true, "auto_expire_benefits": true, "automate_benefits_creation": true, "employee_status": {"section_III": ["Deceased", "Terminated", "Transferred", "Resigned"]}, "required_fields": ["benefit_id", "employee_id"]}, "peshes": {"key_name": "PESH", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "date": "Date", "remarks": "Remarks", "office_id": "Command", "files": "Files", "required_fields": [""], "custom_validations": {"minimum_one_required_fields": ["office_id", "complaint", "date", "remarks"]}}, "beneficiaries": {"key_name": "Beneficiaries", "name": "Name", "gender_id": "Gender", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "phone": "Phone Number", "beneficiary_type": "Type", "percentage": "Percentage", "notify_before_beneficiary_change": true, "file": "Upload", "required_fields": ["name", "employee_id", "percentage"], "required_auto_fields": ["address"]}, "benefit_coverages": {"key_name": "Benefit Dependents", "first_name": "First Name", "last_name": "Last Name", "suffix": "Suffix", "gender_id": "Gender", "relationship": "Relationship", "dependent": "Dependent", "birthday": "DOB", "age": "Age", "effective_date": "Effective Date", "expires_at": "Expiration", "student": "Student", "school_status": "School Status", "add_dependent_to_all_benefits": "Revise to all benefits", "dependent_all_benefits_expiration": true, "child_auto_dependent_code": true, "disabled_child_edit": true, "update_student_after_expiration": true, "coverage_expire_age": 26, "retire_benefit_coverage_expire_age": 19, "required_fields": ["employee_id", "employee_benefit_id", "birthday", "first_name", "last_name", "relationship"], "relationship_value": [{"value": "Member", "key": "member"}, {"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Disabled Child", "key": "disabled_child"}, {"value": "Domestic Partner", "key": "domestic_partner"}], "student_options": [{"value": "Yes", "key": true}, {"value": "No", "key": false}], "school_status_options": [{"value": "Spring", "key": "spring"}, {"value": "Fall", "key": "fall"}]}, "benefit_disbursements": {"key_name": "<PERSON><PERSON><PERSON>", "date": "Claims Service Date", "entry_date": "Entry Date", "relationship": "Relationship", "benefit_coverage_id": "Person Serviced", "notes": "Notes", "order_by_date": true, "calendar_year_expiration": true, "optical_coverage_expiration": true, "required_fields": ["employee_id", "employee_benefit_id"], "custom_validations": {"minimum_one_required_fields": ["date", "entry_date", "relationship", "benefit_coverage_id", "reference_number", "amount", "notes"]}, "relationship_value": [{"value": "Self", "key": "self"}, {"value": "Spouse", "key": "spouse"}, {"value": "Child", "key": "child"}, {"value": "Domestic Partner", "key": "domestic_partner"}, {"value": "Divorced", "key": "divorced"}, {"value": "Family Depend", "key": "family_depend"}, {"value": "Disabled De<PERSON><PERSON>", "key": "disabled_dependent"}, {"value": "Guardian", "key": "guardian"}]}, "members": {"key_name": "Members"}, "additional_notes": {"key_name": "Notes"}, "units": {"key_name": "Title", "name": "Name", "description": "Description", "required_fields": ["name"]}, "delegate_assignments": {"key_name": "Delegate Assignments", "delegate_employee_id": "Delegate Name", "start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["delegate_employee_id", "office_id", "employee_id", "start_date"]}, "delegate_employees": {"key_name": "Delegate Name"}, "discipline_settings": {"key_name": "OATH", "name": "Name", "description": "Description", "required_fields": ["name"]}, "discipline_charges": {"key_name": "Violation Type", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_discipline_settings": {"discipline_setting_id": "Penalty Imposed", "discipline_charge_id": "Violation Type", "charge": "Charge", "dan_number": "Command Discipline #", "date": "Date", "recommended_penalty": "Disposition", "description": "Remarks", "files": "Files", "required_fields": ["employee_id", "discipline_setting_id"]}, "employee_discipline_steps": {"date": "Date", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_discipline_setting_id"]}, "grievances": {"key_name": "Grievances", "name": "Name", "description": "Description", "required_fields": ["name"]}, "grievance_statuses": {"key_name": "Grievance Statuses", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_grievances": {"key_name": "Class Action Grievance", "charge": "<PERSON><PERSON><PERSON><PERSON>", "number": "Case Number", "date": "Date", "filed_olr": "Filed OLR", "description": "Remarks", "files": "Uploads", "required_fields": ["grievance_id"], "unique_fields": ["number"]}, "employee_grievance_steps": {"date": "Date", "grievance_status_id": "Status", "recommended_notes": "Remarks", "is_settled": "Settled", "is_pending": "Pending", "olr": "OLR #", "arbitration": "Arbitration #", "win": "Win", "loss": "Loss", "files": "Uploads", "required_fields": ["step", "employee_grievance_id"]}, "awards": {"key_name": "Awards", "name": "Type", "awarded_on": "Date", "description": "Description", "required_fields": ["employee_id", "name"]}, "uploads": {"key_name": "Uploads", "file": "File", "notes": "Notes", "required_fields": ["file", "employee_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Single Member", "lodi": "<PERSON><PERSON>", "benefit_coverages": "Benefit Dependents", "disciplines": "OATH", "grievances": "Grievances", "union_meetings": "Union Meeting", "employee_delegate_assignment": "Member Delegate Assignment", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "delegates": "Delegates", "meetings": "Meetings", "case_and_abeyance": "Case and Abeyance", "abandonment_hearing": "Abandonment Hearing", "was_employee_pds": "Was Employee PDS", "ta_implemented": "TA implemented", "settled_steps": "Settled Steps", "pending_steps": "Pending Steps", "win": "Win", "loss": "Loss", "charge": "Charge", "dan_number": "Command Discipline #", "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "show_coverages": "Show Dependents", "app_downloaded": "App Downloaded", "employment_statuses_start_date": "Union Status Start Date", "employment_statuses_start_date_from": "Union Status Start From", "employment_statuses_start_date_to": "Union Status Start To", "employment_statuses_end_date_from": "Union Status End From", "employment_statuses_end_date_to": "Union Status End To", "disable_benefits_on_group_by_all_benefits": true, "benefit_coverages_options": [{"value": "Members with dependents (Grouped by All Benefits)", "key": "grouped"}, {"value": "All members with/without dependents", "key": "all"}, {"value": "Members with dependents", "key": "true"}, {"value": "Members without dependents", "key": "false"}], "step_values": [{"value": "Step I", "key": "step_1"}, {"value": "Step II", "key": "step_2"}, {"value": "Arbritration", "key": "arbritration"}], "member_count": "Member List"}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "push": "false", "push_message": "Push Message", "sms": "false", "email": "false", "filter": "Filter", "sms_attachments": "SMS attachments", "sms_no_reply_text": "Please note, this is a no-reply text.", "change_request_notification": true, "sendgrid_templates": true, "congress_district_id": "Congress District Name", "assembly_district_id": "Assembly District Name", "senate_district_id": "Senate District Name", "council_district_id": "Council District Name", "employment_statuses_start_date_from": "Union Status Start From", "employment_statuses_start_date_to": "Union Status Start To", "employment_statuses_end_date_from": "Union Status End From", "employment_statuses_end_date_to": "Union Status End To", "sms_to_value": [{"key": "work", "value": "Personal"}, {"key": "personal", "value": "Alternate"}, {"key": "all", "value": "Both"}]}, "employee_analytics": {"key_name": "Analytics", "customization": true, "dashboard_stats": true, "required_tables": ["lodis", "assaults"], "injury_value": [{"value": "MM1", "key": "MM1"}, {"value": "MM2", "key": "MM2"}, {"value": "MM3", "key": "MM3"}, {"value": "Full Duty", "key": "Full Duty"}]}, "lodis": {"key_name": "LODI", "incident_date": "Date of incident", "injury": "LODI Status", "return_date": "Date of return", "department_name": "Location", "notes": "Notes", "files": "Files", "required_fields": ["department_id", "employee_id", "incident_date"]}, "assaults": {"key_name": "ASSAULTS", "cod_report_date": "COD Report Date", "date": "Date of incident", "time": "Time", "cod": "COD #", "office_id": "Command", "inmate_name": "Inmate Name", "bookcase": "Bookcase #", "nysid": "NYSID #", "physical": "Physical Assault", "verbal": "Verbal Assault", "type_of_incident_id": "Type of Incident", "description": "Description of Incident", "incident_reported_to": "Who was the incident reported to?", "was_inmate_rearrested": "Was the Inmate Rearrested", "charges": "Charges", "suspension": "Suspension", "suspension_days_count": "How many days of suspension?", "who_affected_suspension": "Who affected the suspension", "files": "Upload", "required_fields": ["employee_id", "date"], "custom_validations": {"minimum_one_required_fields": ["date", "time", "incident_reported_to"]}}, "witnesses": {"name": "Name of Witness", "phone": "Phone number of Witness", "address": "Address of Witness", "required_fields": ["name"]}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id", "start_date"]}, "pacfs": {"key_name": "Annuity", "name": "Name", "description": "Description", "hide_status": true, "required_fields": ["name"]}, "payment_types": {"key_name": "Tax Code", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_pacfs": {"payment_type_id": "Tax Code", "date": "Check Date", "check_no": "Check #", "check_no_add": "Check #", "check_valid": "Check #", "check_void": "Check # Void", "amount": "Net Amount", "gross_amount": "Gross Amount", "tax_amount": "Tax Amount", "payee": "Payee", "payee_ssn": "Payee SSN#", "received_application": "Received Application", "notes": "Notes", "required_fields": ["employee_id"], "pacf_percentage_value": [{"value": 10, "key": 10}, {"value": 90, "key": 90}, {"value": 100, "key": 100}, {"value": "MD", "key": "MD"}], "custom_validations": {"minimum_one_required_fields": ["payment_type_id", "date", "check_no", "check_valid", "check_no_add", "check_void", "amount", "gross_amount", "tax_amount", "payee", "payee_ssn", "percentage", "received_application", "notes"]}}, "legislative_addresses": {"key_name": "Legislation", "legislation_details": "Legislation"}, "employee_meeting_types": {"meeting_date": "Date", "attended": "Attended", "notes": "Notes", "required_fields": ["meeting_date", "meeting_type_id", "employee_id"]}, "meeting_types": {"key_name": "Meetings", "name": "Name", "description": "Description", "required_fields": ["name"]}, "common_terms": {"step_1": "STEP I", "step_2": "STEP II", "step_3": "STEP III", "arbritration": "ARBRITRATION", "none": "None", "basic": "Basic", "premium": "Premium", "supplemental": "Supplemental", "life_insurances": "Life Insurances", "dependents": "Dependents", "medscope": "Medscope", "hearing": "Hearing 207c", "arbitration": "Arbitration", "employee_analytics": "Analytics", "sick_bank": "Sick Bank", "notes_section": "Notes"}, "departments": {"key_name": "Mailing List Category", "name": "Name", "notes": "Notes", "required_fields": ["name"]}, "employee_departments": {"key_name": "Mailing List Category", "required_fields": ["department_id", "employee_id"]}, "facilities": {"key_name": "Payee", "name": "Name", "notes": "Notes", "required_fields": ["name"]}, "employee_facilities": {"key_name": "Payee", "required_fields": ["facility_id", "employee_id"]}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}, "maillogs": {"key_name": "Maillogs", "date_received": "Date Received", "received_from": "Received From", "entered_by": "Entered By", "going_to": "Going to", "maillog_type": "Type of mail", "no_of_items": "# of items", "type_options": ["Certified Mail", "Return Receipt", "Letter", "Package"], "required_fields": ["date_received"]}, "devices": {"key_name": "Push notification"}, "forms": {"key_name": "Forms", "file": "Form", "name": "Form Name", "heading": "Heading", "number": "Number", "link": "Link", "add_tel_prefix_in_form": true, "order_by_coba_website": true}}, "ui": {"notes_timestamps": true, "notes_disabled_fields": ["notes", "description", "recommended_notes"], "user_audit": {"key_name": "User <PERSON>t", "show_shield_and_ref_no": true, "table_headers": ["user_name", "title", "employee_name", "created_at"]}, "employees": {"key_name": "Member List", "has_exact_search": true, "ssn_unique_search_label": "Search SSN # / Shield #", "hide_address_except_account_admins": true, "is_search": ["employees"], "is_filter": ["employees", "grievance", "ssn", "birthday", "contact_search"], "show_union_status_top": true, "status_only_for_admin": true, "employment_statuses_count": ["ACTIVE", "COBRA", "Retired", "DISABILITY RETIRED"], "dependents_count": ["Active Member Dependents", "Retired Dependents", "Active Member Spouse", "Active Member Domestic Partner", "Active Member Child Dependents", "Students", "Active Member Students", "Retired Member Students", "Disability Retired Member Students"], "table_headers": ["name", "social_security_number", "a_number", "shield_number", "employment_status_name"], "tabs": ["profile", "benefits", "pacfs", "employee_analytics", "grievances", "discipline_settings", "peshes", "firearm_statuses", "legislative_addresses", "meeting_types", "awards", "uploads", "notes_section"], "profile": {"key_name": "Profile", "multiple_notes": true, "print_profile": true, "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "is_mail_alert": true, "employees": ["name", "social_security_number", "address", "precinct", "a_number", "start_date", "shield_number", "birthday", "do_not_mail", "genders", "age", "marital_statuses", "affiliations", "register_vote", "tour_of_duties", "title_code", "responder_911", "previous_shield_number", "veteran_status", "notes"], "contacts": [{"contact_number": ["work_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["work_email"]}], "others": ["employee_employment_statuses", "employee_offices", "employee_positions", "employee_ranks", "delegate_assignments", "employees.janus_card", "employees.janus_card_opt_out_date", "employees.app_downloaded"], "login_credentials": ["username", "send_credentials", "enable_mobile_access"]}, "discipline_settings": {"tabs": ["discipline_settings"], "table_headers": ["dan_number", "discipline_setting", "charge", "date"]}, "benefits": {"multiple_notes": true, "employee_status": true, "today_entry_date": true, "ignoreBenefitTypes": ["ALUMINUM", "APPROVAL", "CATHERINE", "CLAIMS", "CLAIM", "DUP", "ESTIMATE", "FDNY/EMS", "HEALTH", "NANCY", "NO #", "PART OFNUR", "PRIPHY", "PROTH", "PROTHY", "RCT", "REIM OPT", "REISE", "RE-TREAT.", "SPOOUSE", "SPOUSE", "STOCKING", "TERREL", "TOTAL TO", "WILLIAM", "<PERSON>ph", "Prophy"]}, "grievances": {"tabs": ["grievances", "step_1", "step_2", "step_3", "arbritration"], "table_headers": ["grievance", "charge", "number", "date"]}, "assaults": {"table_headers": ["cod_report_date", "date", "time", "cod", "office_id", "inmate_name", "bookcase", "nysid"]}}, "employee_grievances": {"key_name": "Class Action Grievance", "table_headers": ["charge", "number", "date", "filed_olr", "description", "files"]}, "peshes": {"key_name": "Class Action PESH", "table_headers": ["complaint", "date", "remarks", "office_id", "files"]}, "contact_persons": {"key_name": "Contact List", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location", "units", "departments"], "employees": ["avatar", "name", "address", "primary_work_location", "units", "employee_departments", "notes"], "contacts": [{"contact_number": ["work_phone"]}, {"email_address": ["work_email"]}]}, "legislative_contacts": {"key_name": "Legislative Contacts", "avatar_expand_view": true, "is_search": true, "additional_details": ["first_name", "middle_name", "last_name"], "table_headers": ["name", "address", "primary_work_location", "units", "employee_departments"], "employees": ["avatar", "name", "address", "primary_work_location", "units", "employee_departments", "employee_facilities", "notes"], "contacts": [{"contact_number": ["work_phone", "personal_phone", "home_phone"]}, {"email_address": ["work_email", "personal_email"]}]}, "settings": {"key_name": "Settings", "tabs": ["genders", "benefits", "marital_statuses", "employment_statuses", "units", "positions", "discipline_settings", "grievances", "delegate_assignments", "meeting_types", "firearm_statuses", "departments", "facilities", "offices", "affiliations", "tour_of_duties", "ranks", "payment_types", "discipline_charges", "grievance_statuses", "type_of_incidents"]}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Reports", "tabs": ["single_employee", "disciplines", "grievances", "union_meetings", "employee_delegate_assignment", "lodi", "benefit_coverages", "member_count"], "member_count": {"secondary_filters": [], "actions": ["excel_report"]}, "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["contact_persons", "employment_statuses"], ["reports.employment_statuses_start_date_from", "reports.employment_statuses_start_date_to"], ["reports.employment_statuses_end_date_from", "reports.employment_statuses_end_date_to"], ["employees.shield_number", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.a_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.work_email", "employees.work_phone"], ["employees.email", "employees.cellphone"], ["offices", "firearm_statuses"], ["genders", "positions"], ["units", "affiliations"], ["tour_of_duties", "departments"], ["ranks", "employees.responder_911"], ["marital_statuses", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "columns": ["employees", "employment_statuses", "reports.employment_statuses_start_date", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.social_security_number", "employees.a_number", "employees.shield_number", "employees.birthday", "employees.cellphone", "employees.email", "employees.work_phone", "employees.work_email", "employees.start_date", "employees.title_code", "employees.shield_number", "employees.street", "employees.apartment", "employees.city", "employees.state", "employees.zipcode", "employees.birthday", "marital_statuses", "positions", "genders", "affiliations", "tour_of_duties", "departments", "ranks", "offices", "reports.congress_district_id", "reports.assembly_district_id", "reports.senate_district_id", "reports.council_district_id", "employees.app_downloaded"], "default_columns": ["employees"], "actions": ["mailing_label", "multiple_mailing_label", "excel_report", "pdf_report"]}, "benefit_coverages": {"primary_filters": [["reports.columns"], ["employees"], ["benefits"], ["reports.show_coverages"]], "secondary_filters": [["contact_persons", "employment_statuses"], ["reports.employment_statuses_start_date_from", "reports.employment_statuses_start_date_to"], ["reports.employment_statuses_end_date_from", "reports.employment_statuses_end_date_to"], ["employees.shield_number", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.a_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.work_email", "employees.work_phone"], ["employees.email", "employees.cellphone"], ["offices", "firearm_statuses"], ["genders", "positions"], ["units", "affiliations"], ["tour_of_duties", "departments"], ["ranks", "employees.responder_911"], ["marital_statuses", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "columns": ["employees.first_name", "employees.middle_name", "employees.last_name", "employees.social_security_number", "employees.a_number", "employees.birthday", "employees.start_date", "employees.street", "employees.apartment", "employees.city", "employees.state", "employees.zipcode", "genders", "employees.title_code", "marital_statuses", "affiliations", "tour_of_duties"], "actions": ["excel_report", "pdf_report"]}, "disciplines": {"primary_filters": [["discipline_settings", "discipline_charges"], ["reports.charge", "reports.dan_number"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["contact_persons", "employment_statuses"], ["reports.employment_statuses_start_date_from", "reports.employment_statuses_start_date_to"], ["reports.employment_statuses_end_date_from", "reports.employment_statuses_end_date_to"], ["employees.shield_number", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.a_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.work_email", "employees.work_phone"], ["employees.email", "employees.cellphone"], ["offices", "firearm_statuses"], ["genders", "positions"], ["units", "affiliations"], ["tour_of_duties", "departments"], ["ranks", "employees.responder_911"], ["marital_statuses", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "report_columns": ["dan_number", "charge"], "actions": ["excel_report", "pdf_report"]}, "grievances": {"primary_filters": [["grievances"], ["reports.started_at", "reports.ended_at"], ["reports.settled_steps"]], "secondary_filters": [["employees"], ["contact_persons", "employment_statuses"], ["reports.employment_statuses_start_date_from", "reports.employment_statuses_start_date_to"], ["reports.employment_statuses_end_date_from", "reports.employment_statuses_end_date_to"], ["employees.shield_number", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.a_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.work_email", "employees.work_phone"], ["employees.email", "employees.cellphone"], ["offices", "firearm_statuses"], ["genders", "positions"], ["units", "affiliations"], ["tour_of_duties", "departments"], ["ranks", "employees.responder_911"], ["marital_statuses", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "report_columns": ["number"], "actions": ["excel_report", "pdf_report"], "steps": ["step_1", "step_2", "arbritration"]}, "union_meetings": {"primary_filters": [["reports.started_at", "reports.ended_at"], ["reports.delegates"], ["reports.meetings"]], "secondary_filters": [["employees"], ["contact_persons", "employment_statuses"], ["reports.employment_statuses_start_date_from", "reports.employment_statuses_start_date_to"], ["reports.employment_statuses_end_date_from", "reports.employment_statuses_end_date_to"], ["employees.shield_number", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.a_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.work_email", "employees.work_phone"], ["employees.email", "employees.cellphone"], ["offices", "firearm_statuses"], ["genders", "positions"], ["units", "affiliations"], ["tour_of_duties", "departments"], ["ranks", "employees.responder_911"], ["marital_statuses", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "actions": ["excel_report", "pdf_report"]}, "employee_delegate_assignment": {"primary_filters": [["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["contact_persons", "employment_statuses"], ["reports.employment_statuses_start_date_from", "reports.employment_statuses_start_date_to"], ["reports.employment_statuses_end_date_from", "reports.employment_statuses_end_date_to"], ["employees.shield_number", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.a_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.work_email", "employees.work_phone"], ["employees.email", "employees.cellphone"], ["offices", "firearm_statuses"], ["genders", "positions"], ["units", "affiliations"], ["tour_of_duties", "departments"], ["ranks", "employees.responder_911"], ["marital_statuses", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "actions": ["pdf_report"]}, "lodi": {"primary_filters": [["lodis"], ["reports.started_at", "reports.ended_at"]], "secondary_filters": [["employees"], ["contact_persons", "employment_statuses"], ["reports.employment_statuses_start_date_from", "reports.employment_statuses_start_date_to"], ["reports.employment_statuses_end_date_from", "reports.employment_statuses_end_date_to"], ["employees.shield_number", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.a_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.work_email", "employees.work_phone"], ["employees.email", "employees.cellphone"], ["offices", "firearm_statuses"], ["genders", "positions"], ["units", "affiliations"], ["tour_of_duties", "departments"], ["ranks", "employees.responder_911"], ["marital_statuses", "reports.congress_district_id"], ["reports.assembly_district_id", "reports.senate_district_id"], ["reports.council_district_id", "employees.app_downloaded"]], "report_columns": ["injury"], "actions": ["excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "default_notification_receivers": ["<PERSON>."], "alternative_email": true, "is_notification_sms_to": true, "filters": [["employees"], ["contact_persons", "employment_statuses"], ["notifications.employment_statuses_start_date_from", "notifications.employment_statuses_start_date_to"], ["notifications.employment_statuses_end_date_from", "notifications.employment_statuses_end_date_to"], ["employees.shield_number", "employees.social_security_number"], ["employees.start_date_from", "employees.start_date_to"], ["employees.birthday_from", "employees.birthday_to"], ["employees.a_number", "employees.city"], ["employees.state", "employees.zipcode"], ["employees.work_email", "employees.work_phone"], ["employees.email", "employees.cellphone"], ["offices", "firearm_statuses"], ["genders", "positions"], ["units", "affiliations"], ["tour_of_duties", "departments"], ["ranks", "employees.responder_911"], ["marital_statuses", "notifications.congress_district_id"], ["notifications.assembly_district_id", "notifications.senate_district_id"], ["notifications.council_district_id", "employees.app_downloaded"]]}, "change_requests": {"key_name": "Change Requests", "employee": "General Info", "contact": "Contact Info", "request_header": "Request Header", "current_value": "Current Status", "requested_changes": "Requested Changes", "status": "Status", "action": "Action", "table_headers": ["request_header", "current_value", "requested_changes", "status", "action"]}, "maillogs": {"key_name": "Maillogs", "no_of_items": 500, "table_headers": ["date_received", "received_from", "entered_by", "going_to", "type"]}}, "mobile": {"social_media_feed": {"key_name": "Social Media Feed", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/social-media.png", "widget_type": "SocialMediaFeed", "web_view_script": "<script src='https://static.elfsight.com/platform/platform.js' async></script> <div class='elfsight-app-46b2b5ce-9ca1-4122-a91d-a74604bd3a94' data-elfsight-app-lazy></div>", "header_text": "FOLLOW US ON SOCIAL MEDIA"}, "employees": {"key_name": "Profile", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/memberprofile.png", "welcome_message": {"header": "WELCOME TO THE COBA APP!", "description": "THIS APP WAS CREATED BY THE EXECUTIVE BOARD TO BRING USEFUL RESOURCES TO OUR MEMBERS. THIS APP WILL ALSO BE USED TO COMMUNICATE WITH MEMBERS, SO <PERSON><PERSON>AS<PERSON> MAKE SURE NOTIFICATIONS ARE ENABLED.", "email": null, "regards": "IN SOLIDARITY,\nBENNY BOSCIO JR.\nCOBA PRESIDENT", "button_name": "CLICK HERE TO ENTER"}, "tabs": ["profile", "contacts", "employee_employment_statuses", "employee_offices", "legislation"], "profile": {"key_name": "General Info", "request_type": "employee", "widget_type": "Info", "api": "employees/profile", "actions": ["view", "edit"], "hint": {"caption": "PLEASE NOTE*", "caption_color": "#d93025", "text": "No change in Marital Status can be finalized until a Marriage Certificate or Divorce Decree is received."}, "attributes": {"first_name": {"type": "SingleLineText", "name": "First Name", "required": true, "actions": ["view", "edit"]}, "middle_name": {"type": "SingleLineText", "name": "Middle Name", "actions": ["view", "edit"]}, "last_name": {"type": "SingleLineText", "name": "Last Name", "required": true, "actions": ["view", "edit"]}, "suffix": {"type": "SingleLineText", "name": "Suffix", "actions": ["view", "edit"]}, "street": {"type": "SingleLineText", "name": "Address", "actions": ["view", "edit"]}, "apartment": {"type": "SingleLineText", "name": "Apartment", "actions": ["view", "edit"]}, "city": {"type": "SingleLineText", "name": "City", "actions": ["view", "edit"]}, "state": {"type": "SingleLineText", "name": "State", "actions": ["view", "edit"]}, "zipcode": {"type": "ZipCode", "name": "ZipCode", "actions": ["view", "edit"]}, "a_number": {"type": "SingleLineText", "name": "Reference #", "required": true, "actions": ["view"]}, "birthday": {"type": "Date", "name": "DOB", "actions": ["view", "edit"]}, "age": {"type": "Label", "name": "Age", "actions": ["view"]}, "marital_status_name": {"type": "DropDown", "name": "Marital Status", "api": "marital_statuses", "api_key": "marital_status_id", "required": true, "actions": ["view", "edit"]}, "tour_of_duty_name": {"type": "DropDown", "name": "Political Party", "api": "tour_of_duties", "api_key": "tour_of_duty_id", "actions": ["view", "edit"]}, "title_code": {"type": "SingleLineText", "name": "Spouse Name", "actions": ["view", "edit"]}, "veteran_status": {"type": "Radio", "name": "Veteran Status", "actions": ["view", "edit"]}, "register_vote": {"type": "Radio", "name": "Registered to Vote", "actions": ["view", "edit"]}}}, "contacts": {"key_name": "Contact Info", "request_type": "contact", "widget_type": "Info", "api": "contacts?employee_id=[EMPLOYEE_ID]", "actions": ["view", "edit"], "sub_sections": ["contact_number", "email_address", "emergency_contacts"], "contact_number": {"key_name": "Contact Number", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=phone", "attributes": {"work_phone": {"type": "PhoneNumber", "name": "Cell Phone", "contact_for": "work", "contact_type": "phone", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "email_address": {"key_name": "Email", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=email", "attributes": {"work_email": {"type": "Email", "name": "Personal Email", "contact_for": "work", "contact_type": "email", "contact_attribute": "value", "actions": ["view", "edit"]}}}, "emergency_contacts": {"key_name": "Emergency Contacts", "api": "contacts?employee_id=[EMPLOYEE_ID]&contact_type=emergency", "attributes": {"personal_emergency_name": {"type": "SingleLineText", "name": "Personal Emergency Contact Name", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_name", "actions": ["view", "edit"]}, "personal_emergency_phone": {"type": "PhoneNumber", "name": "Personal Emergency Contact Phone", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "value", "actions": ["view", "edit"]}, "personal_emergency_relationship": {"type": "SingleLineText", "name": "Personal Emergency Contact Relationship", "contact_for": "personal", "contact_type": "emergency", "contact_attribute": "contact_relationship", "actions": ["view", "edit"]}}}}, "employee_employment_statuses": {"key_name": "Union Status", "request_type": "employee_employment_status", "disclaimer": "Contact the union office at (************* or Contact Us through the app from the Menu Tab titled 'Contact Us' if any of this information is incorrect.", "hyperlink_phone": "(*************", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_employment_statuses?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"employment_status_name": {"type": "DropDown", "name": "Union Status", "api": "employment_statuses", "api_key": "employment_status_id", "actions": ["view"]}, "start_date": {"type": "Date", "name": "Start Date", "actions": ["view"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view"]}}}, "employee_offices": {"key_name": "Command(s)", "request_type": "employee_office", "disclaimer": "Contact the union office at (************* or Contact Us through the app from the Menu Tab titled 'Contact Us' if any of this information is incorrect.", "hyperlink_phone": "(*************", "empty_message": "No Data Available", "actions": ["view"], "api": "employee_offices?employee_id=[EMPLOYEE_ID]", "widget_type": "Status", "attributes": {"office_name": {"type": "DropDown", "name": "Command", "api": "offices", "api_key": "office_id", "required": true, "actions": ["view", "edit"]}, "start_date": {"type": "Date", "name": "Start Date", "required": true, "actions": ["view", "edit"]}, "end_date": {"type": "Date", "name": "End Date", "actions": ["view", "edit"]}}}, "legislation": {"key_name": "Legislation", "widget_type": "legislation", "empty_message": "No Legislative Details Found", "api": "legislative_addresses/employee_legislative_address?employee_id=[EMPLOYEE_ID]", "sub_sections": ["county_details", "congress_member_details", "assembly_member_details", "senate_member_details", "council_member_details", "comptroller_member_details", "executive_member_details", "attorney_member_details"], "county_details": {"key_name": "County Details", "attributes": {"county_name": {"type": "SingleLineText", "name": "County Name", "actions": ["view"]}}}, "congress_member_details": {"key_name": "Congress Member Det<PERSON>", "attributes": {"congress_member_name": {"type": "SingleLineText", "name": "Congress Member Name", "actions": ["view"]}, "congress_district_name": {"type": "SingleLineText", "name": "Congress Member District Name", "actions": ["view"]}, "congress_web_url": {"type": "Url", "name": "Congress Member Web URL", "actions": ["view"]}}}, "assembly_member_details": {"key_name": "Assembly Member Details", "attributes": {"assembly_member_name": {"type": "SingleLineText", "name": "Assembly Member Name", "actions": ["view"]}, "assembly_district_name": {"type": "SingleLineText", "name": "Assembly Member District Name", "actions": ["view"]}, "assembly_web_url": {"type": "Url", "name": "Assembly Member Web URL", "actions": ["view"]}}}, "senate_member_details": {"key_name": "Senate Member <PERSON><PERSON>", "attributes": {"senate_member_name": {"type": "SingleLineText", "name": "Senate Member Name", "actions": ["view"]}, "senate_district_name": {"type": "SingleLineText", "name": "Senate Member District Name", "actions": ["view"]}, "senate_web_url": {"type": "Url", "name": "Senate Member Web URL", "actions": ["view"]}}}, "council_member_details": {"key_name": "Council Member Details", "attributes": {"council_member_name": {"type": "SingleLineText", "name": "Council Member Name", "actions": ["view"]}, "council_district_name": {"type": "SingleLineText", "name": "Council Member District Name", "actions": ["view"]}, "council_web_url": {"type": "Url", "name": "Council Member Web URL", "actions": ["view"]}}}, "comptroller_member_details": {"key_name": "Comptroller Member Details", "attributes": {"comptroller_member_name": {"type": "SingleLineText", "name": "Comptroller Member Name", "actions": ["view"]}, "comptroller_web_url": {"type": "Url", "name": "Comptroller Member Web URL", "actions": ["view"]}}}, "executive_member_details": {"key_name": "Executive Member Details", "attributes": {"executive_member_name": {"type": "SingleLineText", "name": "Executive Member Name", "actions": ["view"]}, "executive_web_url": {"type": "Url", "name": "Executive Member Web URL", "actions": ["view"]}}}, "attorney_member_details": {"key_name": "District Attorney Member Details", "attributes": {"attorney_member_name": {"type": "SingleLineText", "name": "District Attorney Member Name", "actions": ["view"]}, "attorney_web_url": {"type": "Url", "name": "District Attorney Web URL", "actions": ["view"]}}}}}, "beneficiaries": {"key_name": "Beneficiaries", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/beneficiary.png", "request_type": "beneficiary", "required_key": "beneficiary_type", "empty_message": "There are no Beneficiaries added.", "actions": ["view"], "api": "beneficiaries?employee_id=[EMPLOYEE_ID]", "widget_type": "Details", "hint": {"caption": "PLEASE NOTE *", "caption_color": "#d93025", "text": "No changes to beneficiaries are final until the union receives a signed, dated and notarized benefit enrollment form. Below is a downloadable copy of the form.", "show_links_in_list": true, "links": {"Designation of Beneficiary": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/nyccoba/29032-COBA-DESIGNATION-BENEFICIARY-ANNUITY-%20FORMS-MAY2025-v2.pdf"}}, "attributes": {"name": {"type": "SingleLineText", "name": "Name", "required": true, "actions": ["view"]}, "relationship": {"type": "SingleLineText", "name": "Relationship", "actions": ["view"]}, "address": {"type": "SingleLineText", "name": "Address", "actions": ["view"]}, "birthday": {"type": "Date", "name": "DOB", "actions": ["view"]}, "phone": {"type": "PhoneNumber", "name": "Phone Number", "action": ["view"]}, "beneficiary_type": {"type": "DropDown", "name": "Type", "required": true, "value": {"Primary": "Primary", "Contingent": "Secondary"}, "actions": ["view"]}, "percentage": {"type": "Number", "name": "Percentage", "required": true, "actions": ["view"]}, "file": {"type": "FileField", "name": "Upload", "selection_type": "single", "max_file_size": 10, "total_files": 1, "actions": ["view"]}}}, "forms": {"key_name": "Frequently Used Resources", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/form.png", "empty_message": "There are no Frequently Used Resources.", "api": "forms?file_type=form", "widget_type": "Forms", "actions": ["view"], "attributes": {"file": {"type": "FileField", "name": "Form", "actions": ["view"]}, "name": {"type": "SingleLineText", "name": "Form Name", "actions": ["view"]}}}, "links": {"key_name": "Useful Links", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/link.png", "empty_message": "There are no Useful Links.", "api": "forms?file_type=useful_links", "widget_type": "Links", "actions": ["view"], "attributes": {"name": {"type": "SingleLineText", "name": "Name", "actions": ["view"]}, "link": {"type": "SingleLineText", "name": "Link", "actions": ["view"]}}}, "phone_numbers": {"key_name": "Important Phone Numbers", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/phone.png", "empty_message": "There are no Important Phone Numbers.", "api": "forms?file_type=useful_phone_numbers", "widget_type": "Links", "actions": ["view"], "headings": ["Headquarters", "Correction Headquarters Facility Contacts", "Hospital", "Rikers Island Facilities", "Units/Divisions"], "attributes": {"name": {"type": "SingleLineText", "name": "Name", "actions": ["view"]}, "number": {"type": "SingleLineText", "name": "Important Phone Number", "actions": ["view"]}}}, "uploads": {"key_name": "Uploads", "empty_message": "No Uploads.", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/upload.png", "actions": ["view", "new"], "attributes": {"file": {"type": "FileField", "name": "File", "selection_type": "single", "max_file_size": 10, "total_files": 1, "required": true, "actions": ["view", "edit"]}, "date": {"type": "FromAPI", "name": "Date", "actions": ["view"]}, "notes": {"type": "MultiLineText", "name": "Notes", "actions": ["view", "edit"]}}}, "notification": {"key_name": "Notifications", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/notification.png", "empty_message": "There are no Notifications.", "api": "push_notification_index"}, "contact_us": {"key_name": "Contact Us", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/contact_us.png", "to": "<EMAIL>", "subject": "NYCCOBA: Feedback/Suggestion", "default_message": null}, "update_password": {"key_name": "Update Password", "menu_icon": "https://fuse-mobile-app-assets.s3.us-east-2.amazonaws.com/menu_icons/reset-password.png", "api": "employees/update_password"}}}