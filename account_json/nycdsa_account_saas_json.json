{"schema": {"employees": {"key_name": "Members Profile", "avatar": "Member Photo", "name": "Name", "first_name": "First Name", "middle_name": "Middle Name", "last_name": "Last Name", "suffix": "Suffix", "address": "Address", "street": "Address", "apartment": "Apt", "city": "City", "state": "State", "zipcode": "Zip Code", "do_not_mail": "Do Not Mail", "birthday": "DOB", "age": "Age", "social_security_number": "SSN", "social_security_number_format": "9", "title_code": "Employee ID #", "shield_number": "Shield #", "placard_number": "Placard #", "a_number": "Emblem Health ID #", "previous_shield_number": "Class", "member_since": "Union Agreement Processed", "start_date": "Date of Hire", "member_start_date": "Life Insurance Update", "janus_card": "<PERSON><PERSON>", "janus_card_opt_out_date": "<PERSON><PERSON>t Out Date", "email": "Email", "required_fields": ["name", "first_name", "last_name"], "unique_fields": ["previous_shield_number"], "search_columns": {"same_model": ["first_name", "middle_name", "last_name", "suffix", "shield_number", "placard_number", "title_code"], "associated_model": {"contacts": ["value"], "benefit_coverages": ["name", "first_name", "last_name", "suffix"], "beneficiaries": ["name"]}}}, "contacts": {"contact_number": {"key_name": "Contact Number", "personal_phone": "Cell", "required_fields": []}, "email_address": {"key_name": "Email address", "personal_email": "Personal", "required_fields": []}, "emergency_contacts": {"key_name": "Emergency Contact", "personal_emergency": "Personal", "colleague_emergency": "Colleague", "required_fields": []}}, "genders": {"key_name": "Gender", "name": "Name", "description": "Description", "required_fields": ["name"]}, "marital_statuses": {"key_name": "Marital Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employment_statuses": {"key_name": "Member Status", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_employment_statuses": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["employee_id", "employment_status_id"]}, "offices": {"key_name": "Commands", "name": "Name", "address": "Address", "phone": "Phone Number", "fax": "Description", "required_fields": ["name"]}, "employee_offices": {"start_date": "Start Date", "end_date": "End Date", "required_fields": ["office_id", "employee_id", "start_date"]}, "ranks": {"key_name": "Titles", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_ranks": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["rank_id", "employee_id"]}, "positions": {"key_name": "Union Position", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_positions": {"start_date": "Start Date", "end_date": "End Date", "notes": "Notes", "required_fields": ["position_id", "employee_id", "start_date"]}, "benefits": {"key_name": "Benefits", "name": "Name", "description": "Description", "required_fields": ["name"]}, "employee_benefits": {"start_date": "Start Date", "end_date": "End Date", "description": "Description", "required_fields": ["benefit_id", "employee_id", "start_date"]}, "beneficiaries": {"key_name": "Life Insurance Beneficiaries", "name": "Name", "gender_id": "Gender", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "phone": "Phone Number", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "beneficiary_type_values": ["Primary", "Secondary"], "required_fields": ["name", "employee_id", "percentage"], "required_auto_fields": ["address"]}, "annuity_beneficiaries": {"key_name": "Annuity Beneficiaries", "name": "Name", "gender_id": "Gender", "relationship": "Relationship", "address": "Address", "birthday": "DOB", "phone": "Phone Number", "beneficiary_type": "Type", "percentage": "Percentage", "file": "Upload", "beneficiary_type_values": ["AnnuityPrimary", "AnnuitySecondary"], "required_fields": ["name", "employee_id", "percentage"], "required_auto_fields": ["address"]}, "users": {"key_name": "Users", "username": "Username", "email": "Email Address", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "password_confirmation": "Password Confirmation", "user_audit_logging": "User <PERSON>t Logging", "required_fields": ["username", "email", "password", "password_confirmation", "role_id"]}, "roles": {"key_name": "Roles"}, "reports": {"single_employee": "Flex Report", "benefit_coverage_name": "Dependent Name", "benefit_coverages": "Benefit Dependents", "employee_name": "Member Name", "relationship": "Relationship", "birthday": "DOB", "lodi": "IOD", "union_meetings": "Union Meetings", "employee_delegate_assignment": "Member Delegate Assignment", "janus": "<PERSON><PERSON>", "columns": "Report Columns", "started_at": "From Date", "ended_at": "To Date", "employee_summary": "Member Summary", "total_summary": "Total Summary", "active_date": "Active Date", "inactive_date": "Inactive Date", "disbursement_date": "Disbursement Date", "disbursement_year": "Disbursement Year", "disbursements_not_found": "Benefit disbursements not found.", "name": "Name", "date_in": "Date In", "date_out": "Date Out", "used_hours": "Used Hours", "lodi_return_to_work_status": "Return to Work Status", "date_to": "Date To", "date_from": "Date From", "excel_report": "Generate Excel Report", "pdf_report": "Generate PDF Report", "single_mailing_label": "Generate Single Mailing Labels", "multiple_mailing_label": "Generate Multiple Mailing Labels", "payment_date_from": "Payment Date From", "payment_date_to": "Payment Date To", "show_disbursements": "Show Disbursements", "show_dependents": "Show Dependents", "delegates": "Delegates", "meetings": "Meetings", "employment_statuses_from": "Status From", "employment_statuses_to": "Status To", "dependent_count": "Dependent Count", "beneficiary": "Beneficiary", "show_coverages": "Show Dependents"}, "notifications": {"key_name": "Notifications", "subject": "Subject", "sms_message": "Sms Message", "email_message": "Email Message", "sms": "false", "email": "false", "filter": "Filter", "sms_no_reply_text": "Please note, this is a no-reply text."}, "user_audit": {"key_name": "User <PERSON>t", "user_name": "User Name", "title": "Title", "employee_name": "Employee Name", "created_at": "Created At"}, "benefit_coverages": {"key_name": "Benefit Dependents", "name": "Name", "relationship": "Relationship", "social_security_number": "employees.social_security_number", "address": "Address", "birthday": "DOB", "expires_at": "Expiration", "required_fields": ["employee_id", "address", "employee_benefit_id", "birthday", "name", "social_security_number", "relationship"]}}, "ui": {"employees": {"key_name": "Member Profile", "ssn_unique_search_label": "Shield #", "is_search": ["employees"], "is_filter": ["employees", "title_code", "ranks"], "table_headers": ["name", "employment_status_name", "office_name", "rank_name", "title_code", "shield_number", "placard_number"], "tabs": ["profile", "benefits"], "profile": {"key_name": "Profile", "additional_details": ["first_name", "middle_name", "last_name", "suffix"], "employees": ["avatar", "name", "address", "do_not_mail", "birthday", "age", "genders", "marital_statuses", "social_security_number", "title_code", "shield_number", "placard_number", "a_number", "previous_shield_number", "start_date", "member_start_date", "member_since"], "contacts": [{"is_restrict_emergency_contact_details": true, "contact_number": ["personal_phone"], "emergency_contacts": ["personal_emergency", "colleague_emergency"], "emergency_contact_details": ["name", "contact", "relationship"]}, {"email_address": ["personal_email"]}], "others": ["employee_employment_statuses", "employee_offices", "employee_positions", "employee_ranks", "employees.janus_card", "employees.janus_card_opt_out_date"]}}, "users": {"is_search": ["users"], "table_headers": ["username", "name", "email", "roles"]}, "reports": {"key_name": "Report", "tabs": ["single_employee"], "single_employee": {"primary_filters": [["reports.columns"], ["employees"]], "secondary_filters": [["offices", "genders"], ["employment_statuses", "marital_statuses"], ["reports.employment_statuses_from", "reports.employment_statuses_to"], ["employees.shield_number", "positions"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.placard_number", "employees.city"], ["employees.state", "employees.zipcode"]], "columns": ["employees", "employees.first_name", "employees.middle_name", "employees.last_name", "employees.suffix", "offices", "employment_statuses", "employees.email", "marital_statuses", "positions", "employees.shield_number", "employees.social_security_number", "employees.placard_number", "employees.birthday", "employees.city", "employees.state", "employees.zipcode", "employees.start_date", "employees.apartment", "employees.street", "genders", "employees.a_number", "employees.member_start_date", "employees.member_since"], "default_columns": ["employees"], "actions": ["mailing_label", "excel_report", "pdf_report"]}}, "notification": {"key_name": "Notifications", "analytics": "Analytics", "is_search": true, "allow_sms_attachments": true, "check_confirmation": true, "notification_tracker_count": true, "filters": [["employees"], ["offices", "genders"], ["employment_statuses", "marital_statuses"], ["reports.employment_statuses_from", "reports.employment_statuses_to"], ["employees.shield_number", "positions"], ["employees.birthday_from", "employees.birthday_to"], ["employees.age_from", "employees.age_to"], ["employees.placard_number", "employees.city"], ["employees.state", "employees.zipcode"]]}, "user_audit": {"key_name": "User <PERSON>t", "table_headers": ["user_name", "title", "employee_name", "created_at"]}, "settings": {"key_name": "Settings", "tabs": ["genders", "marital_statuses", "employment_statuses", "offices", "ranks", "positions", "benefits"]}}}