<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="_x31_" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 154 185" style="enable-background:new 0 0 154 185;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#191349;}
	.st1{fill:url(#SVGID_1_);}
	.st2{fill:url(#SVGID_2_);}
	.st3{fill:url(#SVGID_3_);}
	.st4{fill:url(#SVGID_4_);}
	.st5{fill:url(#SVGID_5_);}
	.st6{fill:url(#SVGID_6_);}
	.st7{fill:url(#SVGID_7_);}
	.st8{fill:url(#SVGID_8_);}
	.st9{fill:#BE202E;}
	.st10{fill:#FFFFFF;}
	.st11{fill:url(#SVGID_9_);}
	.st12{fill:url(#SVGID_10_);}
	.st13{fill:#EA2127;}
	.st14{clip-path:url(#SVGID_12_);}
	.st15{clip-path:url(#SVGID_14_);}
	.st16{fill:url(#SVGID_15_);}
	.st17{fill:url(#SVGID_16_);}
	.st18{fill:#FEFFFF;}
	.st19{fill:url(#SVGID_17_);}
	.st20{fill:none;stroke:#191349;stroke-width:0.5168;stroke-miterlimit:10;}
</style>
<g>
	
		<image style="overflow:visible;opacity:0.75;enable-background:new    ;" width="663" height="750" xlink:href="AA96A4B37F37B29A.png"  transform="matrix(0.2378 0 0 0.2378 -1.2557 1.28)">
	</image>
	<g>
		<g>
			<path class="st0" d="M98.8,143.3c0,0-28-1.6-28.7-2.1c-0.7-0.4,0.1-0.8,0.1-0.8h0.9l28.3,1.5L98.8,143.3z"/>
			<g>
				<g>
					<g>
						
							<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="1018.8903" y1="76.4016" x2="1061.928" y2="80.6523" gradientTransform="matrix(-1 0 0 -1 1159.7368 229.1)">
							<stop  offset="0.1121" style="stop-color:#CB252B"/>
							<stop  offset="0.5698" style="stop-color:#811517"/>
							<stop  offset="0.7921" style="stop-color:#B8262C"/>
							<stop  offset="0.8814" style="stop-color:#962024"/>
							<stop  offset="1" style="stop-color:#000000"/>
						</linearGradient>
						<path class="st1" d="M102.3,149.4c0,0-0.4,0.8-0.6,1.4c-0.1,0.6-0.9,4.5,5.6,4.6s11.2,1.1,17.4,3.6c6.2,2.5,10.4,5.3,10.4,5.3
							l-1.6-12.1l7.6-5.6c0,0-8.2-5.9-16.3-7.5c-8-1.6-19.6,1.1-20,2.5C104.5,143.1,102.3,149.4,102.3,149.4z"/>
						
							<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="1019.3676" y1="85.3832" x2="1037.2281" y2="85.3832" gradientTransform="matrix(-1 0 0 -1 1159.7368 229.1)">
							<stop  offset="1.899310e-02" style="stop-color:#CC8F92"/>
							<stop  offset="0.2895" style="stop-color:#F7F7F7"/>
							<stop  offset="0.5158" style="stop-color:#FFFFFF"/>
							<stop  offset="1" style="stop-color:#CC8F92"/>
						</linearGradient>
						<path class="st2" d="M122.9,140.5c0,0,5.3,1,10.6,3.6c3.5,1.7,5.9,3.7,5.9,3.7l0.9-0.6c0,0-7.5-5.7-17.3-7.5
							C121.8,139.5,122.9,140.5,122.9,140.5z"/>
						
							<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="1024.6125" y1="72.47" x2="1057.7324" y2="72.47" gradientTransform="matrix(-1 0 0 -1 1159.7368 229.1)">
							<stop  offset="1.899310e-02" style="stop-color:#CC8F92"/>
							<stop  offset="0.2895" style="stop-color:#F7F7F7"/>
							<stop  offset="0.5158" style="stop-color:#FFFFFF"/>
							<stop  offset="1" style="stop-color:#CC8F92"/>
						</linearGradient>
						<path class="st3" d="M134.9,162.1c0,0-5.5-3.7-14.9-6.5c-9-2.7-13.8-1.5-16.1-2.7c-1-0.5-2.2-1.4-1.7-2.9c0,0-0.9,1.6,0.6,3
							c2.3,2.2,3.5,0.7,11.8,2.2c10.7,1.9,20,7.6,20.5,8.1L134.9,162.1z"/>
						
							<linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="1033.4437" y1="79.1665" x2="1058.6823" y2="85.2768" gradientTransform="matrix(-1 0 0 -1 1159.7368 229.1)">
							<stop  offset="0" style="stop-color:#B88286"/>
							<stop  offset="0.3853" style="stop-color:#FFFFFF"/>
							<stop  offset="0.6128" style="stop-color:#FFFFFF"/>
							<stop  offset="0.9207" style="stop-color:#B88286"/>
						</linearGradient>
						<path class="st4" d="M103.6,140.9c0,0,0,2.3-0.7,5.6c-0.7,3.2-1,4.3-1,4.3s0.6-2.1,3.4-1.9c3.2,0.2,5.2,3.1,10.5,4.6
							c5.4,1.5,8,0.8,8.4-0.5s0.8-10.1,0.8-10.1L103.6,140.9z"/>
					</g>
					<g>
						
							<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="14.4233" y1="76.4087" x2="57.4582" y2="80.659" gradientTransform="matrix(1 0 0 -1 0 229.1)">
							<stop  offset="0.1121" style="stop-color:#CB252B"/>
							<stop  offset="0.5698" style="stop-color:#811517"/>
							<stop  offset="0.7921" style="stop-color:#B8262C"/>
							<stop  offset="0.8814" style="stop-color:#962024"/>
							<stop  offset="1" style="stop-color:#000000"/>
						</linearGradient>
						<path class="st5" d="M52.9,149.4c0,0,0.4,0.8,0.6,1.4c0.1,0.6,0.9,4.5-5.6,4.6s-11.2,1.1-17.4,3.6s-10.4,5.3-10.4,5.3
							l1.6-12.1l-7.6-5.6c0,0,8.2-5.9,16.2-7.5s19.6,1.1,20,2.5C50.7,143.1,52.9,149.4,52.9,149.4z"/>
						
							<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="14.9" y1="85.3832" x2="32.7605" y2="85.3832" gradientTransform="matrix(1 0 0 -1 0 229.1)">
							<stop  offset="1.899310e-02" style="stop-color:#CC8F92"/>
							<stop  offset="0.2895" style="stop-color:#F7F7F7"/>
							<stop  offset="0.5158" style="stop-color:#FFFFFF"/>
							<stop  offset="1" style="stop-color:#CC8F92"/>
						</linearGradient>
						<path class="st6" d="M32.3,140.5c0,0-5.3,1-10.6,3.6c-3.5,1.7-5.9,3.7-5.9,3.7l-0.9-0.6c0,0,7.5-5.7,17.3-7.5
							C33.4,139.5,32.3,140.5,32.3,140.5z"/>
						
							<linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="20.1646" y1="72.47" x2="53.2882" y2="72.47" gradientTransform="matrix(1 0 0 -1 0 229.1)">
							<stop  offset="1.899310e-02" style="stop-color:#CC8F92"/>
							<stop  offset="0.2895" style="stop-color:#F7F7F7"/>
							<stop  offset="0.5158" style="stop-color:#FFFFFF"/>
							<stop  offset="1" style="stop-color:#CC8F92"/>
						</linearGradient>
						<path class="st7" d="M20.3,162.1c0,0,5.5-3.7,14.9-6.5c9-2.7,13.8-1.5,16.1-2.7c1-0.5,2.2-1.4,1.7-2.9c0,0,0.9,1.6-0.6,3
							c-2.3,2.2-3.5,0.7-11.8,2.2c-10.7,1.9-20,7.6-20.5,8.1L20.3,162.1z"/>
						
							<linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="28.9793" y1="79.148" x2="54.2175" y2="85.2583" gradientTransform="matrix(1 0 0 -1 0 229.1)">
							<stop  offset="0" style="stop-color:#B88286"/>
							<stop  offset="0.3853" style="stop-color:#FFFFFF"/>
							<stop  offset="0.6128" style="stop-color:#FFFFFF"/>
							<stop  offset="0.9207" style="stop-color:#B88286"/>
						</linearGradient>
						<path class="st8" d="M51.7,140.9c0,0,0.1,2.3,0.7,5.6c0.7,3.2,1,4.3,1,4.3s-0.6-2.1-3.4-1.9c-3.2,0.2-5.2,3.1-10.5,4.6
							c-5.4,1.5-8,0.8-8.4-0.5s-0.8-10.1-0.8-10.1L51.7,140.9z"/>
					</g>
				</g>
				<g>
					<circle class="st9" cx="76.4" cy="96.5" r="59.3"/>
					<path class="st10" d="M76.4,155.9c-15.9,0-30.8-6.2-42-17.4S17,112.4,17,96.5s6.2-30.8,17.4-42s26.1-17.4,42-17.4
						s30.8,6.2,42,17.4s17.4,26.1,17.4,42s-6.2,30.8-17.4,42S92.3,155.9,76.4,155.9z M76.4,37.4c-15.8,0-30.6,6.1-41.8,17.3
						s-17.3,26-17.3,41.8s6.2,30.7,17.3,41.8c11.2,11.2,26,17.3,41.8,17.3s30.7-6.2,41.8-17.3c11.2-11.2,17.3-26,17.3-41.8
						s-6.2-30.7-17.3-41.8C107.1,43.5,92.2,37.4,76.4,37.4z"/>
				</g>
				<g>
					
						<linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="77.1227" y1="165.0928" x2="76.4949" y2="135.903" gradientTransform="matrix(1 0 0 -1 0 229.1)">
						<stop  offset="0" style="stop-color:#1C75BB"/>
						<stop  offset="0.2276" style="stop-color:#1D72B6"/>
						<stop  offset="0.4013" style="stop-color:#206AAA"/>
						<stop  offset="0.5572" style="stop-color:#235D99"/>
						<stop  offset="0.7026" style="stop-color:#244C83"/>
						<stop  offset="0.8407" style="stop-color:#21366A"/>
						<stop  offset="0.9717" style="stop-color:#1B1A4F"/>
						<stop  offset="1" style="stop-color:#191349"/>
					</linearGradient>
					<circle class="st11" cx="76.4" cy="96.5" r="42.5"/>
					<path class="st0" d="M76.4,139.2c-23.5,0-42.6-19.2-42.6-42.7s19.1-42.7,42.7-42.7c23.5,0,42.7,19.1,42.7,42.7
						C119.1,120,100,139.2,76.4,139.2z M76.4,54.1C53,54.1,34,73.1,34,96.5s19,42.4,42.4,42.4s42.4-19,42.4-42.4
						S99.8,54.1,76.4,54.1z"/>
				</g>
				<g>
					<g>
						<g>
							<path class="st0" d="M119.8,91.5c0,1.3,0,2.5,0,3.8c-28.8,0-57.7,0-86.5,0c0-1.4,0-2.8,0-4.3c0.8,0.1,1.2-0.2,1.9-0.3
								c0.7-0.1,1.3,0.1,1.6-0.4c0.2,0,0.4,0,0.6,0c0.2-0.1,0.1-0.3,0.5-0.2c0-0.3,0-0.5,0-0.8c0-0.2,0.2-0.1,0.3-0.1
								c0-1.1,0-2,0.5-2.6c-0.1-0.5,0.1-0.8-0.1-1.1c-0.1-0.2,0.1-0.2,0.1-0.4c-0.3-0.8,0.4-1.6,0-2.4c0.3,0.1,0.1-0.3,0.3-0.3
								c0.2,0.4-0.2,0.9-0.1,1.6c0.3-0.1,0.2-0.1,0.4-0.2c0,0.1,0,0.3,0.1,0.1c0.1,0-0.1,0.3,0.1,0.3c-0.1,0.1-0.2,0.2-0.2,0.4
								c0,0.3,0.3,0.3,0.3,0.6c0.1,0,0.4-0.1,0.5,0.1c0.1,0.3-0.2,0.5-0.4,0.7c0.3,0.6-0.1,1,0,1.7c0,0.2,0.2,0,0.3,0.1
								c0,0.5,0,0.9,0,1.3c-0.1,0.2,0.2,0,0.2,0.1c0,0.3,0,0.5,0,0.8c0.2,0.1,0.6,0,0.6,0.3c0.3,0,0.6,0,0.9,0c0,0.1,0,0.2,0,0.4
								c0.4,0.1,0.7-0.1,1.1,0c0.2,0,0.3,0.3,0.5,0.3c0.5,0.2,1.1,0,1.8,0.1c0,0.2,0,0.4,0,0.4c0.5,0,1,0,1.6,0c0-0.7,0-1.4,0-2.2
								c0.4,0,0.8,0,1.2,0c0-0.4,0-0.8,0-1.3c0-0.2,0.2-0.1,0.3-0.1c0-0.3,0-0.6,0-0.9c0.1,0,0.3,0,0.4,0c0-0.3,0-0.5,0-0.8
								c0.1,0,0.3,0,0.4,0c0-0.2,0-0.5,0-0.7c0.4,0,0.9,0,1.3,0c0-1.3,0-2.6,0-3.9c0.4,0,0.7,0,1.1,0c0.2,0,0,0.3,0.1,0.4
								c0.4,0,0.8,0,1.2,0c0,1.4,0,2.8,0,4.3c0,0.1,0.3,0,0.4,0c0,0.3,0,0.6,0,0.9c0.4,0.1,0.9,0,1.4,0c0.1,0,0-0.3,0-0.4
								c0.4,0,0.8,0,1.3,0c0.2,0,0.1-0.3,0.1-0.4c0.1,0,0.2,0,0.3,0c0-0.3,0-0.7,0-1c0-0.2,0.2,0,0.2-0.2c0-1.5,0-3.1,0-4.6
								c0.1-0.1,0.3-0.1,0.2-0.4c0.4,0,0.7,0,1.1,0c0,0.4,0,0.8,0.3,0.9c0,1.4,0,2.7,0,4.1c-0.1,0.2,0.2,0,0.2,0.1
								c0.1,1.3,0.2,2.5,0.1,3.9c0.2,0.1,0.6,0,0.9,0c0.1-2.1,0-4.7,0-7.1c0-1.1-0.2-2.5,0.4-2.9c0.1-0.7-0.2-0.9-0.4-1.3
								c0.3,0.1,0.4-0.1,0.4-0.2c0.7-0.1,1.2,0.1,1.7,0.1c0,0.2,0,0.4,0,0.7c-0.1,0-0.2,0-0.4,0c0.1,0.4-0.2,1.1,0.2,1.1
								c0,0.7,0,1.4,0,2.1c0.6,0,0.5-0.7,0.6-1.1c0.1-0.2,0.1-0.5,0.2-0.7s0.1-0.5,0.3-0.5c0.3,0.2,0.1,0.6,0.1,0.9
								c0.1,0.3,0.3,0.5,0.4,0.6c0-1.6,0-3.1,0-4.7c0-0.2,0.2-0.1,0.3-0.1c0-0.2,0-0.3,0-0.5c0.2,0,0.4,0,0.5,0c0,0.2,0,0.4,0,0.6
								c0.4,0,0.7,0,1.1,0c0,2.8,0,5.6,0,8.5c0,0.2,0.2,0.2,0.2,0.4c0,1.4,0,2.7,0,4.1c0.1,0.1,0.4,0,0.6,0c0.1-0.3-0.2-1,0.2-0.9
								c0-1.9,0-3.9,0-5.8c0-0.2,0.1-0.1,0.2-0.1c0-0.4,0-0.9,0-1.3c0.3-0.1,0.2-0.6,0.2-0.9c0.2-0.1,0.3-0.3,0.2-0.6
								c0.1,0,0.2,0,0.3,0c0-0.7,0-1.3,0-2c0.1-0.1,0.2,0,0.3,0c0.1-0.9-0.1-1.9,0.1-2.6c0,0.9,0,1.7,0,2.6c0,0.2,0.2,0,0.3,0.1
								c0,0.6,0,1.3,0,1.9c0.2,0,0.3,0.2,0.2,0.4c0,0.2,0.3,0,0.3,0.1c0,0.3,0,0.5,0,0.8c0,0.1,0.1,0.1,0.2,0.1c-0.1,0.4,0,1,0,1.5
								c0.2-0.2,0.6-0.4,0.6-0.8c0.2-0.8-0.2-2,0-2.9c0-0.2,0.2-0.1,0.3-0.1c0-1.4,0-2.7,0-4.1c0.3,0,0.6,0,0.9,0
								c0.2-0.7-0.1-1.4,0-2c0.1-0.5,0.8-0.6,0.6-1.3c0.4,0,0.7-0.1,0.8-0.4c0.2-0.1,0.1,0.2,0.1,0.3c0.1,0,0.3,0,0.4,0
								c-0.1,0.2,0,0.3,0.3,0.3c-0.1,0.6,0.3,0.8,0.5,1.1c0,0.7,0,1.4,0,2.1c0.3,0,0.6,0,0.9,0c0,1.6,0,3.2,0,4.7
								c0.1,0.1,0.3,0,0.5,0c0-1-0.1-2.2,0-3.2c0.4,0.2,0,1.3,0.2,1.7c0.4,0.1,0.1-0.5,0.5-0.3c0-0.6,0-1.2,0-1.8c0.3,0,0.5,0,0.8,0
								c0.1-1,0-2.2,0-3.2c0.2,0,0.3,0,0.5,0c0,1.1,0,2.1,0,3.2c0.2,0.1,0.6,0,0.9,0c-0.1,0.5,0,1.1,0,1.7c0.2,0.1,0.3,0.2,0.4,0.3
								c0.3-0.3-0.1-1.2,0.2-1.5c0.2,0,0.1,0.4,0.1,0.5c0.1,3-0.1,6.3,0,9.3c0.1,0.1,0.5,0,0.7,0c0-0.9,0-1.8,0-2.7
								c0.2,0,0.4,0,0.6,0c0-2.7,0-5.5,0-8.2c0.1-0.1,0.2-0.2,0.2-0.5c0.1-0.1,0.2-0.1,0.3,0c0-0.6,0.4-0.7,0.6-1.1
								c0-0.6-0.1-1.2,0-1.7c0.3,0.5,0,1.1,0.1,1.7c0.1,0.4,0.6,0.6,0.6,1.1c0,0.1,0.2,0,0.2,0c0,0.1,0,0.3,0,0.4
								c0.1,0.1,0.3,0,0.3,0c0,2.8,0,5.6,0,8.3c0.1,0.1,0.3,0,0.4,0c0,0.8,0,1.6,0,2.4c0.1,0,0.2,0,0.4,0c0.2-0.6-0.1-1.7,0.1-2.2
								c0.4-0.1,0.3,0.3,0.5,0.4c0.6,0,1.1,0,1.7,0c0.1-1.3,0.1-2.9,0-4.2c0-0.1,0.2-0.1,0.4-0.1c0.1-0.9-0.1-1.6,0-2.3
								c0.1-0.3,0.3-0.5,0.4-0.8v-0.3c0-0.1,0.2-0.1,0.2-0.1c0.1-0.3-0.1-0.4,0.1-0.7c0.4,0.2-0.1,1.2,0.5,1
								c-0.1,0.4,0.3,0.5,0.4,0.8c0.2,0.6-0.2,1.6,0,2.4l0.1,0.1c0.2,1.6-0.2,3.3,0,4.8c0.1,0,0.2,0,0.3,0c0.3-1-0.2-2.5,0-3.5
								c0.1-0.4,0.7-0.6,0.8-0.9c0.4,0,0.8,0,1.2,0c0.1-2.8,0-5.8,0-8.7c0-0.1,0.1-0.1,0.2-0.1c0-0.6,0-1.2,0-1.7c0.3,0,0.5,0,0.8,0
								c0-0.4,0-0.7,0-1.1c0-0.2,0.2-0.1,0.2-0.1c0-0.2,0-0.3,0-0.5c0.3-0.2,0.3-0.8,0.8-1c0.1-1.1-0.1-2.4,0.1-3.4
								c0.2,0.9,0,2.3,0.1,3.4c0.5,0.6,0.8,1.3,0.8,2.3c0,0.2,0.3,0,0.4,0.1c0,0.2,0,0.4,0,0.6c0.1,0.1,0.3,0,0.5,0
								c0.2-0.1-0.1-0.6,0.1-0.6c0.2,0.1,0.1,0.5,0.1,0.9c0,0.3,0,0.7,0,1c0,0.1,0.3,0,0.3,0.1c0,2.9,0,5.9,0,8.8c0.4,0.1,1,0,1.5,0
								c0,1.6-0.1,3.4,0,5c0.4,0,0.1-0.7,0.2-1.1c0.3-0.1,0.3-0.7,0-0.8c0-0.2,0.2-0.1,0.3-0.1c0-2.9,0-5.8,0-8.6
								c0-0.1,0.1-0.1,0.2-0.1c0-0.8,0-1.5,0-2.3c0.4-0.3,0.4-0.9,0.4-1.5c0-0.2,0.1-0.1,0.2-0.1c0-0.2,0-0.4,0-0.5
								c0.3-0.2,0.2-1,0.6-1.2c0-0.8-0.1-1.8,0-2.5c0.3,0.8-0.1,1.7,0.1,2.5c0.1,0.5,0.5,1,0.4,1.6c-0.1,0.2,0.2,0,0.2,0.1
								c0,0.3,0,0.6,0,0.9c0.3,0,0.1,0.6,0.4,0.6c0,0.7,0,1.5,0,2.2c-0.1,0.2,0.2,0.1,0.2,0.2c0,2.9,0,5.8,0,8.6
								c0,0.1,0.4,0,0.4,0.1c0.1,0.2-0.1,0.1-0.3,0.1c0,0.2,0,0.4,0,0.5s0.3,0,0.3,0.1c0,0.3,0,0.6,0,0.9c0,0.1,0.2,0,0.2,0.1
								c0,0.7,0,1.5,0,2.2c0.1,0.1,0.3,0,0.4,0c0,1.4,0,2.7,0,4.1c0.1,0,0.2,0,0.4,0c0.1-1.3-0.1-2.7,0-4c0.2,0,0.1-0.4,0.4-0.4
								c0-0.7,0-1.4,0-2.2c-0.1-0.2,0.2,0,0.2-0.1c0-0.4,0-0.8,0-1.3c0.1-0.1,0.2-0.2,0.1-0.4c0.1-0.1,0.3-0.2,0.2-0.4
								c0-0.2,0.2,0,0.3-0.1c0-0.1-0.1-0.4,0.1-0.4c0.2-0.1,0.2,0.2,0.2,0.3c1-0.1,1.8,0,1.6,1.1c0,0.1,0.3,0,0.3,0.1
								c0,0.4,0,0.8,0,1.2c0,0.1,0.3,0,0.3,0.1c0,0.7,0,1.5,0,2.2c0.4,0,0.8,0,1.2,0c0-0.7,0-1.4,0-2.1c0.7-0.5,1.3,0.3,1.7,0.7
								c0,0.3,0,0.5,0.2,0.5c0,1.7,0,3.4,0,5.2c0.1,0,0.2,0,0.3,0c0.1-1.7,0-3.6,0-5.4c0.1,0,0.3,0,0.4,0c0-0.4,0-0.7,0-1.1
								c1.5-0.1,2.7,0.2,4.2,0.1c-0.1,0.4,0.2,0.5,0.1,0.9c-0.1,0.2,0.2,0,0.2,0.1c0,1.1,0,2.2,0,3.4c0.1,0,0.2,0,0.4,0
								c0.1-0.6,0.1-1.2,0.1-1.7c0.3-0.2,0.3-0.6,0.7-0.6s0.4,0.4,0.8,0.5c0.2,0.9,0.1,2,0.1,3.1c0.4,0,0.8,0,1.2,0
								c0-0.2,0.3-0.3,0.4-0.4c0.2,0,0,0.3,0.3,0.2c0,0.2,0,0.5,0,0.7c0.1,0.1,0.4,0,0.5,0c0-0.3,0-0.5,0-0.8c0.4,0,0.6-0.1,1,0
								c0,0.1-0.1,0.4,0.1,0.4c0.3-0.2,0.1-1,0.1-1.4c0.3-0.3,0.1-1.1,0.1-1.7c0.3,0,0.3-0.2,0.4-0.4c0.2,0,0.4,0,0.7,0
								c0.1,0.2,0.2,0.3,0.4,0.4c0.1,0.5-0.2,1.4,0.2,1.5c0,0.7,0,1.4,0.1,2C119.4,91.6,119.6,91.5,119.8,91.5z"/>
						</g>
					</g>
				</g>
				<g>
					
						<radialGradient id="SVGID_10_" cx="76.4243" cy="132.52" r="56.6" gradientTransform="matrix(1 0 0 -1 0 229.1)" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#1C75BB"/>
						<stop  offset="0.2276" style="stop-color:#1D72B6"/>
						<stop  offset="0.4013" style="stop-color:#206AAA"/>
						<stop  offset="0.5572" style="stop-color:#235D99"/>
						<stop  offset="0.7026" style="stop-color:#244C83"/>
						<stop  offset="0.8407" style="stop-color:#21366A"/>
						<stop  offset="0.9717" style="stop-color:#1B1A4F"/>
						<stop  offset="1" style="stop-color:#191349"/>
					</radialGradient>
					<path class="st12" d="M76.4,40c-31.2,0-56.6,25.3-56.6,56.6c0,31.2,25.3,56.6,56.6,56.6c31.2,0,56.6-25.3,56.6-56.6
						S107.7,40,76.4,40z M76.4,139c-23.5,0-42.5-19-42.5-42.5S52.9,54,76.4,54s42.5,19,42.5,42.5C119,120,99.9,139,76.4,139z"/>
					<path class="st10" d="M76.4,153.2c-15.1,0-29.4-5.9-40.1-16.6c-10.7-10.7-16.6-24.9-16.6-40.1c0-15.1,5.9-29.4,16.6-40.1
						s24.9-16.6,40.1-16.6s29.4,5.9,40.1,16.6s16.6,24.9,16.6,40.1c0,15.1-5.9,29.4-16.6,40.1C105.8,147.3,91.6,153.2,76.4,153.2z
						 M76.4,40.1c-15.1,0-29.2,5.9-39.9,16.5C25.9,67.3,20,81.4,20,96.5c0,31.1,25.3,56.4,56.4,56.4s56.4-25.3,56.4-56.4
						c0-15.1-5.9-29.2-16.5-39.9C105.7,46,91.5,40.1,76.4,40.1z M76.4,139.2c-23.5,0-42.6-19.2-42.6-42.7s19.1-42.7,42.7-42.7
						c23.5,0,42.7,19.1,42.7,42.7C119.1,120,100,139.2,76.4,139.2z M76.4,54.1C53,54.1,34,73.1,34,96.5s19,42.4,42.4,42.4
						s42.4-19,42.4-42.4S99.8,54.1,76.4,54.1z"/>
				</g>
				<g>
					<g>
						<polygon class="st9" points="76.7,16.8 84.4,32.3 101.4,34.7 89.1,46.8 92,63.8 76.7,55.8 61.4,63.8 64.3,46.8 52,34.7 
							69.1,32.3 						"/>
						<path class="st10" d="M61.3,64.1l3-17.2L51.7,34.7L69,32.2l7.7-15.7l7.7,15.7l17.3,2.5L89.2,46.9l3,17.2L76.7,56L61.3,64.1z
							 M76.7,55.6L76.7,55.6l15.1,7.9l-2.9-16.8l0,0l12.2-11.9l-16.9-2.5L76.6,17L69,32.3h-0.1l-16.8,2.4l12.2,11.9l-2.9,16.8
							L76.7,55.6z"/>
					</g>
					<polygon class="st0" points="76.9,22.3 82.9,34.5 96.5,36.5 86.7,46 89,59.5 76.9,53.1 64.8,59.5 67.1,46 57.3,36.5 70.8,34.5 
											"/>
				</g>
				<g>
					<path class="st10" d="M115.5,121l0.5-0.8l5.3,0.9l-0.7,1l-1.1-0.2l-1.1,1.6l0.6,0.9l-0.7,1L115.5,121z M116.9,121.4l0.9,1.4
						l0.7-1L116.9,121.4z"/>
					<path class="st10" d="M119.5,116.4c-0.1,0-0.3,0.1-0.4,0.2c-0.1,0.1-0.2,0.2-0.3,0.4c0,0.1-0.1,0.2-0.1,0.2c0,0.1,0,0.2,0,0.3
						c0,0.1,0,0.2,0.1,0.2c0,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.3,0.1,0.4,0c0.1-0.1,0.2-0.2,0.4-0.3c0.1-0.1,0.3-0.3,0.4-0.4
						c0.1-0.1,0.3-0.3,0.5-0.4s0.4-0.2,0.6-0.2c0.2,0,0.4,0,0.7,0.2c0.2,0.1,0.4,0.3,0.6,0.5c0.1,0.2,0.2,0.4,0.3,0.6
						c0,0.2,0,0.4,0,0.7c-0.1,0.2-0.1,0.5-0.3,0.7c-0.2,0.3-0.3,0.5-0.6,0.7c-0.2,0.2-0.5,0.4-0.8,0.5l-0.4-1.1
						c0.2,0,0.4-0.1,0.5-0.2c0.2-0.1,0.3-0.3,0.4-0.4c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2-0.1-0.2
						c0-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.3-0.1-0.4,0c-0.1,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.3,0.3-0.4,0.4c-0.2,0.2-0.3,0.3-0.5,0.4
						s-0.4,0.2-0.6,0.2c-0.2,0-0.4,0-0.7-0.2c-0.2-0.1-0.4-0.3-0.5-0.5s-0.2-0.4-0.2-0.6s0-0.5,0-0.7s0.1-0.5,0.2-0.7
						c0.1-0.3,0.3-0.5,0.5-0.7s0.4-0.3,0.7-0.5L119.5,116.4z"/>
					<path class="st10" d="M121.2,112.7c-0.1,0-0.3,0.1-0.4,0.2c-0.1,0.1-0.2,0.2-0.2,0.4c0,0.1-0.1,0.2-0.1,0.2c0,0.1,0,0.2,0,0.3
						c0,0.1,0,0.2,0.1,0.2c0,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.3,0,0.4,0c0.1-0.1,0.2-0.2,0.4-0.3c0.1-0.1,0.2-0.3,0.4-0.5
						c0.1-0.2,0.3-0.3,0.4-0.4c0.2-0.1,0.3-0.2,0.5-0.2s0.4,0,0.7,0.1s0.5,0.2,0.6,0.4c0.2,0.2,0.2,0.4,0.3,0.6s0.1,0.4,0,0.7
						c0,0.2-0.1,0.5-0.2,0.7c-0.1,0.3-0.3,0.6-0.5,0.8c-0.2,0.2-0.4,0.4-0.7,0.5l-0.5-1.1c0.2,0,0.3-0.1,0.5-0.3
						c0.1-0.1,0.2-0.3,0.3-0.4c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2-0.1-0.2c0-0.1-0.1-0.1-0.2-0.1
						c-0.1-0.1-0.3,0-0.4,0c-0.1,0.1-0.2,0.2-0.4,0.3c-0.1,0.1-0.3,0.3-0.4,0.5c-0.1,0.2-0.3,0.3-0.4,0.4c-0.2,0.1-0.3,0.2-0.5,0.2
						s-0.4,0-0.7-0.1c-0.2-0.1-0.4-0.2-0.6-0.4c-0.1-0.2-0.2-0.4-0.3-0.6s-0.1-0.4,0-0.7c0-0.2,0.1-0.5,0.2-0.7
						c0.1-0.3,0.3-0.5,0.4-0.7c0.2-0.2,0.4-0.4,0.6-0.5L121.2,112.7z"/>
					<path class="st10" d="M122.9,112c-0.4-0.1-0.7-0.3-1-0.5s-0.5-0.5-0.6-0.8c-0.2-0.3-0.2-0.6-0.3-1c0-0.4,0-0.7,0.1-1.1
						s0.3-0.7,0.5-1s0.5-0.5,0.7-0.7c0.3-0.2,0.6-0.3,0.9-0.3c0.3,0,0.7,0,1.1,0.1c0.4,0.1,0.7,0.3,1,0.5s0.5,0.5,0.6,0.8
						s0.2,0.6,0.3,1c0,0.4,0,0.7-0.1,1.1s-0.3,0.7-0.5,1s-0.5,0.5-0.7,0.7c-0.3,0.2-0.6,0.3-0.9,0.3
						C123.6,112.1,123.3,112.1,122.9,112z M123.2,110.9c0.2,0.1,0.4,0.1,0.6,0.1c0.2,0,0.4-0.1,0.6-0.2c0.2-0.1,0.3-0.2,0.5-0.4
						c0.1-0.2,0.2-0.4,0.3-0.6s0.1-0.4,0.1-0.7c0-0.2-0.1-0.4-0.2-0.6c-0.1-0.2-0.2-0.3-0.4-0.4s-0.4-0.2-0.6-0.3
						c-0.2-0.1-0.4-0.1-0.6-0.1c-0.2,0-0.4,0.1-0.6,0.2c-0.2,0.1-0.3,0.2-0.5,0.4c-0.1,0.2-0.2,0.4-0.3,0.6s-0.1,0.4-0.1,0.7
						c0,0.2,0.1,0.4,0.2,0.6c0.1,0.2,0.2,0.3,0.4,0.4C122.8,110.7,123,110.8,123.2,110.9z"/>
					<path class="st10" d="M124,102.5c-0.1,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.2,0.3-0.2,0.5s0,0.4,0,0.6s0.1,0.4,0.2,0.5
						c0.1,0.2,0.3,0.3,0.4,0.4c0.2,0.1,0.4,0.2,0.6,0.2c0.2,0,0.4,0,0.6,0c0.2,0,0.4-0.1,0.5-0.2c0.2-0.1,0.3-0.2,0.4-0.4
						c0.1-0.2,0.2-0.3,0.2-0.5s0-0.4,0-0.6c-0.1-0.2-0.2-0.4-0.3-0.5l0.8-0.8c0.2,0.3,0.4,0.6,0.5,0.9s0.1,0.7,0,1
						c-0.1,0.4-0.2,0.7-0.4,1s-0.4,0.6-0.7,0.8c-0.3,0.2-0.6,0.3-0.9,0.4c-0.3,0.1-0.7,0.1-1.1,0c-0.4-0.1-0.7-0.2-1-0.4
						c-0.3-0.2-0.5-0.4-0.7-0.7c-0.2-0.3-0.3-0.6-0.4-0.9c-0.1-0.4-0.1-0.7,0-1.1c0-0.1,0.1-0.3,0.1-0.4c0.1-0.1,0.1-0.3,0.2-0.4
						c0.1-0.1,0.2-0.3,0.3-0.4s0.2-0.2,0.4-0.3L124,102.5z"/>
					<path class="st10" d="M122.9,100.8l0.1-1.1l4.9,0.5l-0.1,1.1L122.9,100.8z"/>
					<path class="st10" d="M123.1,97.2v-0.9l5-2.1v1.3l-1.1,0.4v2l1,0.4v1.2L123.1,97.2z M124.6,96.8l1.5,0.6v-1.2L124.6,96.8z"/>
					<path class="st10" d="M124,93.4l0.1,1.4l-1,0.1l-0.3-4l1-0.1l0.1,1.4l4-0.3L128,93L124,93.4z"/>
					<path class="st10" d="M122.8,90.5l-0.1-1.1l4.9-0.7l0.2,1.1L122.8,90.5z"/>
					<path class="st10" d="M125,88.2c-0.4,0.1-0.7,0.1-1.1,0c-0.3-0.1-0.6-0.2-0.9-0.4c-0.3-0.2-0.5-0.4-0.7-0.7
						c-0.2-0.3-0.3-0.6-0.4-1c-0.1-0.4-0.1-0.8-0.1-1.1c0-0.4,0.1-0.7,0.3-1s0.4-0.5,0.7-0.7s0.6-0.3,1-0.4s0.7-0.1,1.1,0
						c0.3,0.1,0.6,0.2,0.9,0.4c0.3,0.2,0.5,0.4,0.7,0.7c0.2,0.3,0.3,0.6,0.4,1c0.1,0.4,0.1,0.8,0.1,1.1c0,0.4-0.1,0.7-0.3,1
						s-0.4,0.5-0.7,0.7C125.8,88,125.4,88.2,125,88.2z M124.8,87.1c0.2-0.1,0.4-0.1,0.6-0.2c0.2-0.1,0.3-0.3,0.4-0.4
						c0.1-0.2,0.2-0.4,0.2-0.6s0-0.4,0-0.7c-0.1-0.2-0.1-0.4-0.3-0.6c-0.1-0.2-0.3-0.3-0.4-0.4c-0.2-0.1-0.4-0.2-0.6-0.2
						c-0.2,0-0.4,0-0.6,0c-0.2,0.1-0.4,0.1-0.6,0.2c-0.2,0.1-0.3,0.3-0.4,0.4c-0.1,0.2-0.2,0.4-0.2,0.6s0,0.4,0,0.7
						c0.1,0.2,0.1,0.4,0.3,0.6c0.1,0.2,0.3,0.3,0.4,0.4c0.2,0.1,0.4,0.2,0.6,0.2C124.4,87.2,124.6,87.2,124.8,87.1z"/>
					<path class="st10" d="M121.3,83l-0.5-1.4l2.5-3.2l0,0l-3.2,1.2l-0.4-1l4.6-1.7l0.5,1.4l-2.5,3.3l0,0l3.3-1.2l0.4,1L121.3,83z"
						/>
				</g>
				<g>
					<path class="st10" d="M31.2,81.6c0.1,0,0.3-0.1,0.4-0.3c0.1-0.1,0.2-0.3,0.2-0.4s0-0.2,0.1-0.2c0-0.1,0-0.2,0-0.3
						c0-0.1,0-0.2-0.1-0.2c0-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.3,0-0.4,0c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.3-0.4,0.5
						c-0.1,0.2-0.3,0.3-0.4,0.4c-0.2,0.1-0.3,0.2-0.5,0.3c-0.2,0-0.4,0-0.7-0.1s-0.5-0.2-0.6-0.4c-0.2-0.2-0.3-0.4-0.3-0.6
						c-0.2,0-0.2-0.2-0.2-0.5c0-0.2,0.1-0.5,0.2-0.7c0.1-0.3,0.3-0.6,0.5-0.8c0.2-0.2,0.4-0.4,0.7-0.6l0.5,1
						c-0.2,0.1-0.3,0.1-0.5,0.3c-0.1,0.1-0.2,0.3-0.3,0.5c0,0.1-0.1,0.2-0.1,0.3c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0.1,0.2
						c0,0.1,0.1,0.1,0.2,0.1c0.1,0.1,0.3,0,0.4,0c0.1-0.1,0.2-0.2,0.4-0.3c0.1-0.1,0.2-0.3,0.4-0.5c0.1-0.2,0.3-0.3,0.4-0.4
						c0.2-0.1,0.3-0.2,0.5-0.3c0.2-0.1,0.4,0,0.7,0.1c0.2,0.1,0.4,0.2,0.6,0.4c0.2,0.2,0.3,0.4,0.3,0.6c0.1,0.2,0.1,0.4,0.1,0.7
						c0,0.2-0.1,0.5-0.2,0.7c-0.1,0.3-0.2,0.5-0.4,0.7s-0.4,0.4-0.6,0.5L31.2,81.6z"/>
					<path class="st10" d="M27.8,86.7c-0.3-0.1-0.5-0.2-0.7-0.3c-0.2-0.2-0.4-0.3-0.5-0.6c-0.1-0.2-0.2-0.5-0.2-0.8s0-0.6,0.1-0.9
						s0.2-0.6,0.4-0.9c0.2-0.2,0.4-0.4,0.6-0.5c0.2-0.1,0.5-0.2,0.7-0.2c0.3,0,0.5,0,0.8,0.1l2.9,0.8l-0.4,1.1l-2.9-0.8
						c-0.1,0-0.3-0.1-0.4,0c-0.1,0-0.3,0.1-0.4,0.1c-0.1,0.1-0.2,0.1-0.3,0.3c-0.1,0.1-0.1,0.2-0.2,0.4c0,0.1-0.1,0.3,0,0.4
						c0,0.1,0.1,0.3,0.1,0.4c0.1,0.1,0.2,0.2,0.3,0.3c0.1,0.1,0.2,0.1,0.4,0.2l2.9,0.8l-0.3,0.9L27.8,86.7z"/>
					<path class="st10" d="M30.5,88.3l-0.3,1.9c0,0.3-0.1,0.5-0.2,0.7c-0.1,0.2-0.2,0.4-0.3,0.6s-0.3,0.3-0.5,0.3
						c-0.2,0.1-0.5,0.1-0.7,0c-0.3,0-0.5-0.1-0.7-0.3c-0.2-0.1-0.3-0.3-0.4-0.5c-0.1-0.2-0.1-0.4-0.2-0.6c0-0.2,0-0.5,0-0.7l0.1-0.8
						l-1.9-0.3l0.2-1.1L30.5,88.3z M28.3,89.1l-0.1,0.7c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0.1,0.3c0,0.1,0.1,0.1,0.1,0.2
						c0.1,0.1,0.2,0.1,0.3,0.1s0.2,0,0.3,0s0.2-0.1,0.2-0.2c0.1-0.1,0.1-0.2,0.1-0.3c0-0.1,0.1-0.2,0.1-0.3l0.1-0.6L28.3,89.1z"/>
					<path class="st10" d="M29.9,92.6L29.7,96l-1-0.1l0.1-2.3l-0.9-0.1l-0.1,2.2l-1-0.1l0.1-2.2l-1-0.1l-0.2,2.4l-1-0.1l0.2-3.6
						L29.9,92.6z"/>
					<path class="st10" d="M29.7,96.9l0.1,2c0,0.3,0,0.5,0,0.7s-0.1,0.4-0.2,0.6c-0.1,0.2-0.3,0.3-0.4,0.4c-0.2,0.1-0.4,0.2-0.7,0.2
						s-0.6-0.1-0.9-0.2c-0.3-0.2-0.4-0.4-0.5-0.8l-2,1.4v-1.3l1.9-1.1v-0.5l-2,0.1v-1.1L29.7,96.9z M27.7,98.1v0.7
						c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0.1,0.3c0,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.1,0.3,0.1s0.2,0,0.3-0.1s0.1-0.1,0.2-0.2
						c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0-0.3v-0.7H27.7z"/>
					<path class="st10" d="M29.8,100.8l0.2,1.3l-3,1.7l0,0l3.4,0.8l0.2,1.2l-5.2-1.4l-0.1-0.9L29.8,100.8z"/>
					<path class="st10" d="M30.6,106.1l0.2,1.1l-4.8,1.1l-0.2-1.1L30.6,106.1z"/>
					<path class="st10" d="M30.6,110.8c0.1-0.1,0.1-0.3,0.1-0.4c0-0.2,0-0.3,0-0.4s-0.1-0.2-0.1-0.2c0-0.1-0.1-0.1-0.2-0.2
						c-0.1-0.1-0.1-0.1-0.2-0.1c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2,0.1-0.3,0.3c0,0.1-0.1,0.3-0.1,0.5s0,0.4,0,0.6s0,0.4-0.1,0.6
						s-0.1,0.4-0.3,0.5c-0.1,0.2-0.3,0.3-0.6,0.4s-0.5,0.1-0.7,0c-0.2,0-0.4-0.1-0.6-0.3c-0.2-0.1-0.3-0.3-0.5-0.5
						c-0.1-0.2-0.2-0.4-0.3-0.7c-0.1-0.3-0.1-0.6-0.1-0.9s0.1-0.6,0.2-0.9l1,0.5c-0.1,0.2-0.2,0.3-0.2,0.5s0,0.4,0,0.6
						c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.1,0.1,0.2,0.1c0.1,0,0.2,0,0.2,0c0.1,0,0.2-0.1,0.3-0.3
						c0.1-0.1,0.1-0.3,0.1-0.5s0-0.4,0-0.6s0-0.4,0.1-0.6c0-0.2,0.1-0.4,0.3-0.5c0.1-0.2,0.3-0.3,0.6-0.4s0.5-0.1,0.7,0
						c0.2,0,0.4,0.1,0.6,0.3c0.2,0.1,0.3,0.3,0.5,0.5c0.1,0.2,0.2,0.4,0.3,0.7c0.1,0.3,0.1,0.5,0.1,0.8s-0.1,0.5-0.2,0.8L30.6,110.8
						z"/>
					<path class="st10" d="M29.8,112.5c0.4-0.2,0.7-0.2,1.1-0.2c0.3,0,0.7,0.1,1,0.2s0.6,0.3,0.8,0.6c0.2,0.3,0.4,0.6,0.6,0.9
						c0.2,0.4,0.2,0.7,0.2,1.1s0,0.7-0.2,1c-0.1,0.3-0.3,0.6-0.5,0.8c-0.2,0.2-0.5,0.4-0.9,0.6s-0.7,0.2-1.1,0.2
						c-0.3,0-0.7-0.1-1-0.2s-0.6-0.3-0.8-0.6c-0.2-0.3-0.4-0.6-0.6-0.9c-0.1-0.4-0.2-0.7-0.2-1.1s0-0.7,0.2-1
						c0.1-0.3,0.3-0.6,0.5-0.8C29.1,112.8,29.4,112.6,29.8,112.5z M30.2,113.5c-0.2,0.1-0.4,0.2-0.5,0.3c-0.1,0.1-0.3,0.3-0.3,0.5
						c-0.1,0.2-0.1,0.4-0.1,0.6s0,0.4,0.1,0.6s0.2,0.4,0.4,0.6c0.1,0.1,0.3,0.3,0.5,0.3c0.2,0.1,0.4,0.1,0.6,0.1
						c0.2,0,0.4-0.1,0.6-0.1c0.2-0.1,0.4-0.2,0.5-0.3c0.2-0.1,0.3-0.3,0.3-0.5c0.1-0.2,0.1-0.4,0.1-0.6s0-0.4-0.1-0.6
						s-0.2-0.4-0.4-0.6c-0.1-0.1-0.3-0.3-0.5-0.3c-0.2-0.1-0.4-0.1-0.6-0.1C30.6,113.4,30.4,113.5,30.2,113.5z"/>
					<path class="st10" d="M34.4,117l0.9,1.7c0.1,0.2,0.2,0.5,0.3,0.7s0.1,0.4,0.1,0.6c0,0.2-0.1,0.4-0.2,0.6
						c-0.1,0.2-0.3,0.3-0.6,0.5s-0.6,0.2-0.9,0.2c-0.3,0-0.6-0.2-0.8-0.5l-1.2,2.1l-0.6-1.2l1.2-1.9l-0.3-0.5l-1.7,1l-0.5-1
						L34.4,117z M33.1,119l0.3,0.6c0,0.1,0.1,0.2,0.2,0.3c0.1,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.1,0.3,0.1s0.2,0,0.3-0.1
						s0.2-0.1,0.2-0.2c0-0.1,0.1-0.2,0.1-0.3s0-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.3l-0.4-0.6L33.1,119z"/>
					<path class="st10" d="M37,123.6c0-0.1,0-0.3,0-0.4c0-0.2-0.1-0.3-0.2-0.4c0-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.1-0.1-0.2-0.2
						c-0.1,0-0.2-0.1-0.2-0.1c-0.1,0-0.2,0-0.2,0.1c-0.1,0.1-0.2,0.2-0.2,0.3s0,0.3,0.1,0.5s0.1,0.4,0.2,0.6
						c0.1,0.2,0.1,0.4,0.1,0.6c0,0.2,0,0.4-0.1,0.6c-0.1,0.2-0.2,0.4-0.5,0.5c-0.2,0.2-0.5,0.2-0.7,0.3c-0.2,0-0.4,0-0.7-0.1
						c-0.2-0.1-0.4-0.2-0.6-0.3c-0.2-0.2-0.4-0.3-0.5-0.6c-0.2-0.3-0.3-0.6-0.4-0.8c-0.1-0.3-0.1-0.6-0.1-0.9l1.1,0.2
						c-0.1,0.2-0.1,0.4,0,0.5c0,0.2,0.1,0.4,0.2,0.5c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0,0.2,0.1,0.2,0.1
						c0.1,0,0.2,0,0.2-0.1c0.1-0.1,0.2-0.2,0.2-0.3s0-0.3-0.1-0.5c0-0.2-0.1-0.4-0.2-0.6c-0.1-0.2-0.1-0.4-0.1-0.6
						c0-0.2,0-0.4,0.1-0.6c0.1-0.2,0.2-0.4,0.4-0.5c0.2-0.2,0.4-0.2,0.7-0.3c0.2,0,0.4,0,0.7,0.1c0.2,0.1,0.4,0.2,0.6,0.3
						c0.2,0.2,0.3,0.3,0.5,0.5s0.3,0.5,0.4,0.8c0.1,0.3,0.1,0.5,0.1,0.8L37,123.6z"/>
				</g>
				<polygon class="st13" points="120.2,72.6 120.9,73.9 122.3,74.1 121.3,75.1 121.5,76.5 120.2,75.9 118.9,76.5 119.2,75.1 
					118.1,74.1 119.6,73.9 				"/>
				<polygon class="st13" points="32.1,72.3 32.8,73.6 34.2,73.9 33.2,74.9 33.4,76.3 32.1,75.6 30.8,76.3 31.1,74.9 30,73.9 
					31.5,73.6 				"/>
				<g>
					<path class="st10" d="M35.4,67.1c-0.1,0-0.3,0.1-0.4,0.2c-0.1,0.1-0.2,0.2-0.3,0.3c0,0.1-0.1,0.1-0.1,0.2s0,0.2-0.1,0.2
						c0,0.1,0,0.2,0,0.2c0,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.2,0.1,0.4,0c0.1,0,0.3-0.1,0.4-0.2c0.1-0.1,0.3-0.2,0.4-0.4
						c0.2-0.1,0.3-0.2,0.5-0.3c0.2,0,0.4,0,0.6,0s0.4,0.1,0.7,0.2c0.2,0.2,0.4,0.3,0.5,0.5s0.2,0.4,0.2,0.6s0,0.4-0.1,0.7
						c-0.1,0.2-0.2,0.4-0.3,0.7c-0.2,0.3-0.4,0.5-0.6,0.7c-0.2,0.2-0.5,0.3-0.8,0.4l-0.3-1.1c0.2,0,0.4-0.1,0.5-0.2
						c0.2-0.1,0.3-0.2,0.4-0.4c0-0.1,0.1-0.2,0.1-0.2c0-0.1,0-0.2,0.1-0.3c0-0.1,0-0.2,0-0.2c0-0.1-0.1-0.1-0.2-0.2
						c-0.1-0.1-0.3-0.1-0.4-0.1c-0.1,0-0.3,0.1-0.4,0.2c-0.1,0.1-0.3,0.2-0.5,0.4c-0.2,0.1-0.3,0.2-0.5,0.3
						c-0.2,0.1-0.4,0.1-0.6,0.1c-0.2,0-0.4-0.1-0.6-0.2c-0.2-0.1-0.4-0.3-0.5-0.5s-0.2-0.4-0.2-0.6s0-0.4,0.1-0.7
						c0.1-0.2,0.2-0.4,0.3-0.6c0.2-0.2,0.3-0.4,0.5-0.6s0.5-0.3,0.7-0.4L35.4,67.1z"/>
					<path class="st10" d="M40.4,63.6c0.2,0.2,0.4,0.4,0.5,0.6c0.1,0.2,0.2,0.5,0.2,0.7s0,0.5-0.1,0.8s-0.2,0.5-0.4,0.8
						s-0.4,0.5-0.7,0.6c-0.2,0.1-0.5,0.2-0.7,0.2c-0.3,0-0.5,0-0.7-0.1S38,67,37.8,66.8l-2.3-1.9l0.7-0.8l2.3,1.9
						c0.1,0.1,0.2,0.2,0.4,0.2c0.1,0,0.3,0.1,0.4,0.1c0.1,0,0.3,0,0.4-0.1c0.1-0.1,0.2-0.1,0.3-0.3c0.1-0.1,0.2-0.2,0.2-0.4
						c0-0.1,0-0.3,0-0.4s-0.1-0.3-0.1-0.4c-0.1-0.1-0.2-0.2-0.3-0.3l-2.4-1.9l0.7-0.8L40.4,63.6z"/>
					<path class="st10" d="M39,60.6l1.3-1.3c0.2-0.2,0.3-0.3,0.5-0.4s0.4-0.2,0.5-0.3c0.2-0.1,0.4-0.1,0.6,0c0.2,0,0.4,0.2,0.6,0.3
						c0.2,0.2,0.3,0.4,0.3,0.7c0,0.2,0,0.5-0.2,0.7l0,0c0.1-0.1,0.3-0.2,0.4-0.2s0.3-0.1,0.4-0.1s0.3,0,0.4,0.1s0.3,0.1,0.4,0.3
						c0.2,0.2,0.3,0.4,0.3,0.6s0,0.4,0,0.6c-0.1,0.2-0.1,0.4-0.3,0.6c-0.1,0.2-0.3,0.4-0.4,0.5l-1.4,1.4L39,60.6z M41.2,61.3
						l0.6-0.6c0.1-0.1,0.1-0.1,0.2-0.2c0-0.1,0.1-0.1,0.1-0.2s0-0.2,0-0.2c0-0.1-0.1-0.1-0.1-0.2c-0.1-0.1-0.1-0.1-0.2-0.1
						c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2,0.1-0.2,0.1c-0.1,0.1-0.1,0.1-0.2,0.2l-0.8,0.4L41.2,61.3z M42.6,62.7l0.7-0.7
						c0.1-0.1,0.1-0.1,0.2-0.2c0-0.1,0.1-0.2,0.1-0.2c0-0.1,0-0.2,0-0.2c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.1-0.2-0.1-0.3-0.1
						c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0.1-0.3,0.1c-0.1,0.1-0.2,0.1-0.2,0.2l-0.6,0.5L42.6,62.7z"/>
					<path class="st10" d="M42.1,57.4l0.9-0.7l2.6,2l0,0l-1.1-3.1l0.8-0.6l2.8,1.9l0,0l-1.3-3.1l0.9-0.7l1.9,4.8l-0.8,0.6l-3-2l0,0
						l1.3,3.4l-0.7,0.6L42.1,57.4z"/>
					<path class="st10" d="M49.8,51.9l0.8-0.5l4.4,3.1l-1.2,0.7l-0.9-0.7l-1.6,1l0.2,1.1l-1,0.6L49.8,51.9z M50.8,52.9l0.3,1.6
						l1-0.6L50.8,52.9z"/>
					<path class="st10" d="M55,52.3l-2.9-1.8l1.2-0.6l1.7,1.2l0.2-2.1l1.2-0.6l-0.5,3.3l0.9,1.9l-1,0.5L55,52.3z"/>
					<path class="st10" d="M97.9,50.8c0-0.1-0.1-0.3-0.2-0.4c-0.1-0.1-0.2-0.2-0.4-0.3c-0.1,0-0.2-0.1-0.2-0.1c-0.1,0-0.2,0-0.3,0
						c-0.1,0-0.2,0-0.2,0.1c-0.1,0-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.3,0,0.4c0.1,0.1,0.2,0.2,0.3,0.4c0.1,0.1,0.3,0.2,0.4,0.4
						c0.2,0.1,0.3,0.3,0.4,0.4c0.1,0.2,0.2,0.3,0.2,0.5s0,0.4-0.1,0.7c-0.1,0.2-0.3,0.4-0.4,0.6c-0.2,0.1-0.4,0.2-0.6,0.3
						c-0.2,0-0.4,0.1-0.7,0c-0.2,0-0.5-0.1-0.7-0.2c-0.3-0.1-0.5-0.3-0.7-0.5s-0.4-0.5-0.5-0.8l1.1-0.4c0,0.2,0.1,0.3,0.2,0.5
						c0.1,0.1,0.3,0.3,0.4,0.3S95.9,53,96,53c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0,0.2-0.1c0.1,0,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.3,0-0.4
						c-0.1-0.1-0.2-0.3-0.3-0.4c-0.1-0.1-0.3-0.3-0.4-0.4c-0.2-0.1-0.3-0.3-0.4-0.5s-0.2-0.3-0.2-0.5s0-0.4,0.1-0.7
						c0.1-0.2,0.3-0.4,0.4-0.6c0.2-0.1,0.4-0.2,0.6-0.3c0.2,0,0.4-0.1,0.7,0c0.2,0,0.5,0.1,0.7,0.2c0.3,0.1,0.5,0.3,0.7,0.4
						c0.2,0.2,0.4,0.4,0.5,0.7L97.9,50.8z"/>
					<path class="st10" d="M102.1,55.3c-0.2,0.3-0.3,0.5-0.5,0.6c-0.2,0.2-0.4,0.3-0.7,0.3c-0.2,0.1-0.5,0.1-0.8,0
						c-0.3,0-0.6-0.2-0.9-0.3c-0.3-0.2-0.5-0.4-0.7-0.6s-0.3-0.5-0.3-0.7c-0.1-0.2-0.1-0.5,0-0.8c0-0.3,0.1-0.5,0.3-0.8l1.5-2.6
						l0.9,0.6l-1.5,2.6c-0.1,0.1-0.1,0.3-0.2,0.4c0,0.1,0,0.3,0,0.4c0,0.1,0.1,0.2,0.2,0.4c0.1,0.1,0.2,0.2,0.3,0.3
						c0.1,0.1,0.2,0.1,0.4,0.1c0.1,0,0.3,0,0.4,0s0.2-0.1,0.3-0.2c0.1-0.1,0.2-0.2,0.3-0.3l1.5-2.6l0.9,0.6L102.1,55.3z"/>
					<path class="st10" d="M104.8,53.5l1.5,1.1c0.2,0.2,0.4,0.3,0.5,0.5c0.2,0.2,0.3,0.4,0.3,0.5c0.1,0.2,0.1,0.4,0,0.6
						c0,0.2-0.1,0.4-0.3,0.7c-0.2,0.3-0.5,0.5-0.8,0.6s-0.6,0.1-0.9-0.1l-0.2,2.4l-1-0.8l0.4-2.2l-0.4-0.3l-1.2,1.6l-0.9-0.7
						L104.8,53.5z M104.5,55.8l0.5,0.4c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.3-0.2
						c0.1-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2-0.1-0.3c0-0.1-0.1-0.2-0.2-0.2c-0.1-0.1-0.1-0.1-0.2-0.2l-0.6-0.4L104.5,55.8z"/>
					<path class="st10" d="M108.7,56.5l2.5,2.2l-0.7,0.7l-1.6-1.5l-0.7,0.7l1.5,1.4l-0.7,0.7l-1.5-1.4l-1.3,1.4l-0.8-0.7L108.7,56.5
						z"/>
					<path class="st10" d="M112.8,60.5l0.6,0.7l-2.2,4.9l-0.8-0.9l0.5-1l-1.3-1.4l-1.1,0.4l-0.8-0.9L112.8,60.5z M112.1,61.7
						l-1.5,0.6l0.8,0.9L112.1,61.7z"/>
					<path class="st10" d="M116.3,66.7c0-0.2,0-0.3,0-0.5s-0.1-0.3-0.2-0.5c-0.1-0.2-0.3-0.3-0.4-0.4c-0.2-0.1-0.3-0.1-0.5-0.2
						c-0.2,0-0.4,0-0.6,0.1c-0.2,0.1-0.4,0.2-0.6,0.3c-0.2,0.1-0.3,0.3-0.4,0.5c-0.1,0.2-0.2,0.4-0.2,0.5c0,0.2,0,0.4,0,0.6
						s0.1,0.4,0.2,0.5c0.1,0.2,0.3,0.3,0.5,0.4s0.4,0.1,0.6,0.1v1.1c-0.4,0-0.7-0.1-1-0.2c-0.3-0.2-0.6-0.4-0.8-0.7
						c-0.2-0.3-0.4-0.6-0.5-1c-0.1-0.3-0.1-0.7-0.1-1s0.1-0.6,0.3-0.9s0.4-0.6,0.7-0.8c0.3-0.2,0.6-0.4,1-0.5c0.3-0.1,0.7-0.1,1,0
						c0.3,0.1,0.6,0.2,0.9,0.4c0.3,0.2,0.6,0.4,0.8,0.7c0.1,0.1,0.2,0.2,0.2,0.4c0.1,0.1,0.1,0.3,0.2,0.4c0,0.2,0.1,0.3,0.1,0.5
						s0,0.3-0.1,0.5L116.3,66.7z"/>
					<path class="st10" d="M118.4,67.6l1.7,2.9l-0.9,0.5l-1.2-1.9l-0.8,0.5l1.1,1.8l-0.9,0.5l-1.1-1.8l-0.9,0.5l1.2,2l-0.9,0.5
						l-1.8-3L118.4,67.6z"/>
				</g>
				<g>
					<path class="st10" d="M70.6,41.6c-0.1-0.1-0.2-0.2-0.4-0.3c-0.2-0.1-0.3-0.1-0.5-0.1c-0.1,0-0.2,0-0.3,0
						c-0.1,0-0.2,0.1-0.3,0.1c-0.1,0-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.2,0.1,0.3,0.2,0.4c0.1,0.1,0.3,0.2,0.5,0.2
						c0.2,0.1,0.4,0.1,0.6,0.2c0.2,0.1,0.4,0.1,0.6,0.3c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.2,0.2,0.4,0.2,0.8c0,0.3-0.1,0.6-0.2,0.8
						s-0.3,0.4-0.5,0.6c-0.2,0.1-0.4,0.3-0.7,0.3c-0.3,0.1-0.5,0.1-0.8,0.1s-0.7-0.1-1-0.2c-0.3-0.1-0.6-0.3-0.8-0.5l0.8-0.9
						c0.1,0.2,0.3,0.3,0.5,0.4c0.2,0.1,0.4,0.1,0.6,0.1c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.3-0.1c0.1,0,0.1-0.1,0.2-0.2
						c0.1-0.1,0.1-0.2,0.1-0.3c0-0.2-0.1-0.3-0.2-0.4c-0.1-0.1-0.3-0.2-0.5-0.2c-0.2-0.1-0.4-0.1-0.6-0.2c-0.2-0.1-0.4-0.2-0.6-0.3
						s-0.3-0.3-0.5-0.4c-0.1-0.2-0.2-0.4-0.2-0.7s0.1-0.5,0.2-0.8c0.1-0.2,0.3-0.4,0.5-0.5s0.4-0.3,0.7-0.3c0.2-0.1,0.5-0.1,0.8-0.1
						s0.6,0,0.9,0.1s0.5,0.2,0.8,0.4L70.6,41.6z"/>
					<path class="st10" d="M74.9,41.6c-0.1-0.1-0.2-0.2-0.4-0.3c-0.2-0.1-0.3-0.1-0.5-0.1c-0.1,0-0.2,0-0.3,0
						c-0.1,0-0.2,0.1-0.3,0.1c-0.1,0-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.2,0.1,0.3,0.2,0.4c0.1,0.1,0.3,0.2,0.5,0.2
						c0.2,0.1,0.4,0.1,0.6,0.2c0.2,0.1,0.4,0.1,0.6,0.3c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.2,0.2,0.4,0.2,0.8c0,0.3-0.1,0.6-0.2,0.8
						s-0.3,0.4-0.5,0.6c-0.2,0.1-0.4,0.3-0.7,0.3c-0.3,0.1-0.5,0.1-0.8,0.1s-0.7-0.1-1-0.2c-0.3-0.1-0.6-0.3-0.8-0.5l0.8-0.9
						c0.1,0.2,0.3,0.3,0.5,0.4c0.2,0.1,0.4,0.1,0.6,0.1c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.3-0.1c0.1,0,0.1-0.1,0.2-0.2
						c0-0.1,0.1-0.2,0.1-0.3c0-0.2-0.1-0.3-0.2-0.4c-0.1-0.1-0.3-0.2-0.5-0.2c-0.2-0.1-0.4-0.1-0.6-0.2c-0.2-0.1-0.4-0.2-0.6-0.3
						s-0.3-0.3-0.5-0.4c-0.1-0.2-0.2-0.4-0.2-0.7s0.1-0.5,0.2-0.8c0.1-0.2,0.3-0.4,0.5-0.5s0.4-0.3,0.7-0.3c0.2-0.1,0.5-0.1,0.8-0.1
						s0.6,0,0.9,0.1s0.5,0.2,0.8,0.4L74.9,41.6z"/>
					<path class="st10" d="M79.3,41.6c-0.1-0.1-0.2-0.2-0.4-0.3c-0.2-0.1-0.3-0.1-0.5-0.1c-0.1,0-0.2,0-0.3,0
						c-0.1,0-0.2,0.1-0.3,0.1c-0.1,0-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.2,0.1,0.3,0.2,0.4c0.1,0.1,0.3,0.2,0.5,0.2
						c0.2,0.1,0.4,0.1,0.6,0.2c0.2,0.1,0.4,0.1,0.6,0.3c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.2,0.2,0.4,0.2,0.8c0,0.3-0.1,0.6-0.2,0.8
						s-0.3,0.4-0.5,0.6c-0.2,0.1-0.4,0.3-0.7,0.3c-0.2,0.1-0.5,0.1-0.8,0.1s-0.7-0.1-1-0.2c-0.3-0.1-0.6-0.3-0.8-0.5l0.8-0.9
						c0.1,0.2,0.3,0.3,0.5,0.4c0.2,0.1,0.4,0.1,0.6,0.1c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.3-0.1c0.1,0,0.1-0.1,0.2-0.2
						c0-0.1,0.1-0.2,0.1-0.3c0-0.2-0.1-0.3-0.2-0.4c-0.1-0.1-0.3-0.2-0.5-0.2c-0.2-0.1-0.4-0.1-0.6-0.2c-0.2-0.1-0.4-0.2-0.6-0.3
						s-0.3-0.3-0.5-0.4c-0.1-0.2-0.2-0.4-0.2-0.7s0.1-0.5,0.2-0.8c0.1-0.2,0.3-0.4,0.5-0.5s0.4-0.3,0.7-0.3c0.2-0.1,0.5-0.1,0.8-0.1
						s0.6,0,0.9,0.1s0.5,0.2,0.8,0.4L79.3,41.6z"/>
					<path class="st10" d="M82.8,40.3h1l2.3,5.4h-1.3l-0.5-1.1h-2.1l-0.5,1.1h-1.3L82.8,40.3z M83.2,41.9l-0.7,1.7h1.3L83.2,41.9z"
						/>
				</g>
				<g>
					<path class="st10" d="M66.1,143.2h1.7l2.3,3.7l0,0v-3.7h1.2v5.4h-1.5l-2.3-3.8l0,0v3.8h-1.2v-5.4H66.1z"/>
					<path class="st10" d="M72.2,148c0-0.1,0-0.2,0.1-0.3c0-0.1,0.1-0.2,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1s0.2-0.1,0.3-0.1
						c0.1,0,0.2,0,0.3,0.1c0.1,0,0.2,0.1,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2c0,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
						c0,0.1-0.1,0.2-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1s-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3-0.1c-0.1,0-0.2-0.1-0.2-0.1
						c-0.1-0.1-0.1-0.1-0.1-0.2C72.2,148.2,72.2,148.1,72.2,148z"/>
					<path class="st10" d="M75.9,146.3l-2-3.1h1.5l1.1,2l1.2-2h1.4l-2,3.1v2.3h-1.2V146.3z"/>
					<path class="st10" d="M78.5,148c0-0.1,0-0.2,0.1-0.3c0-0.1,0.1-0.2,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1s0.2-0.1,0.3-0.1
						c0.1,0,0.2,0,0.3,0.1c0.1,0,0.2,0.1,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2c0,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
						c0,0.1-0.1,0.2-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1s-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3-0.1c-0.1,0-0.2-0.1-0.2-0.1
						c-0.1-0.1-0.1-0.1-0.1-0.2C78.6,148.2,78.5,148.1,78.5,148z"/>
					<path class="st10" d="M84.4,144.6c-0.1-0.1-0.3-0.3-0.4-0.3c-0.2-0.1-0.4-0.1-0.6-0.1s-0.4,0-0.6,0.1s-0.4,0.2-0.5,0.4
						S82,145,82,145.2c-0.1,0.2-0.1,0.4-0.1,0.7s0,0.5,0.1,0.7s0.2,0.4,0.3,0.5c0.1,0.2,0.3,0.3,0.5,0.4c0.2,0.1,0.4,0.1,0.6,0.1
						s0.5,0,0.6-0.2c0.2-0.1,0.3-0.2,0.5-0.4l1,0.7c-0.2,0.3-0.5,0.6-0.9,0.7c-0.3,0.2-0.7,0.2-1.1,0.2s-0.8-0.1-1.2-0.2
						c-0.4-0.1-0.7-0.3-0.9-0.6c-0.3-0.2-0.5-0.5-0.6-0.9s-0.2-0.7-0.2-1.2c0-0.4,0.1-0.8,0.2-1.2c0.1-0.3,0.3-0.6,0.6-0.9
						c0.3-0.2,0.6-0.4,0.9-0.6c0.4-0.1,0.7-0.2,1.2-0.2c0.2,0,0.3,0,0.5,0s0.3,0.1,0.5,0.1c0.2,0.1,0.3,0.1,0.5,0.2
						c0.1,0.1,0.3,0.2,0.4,0.4L84.4,144.6z"/>
					<path class="st10" d="M86,148c0-0.1,0-0.2,0.1-0.3c0-0.1,0.1-0.2,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.1s0.2-0.1,0.3-0.1
						c0.1,0,0.2,0,0.3,0.1c0.1,0,0.2,0.1,0.2,0.1c0.1,0.1,0.1,0.1,0.1,0.2c0,0.1,0.1,0.2,0.1,0.3s0,0.2-0.1,0.3
						c0,0.1-0.1,0.2-0.1,0.2c-0.1,0.1-0.1,0.1-0.2,0.1s-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3-0.1c-0.1,0-0.2-0.1-0.2-0.1
						c-0.1-0.1-0.1-0.1-0.1-0.2C86,148.2,86,148.1,86,148z"/>
				</g>
				<g>
					<g>
						<defs>
							<circle id="SVGID_11_" cx="76.2" cy="96" r="42.4"/>
						</defs>
						<clipPath id="SVGID_12_">
							<use xlink:href="#SVGID_11_"  style="overflow:visible;"/>
						</clipPath>
						<g class="st14">
							<path class="st0" d="M44.9,96.4l-0.7,2v7.8l-0.8,0.5v5.1l-0.7,4l0.5,1.2v0.4l-0.5,0.3l0.1,0.8l1.1,0.4H45l0.9,0.2l2,0.4h3.2
								v1v1.2l-0.3,0.8v1.1h0.6l1.3,0.2l0.4-0.3l0.2-0.6l0.5-1.4l0.4-1.3l0.6-0.4h2.1h4.8l3,0.1c0,0,1.5-0.1,2.6-0.4
								c1.1-0.3,9-2.3,9-2.3l0.3-1h0.8l0.5,0.1v0.4l0.9-0.1v-14.1l-14-7.2l-13-0.5L44.9,96.4z"/>
							<g>
								<path class="st10" d="M72.8,114.5l4.9,0.1c0-0.3,0-0.7,0-1.2l-4.9-0.8C72.8,113.3,72.8,114,72.8,114.5z"/>
								<path class="st10" d="M67.7,94.3c-1.9-0.9-8.4-1.7-14-1.7c-10,0-10,4.5-10,7.2c0,0.8,0,5.3,0,5.3s-0.5,0-0.8,0
									c-0.3,0-0.6,0.1-0.6,0.7s-0.2,8.4-0.2,9.2c0,0.9-0.2,1.6,1.1,1.6c0.2,0,0.5,0,1,0c0.1-0.1,0.5-0.4,0.5-1.1
									c0-0.8,0-16.6,0-16.6s0.6-0.1,0.6,0s0,0.4,0,0.4l4.5-1.1v7.7l-4.6,0.2v1.5c0,0-0.3-0.1-0.3,0.3s-0.1,7.8,0.1,7.9
									s0.1,0.6,0,0.8c3.8,0.1,11.9,0.3,13.1,0.3c1.5,0,4.5-0.1,6-0.1c0.1,0,0.1,0,0.2,0v-10.9c0,0-4.2-0.3-4.3-0.3
									c-0.2,0-0.4-0.1-0.4-0.6s0-5.2,0-5.8c0-0.9,0.5-1.1,1.1-1.1s3.9,0.5,4.1,0.6S65,99,65,99.4c0,0.4-0.2,16.3-0.3,17.4
									c1.1,0,1.3-0.1,1.4-0.3c0-0.3,0.1-17.5,0.1-18.1c0-0.7,0.7-0.2,0.7-0.2s-0.2,17.9-0.2,19.7s0.4,1.5,0.4,1.5s4.9-1.2,7.2-1.7
									c2.3-0.5,2-1,2.1-1.1c0.1-0.2,0.6-0.1,1-0.1s0.4,0.4,0.4,0.4h0.6c0,0,0.1-0.4,0.9-0.4c0.7,0.1,0.7-0.4,0.7-0.4
									c0.2-0.7,0.5-1.7,0.6-2.6c0,0.1-0.1,0.2-0.2,0.2l-1.8-0.3c0,0.4,0,0.8,0,1.1h1.5c0.1,0,0.1,0.1,0.1,0.2s-0.1,0.2-0.1,0.2
									h-1.5c0,0.3,0,0.5,0,0.5c0,0.1-0.1,0.1-0.2,0.2c-0.1,0-0.4,0.1-0.5,0.1s-0.1-0.1-0.1-0.3c0,0,0-0.2,0-0.4l-4.9-0.1
									c0,1,0,1.7,0,1.8c0,0.1-0.1,0.3-0.3,0.3c-0.2,0-0.7,0.1-0.8,0.1c-0.1,0-0.2-0.2-0.2-0.5c0-0.1,0-0.7,0-1.7l-2.8-0.1
									c-0.1,0-0.1-0.1-0.1-0.2s0.1-0.2,0.1-0.2l2.8,0.1c0-0.6,0-1.3,0-2l-2.8-0.5c-0.1,0-0.1-0.1-0.1-0.3c0-0.1,0.1-0.3,0.2-0.2
									l2.7,0.4c0.1-3.6,0.2-8,0.2-8.3c0-0.4,0-1.2,0.5-0.9c0.4,0.3,0.7,0.7,0.8,0.8c0.1,0.1,0.1,0.3,0.1,0.5s-0.1,4.6-0.2,8.1
									l4.9,0.8c0-2,0.1-4.5,0.1-4.7s0-0.7,0.3-0.5c0.2,0.2,0.4,0.4,0.5,0.5s0.1,0.1,0.1,0.3c0,0.1-0.1,2.6-0.1,4.6l1.8,0.3
									c0.1,0,0.1,0.1,0.1,0.3c0.4-2.2-1.6-9.7-1.6-9.7L67.7,94.3z M79.4,109.4c0-0.3,0.2-0.2,0.2-0.2c0.1,0.1,0.8,0.7,0.9,0.8
									c0.1,0.1,0.1,0.2,0.1,0.4c0,0.2,0,1.8,0,2.1c0,0.3,0,0.4-0.1,0.4s-0.8-0.5-1-0.5c-0.1,0-0.1-0.1-0.1-0.3
									S79.4,109.6,79.4,109.4z M75.7,106.5c0-0.5,0.3-0.4,0.4-0.3s0.3,0.3,0.5,0.5s0.1,0.3,0.1,0.6c0,0.3,0,3.2,0,3.7
									s-0.1,0.7-0.2,0.7s-0.4-0.2-0.7-0.2c-0.2-0.1-0.2-0.2-0.2-0.5C75.7,110.7,75.7,106.9,75.7,106.5z M74,104.9
									c0-0.5,0.3-0.5,0.5-0.3c0.2,0.1,0.4,0.4,0.6,0.6s0.1,0.4,0.1,0.7c0,0.3,0,3.7,0,4.3c0,0.6-0.1,0.8-0.2,0.8s-0.5-0.2-0.8-0.3
									c-0.2-0.1-0.2-0.3-0.2-0.6S74,105.5,74,104.9z M68.6,100.8c0-0.8,0.4-0.7,0.7-0.5c0.2,0.2,0.6,0.6,0.9,0.9s0.2,0.6,0.2,1
									s0,5.5,0,6.4s-0.1,1.1-0.3,1.2c-0.2,0-0.8-0.3-1.1-0.4c-0.3-0.1-0.3-0.4-0.3-0.9C68.6,108.1,68.6,101.6,68.6,100.8z
									 M49.1,115.3c-0.8,0-1.4-0.6-1.4-1.4s0.6-1.4,1.4-1.4s1.4,0.6,1.4,1.4C50.5,114.7,49.8,115.3,49.1,115.3z M49.1,111.3
									c-0.8,0-1.4-0.6-1.4-1.4s0.6-1.4,1.4-1.4s1.4,0.6,1.4,1.4C50.5,110.7,49.9,111.3,49.1,111.3z M57.4,106.8
									c0,0.4-0.6,0.4-0.6,0.4s-3.3,0-3.8,0s-0.7-0.5-0.7-0.8s0-5.2,0-5.7s0.1-0.7,0.5-0.8c0.4,0,4.2-0.1,4.2-0.1s0.4,0.1,0.4,0.8
									C57.4,100.8,57.4,106.8,57.4,106.8z M60.2,115.4c-0.8,0-1.4-0.6-1.4-1.4s0.6-1.4,1.4-1.4s1.4,0.6,1.4,1.4
									S60.9,115.4,60.2,115.4z M60.2,108.6c0.8,0,1.4,0.6,1.4,1.4s-0.6,1.4-1.4,1.4s-1.4-0.6-1.4-1.4
									C58.8,109.3,59.4,108.6,60.2,108.6z"/>
								<path class="st10" d="M65.6,119.2c-0.3,0-1.4,0.1-3.7,0.2c-2.3,0-3.7-0.1-4-0.2c-0.3,0-0.3-0.4,0-0.3c0.3,0,1.8,0.1,3.8,0.1
									s3.5-0.2,3.8-0.2s0.3-0.1,0.2-0.3c0-0.1,0.1-0.2-0.2-0.2c-0.3,0-2.2,0.2-3.8,0.2c-1.6,0-4.6-0.1-5.2-0.1s-0.5-0.4,0.1-0.3
									c0.6,0,1.9,0,2.2,0c0.2,0,0.1-0.3-0.1-0.3c-0.5,0-1.2,0-4.9-0.1c-2.1-0.1-3.8-0.1-4.2-0.2c-0.4,0-0.7-0.1-0.7,0.1
									s0.2,0.1,0.3,0.2c0.1,0,0.1,0.1,0,0.2s-0.6,0.1-1.8,0.1c-1,0-2-0.1-2.9-0.2c-0.8-0.1-1.4-0.2-1.8-0.3
									c-0.3-0.1-0.4,0.1-0.4,0.4c0,0.2,0.4,0.2,1,0.3c0.9,0.1,1.7,0.2,3,0.3c1.5,0.1,2,0.1,2.2,0.1c0.3,0,0.3,0.3,0.1,0.3
									c-0.2,0-0.8,0-2.1-0.1c-1.5-0.1-2.7-0.2-3.5-0.4c-0.4-0.1-0.6-0.1-0.6,0.2s0.3,0.3,1.3,0.5c0.9,0.1,1.3,0.2,1.3,0.2
									c0.2,0.4,0.4,0.4,1,0.5c1.3,0.2,3.1,0.3,4.7,0.4c0.1,0,0.1,0.2,0,0.3c-0.1,0.3-0.7,1.5-0.6,1.9c0,0.4,0.1,1.8,0.1,2.6
									c0.1,0.8,0.4,0.8,0.8,0.8s1.3,0,1.8,0s0.5-0.4,0.5-0.6c0-0.3,0.1-1.7,0-2.1c0-0.4,0.2-0.7,0.6-1.5c0.3-0.8,0.7-1.2,0.7-1.2
									s2,0.1,5.4,0c4-0.1,4.8-0.2,5.6-0.3c0.5-0.1,0.3-0.4,0.3-0.6C66,119.2,66,119.1,65.6,119.2z M53.2,122.9
									c0,0.3-0.2,0.3-0.3,0.3c-0.1,0-0.8,0-1.3,0s-0.6,0-0.7,0c-0.1-0.1-0.1-0.8,0.1-0.8c0.5,0,1.4,0,1.8,0
									C53.2,122.3,53.2,122.6,53.2,122.9z M53.3,121c-0.1,0.6-0.2,0.9-0.4,1c-0.2,0-1,0-1.3,0c-0.2,0-0.3-0.5-0.2-0.7
									c0.1-0.2,0.4-0.8,0.5-1.1s0.3-0.5,0.8-0.5c0.6,0,0.8,0,0.8,0.2C53.5,120.1,53.4,120.3,53.3,121z"/>
								<path class="st10" d="M44.9,117.5c0.2,0,0.2,0,0.2-0.2c0-0.1,0.1-0.3-0.1-0.3c-0.1,0-0.8,0-1.2-0.1c-0.5,0-0.7,0-0.8,0.1
									c0,0,0,0.2,0.4,0.2C44,117.4,44.7,117.5,44.9,117.5z"/>
								<path class="st0" d="M80.5,113.6C80.5,113.6,80.5,113.7,80.5,113.6C80.5,113.7,80.5,113.7,80.5,113.6L80.5,113.6z"/>
							</g>
						</g>
					</g>
					<g>
						<defs>
							<circle id="SVGID_13_" cx="76.2" cy="96" r="42.4"/>
						</defs>
						<clipPath id="SVGID_14_">
							<use xlink:href="#SVGID_13_"  style="overflow:visible;"/>
						</clipPath>
						<g class="st15">
							<g>
								<g>
									<path class="st10" d="M78.1,94.2c0,0-4.3,4.5-4.6,17.2c0,0.6,0.3,0.8,0.3,1.4c0,0.4-0.4,0.5-0.4,0.9c0,1.3,0.1,2,0.2,3.6
										c0,0-1.8,3,3.8,4.2c0,0,13.4,1.9,15.8,1.8c0,0,2.3,0.2,5.2-1.1c2.9-1.3,4.2-1.2,4.2-1.2l1.4,0.5c0,0,1,0.1,1.2,0.1
										s0.8-0.2,0.8-0.2l0.5-0.9l0.4-0.9l6.8-2.1l0.8,0.8h1.2l0.7-0.9l0.6-1.3l0.5-0.4l0.5-0.1l0.1-1.4l-0.2-8.7
										c0,0-0.6-0.5-2-1.4S105.6,97,105.6,97l-5.8-2.6l-2.5-1.3H85.4l-5.6,0.4L78.1,94.2z"/>
									<g>
										<g>
											<g id="OA7rZa_10_">
												<g>
													<path class="st0" d="M95,121.6c-3,0.1-6,0-8.9-0.1c-2.2-0.1-4.5-0.4-6.7-0.6c-1.4-0.2-2.8-0.2-4.2-0.6
														c-1.3-0.4-1.7-1.1-1.4-2.5c0.9,0.1,1.9,0.2,2.8,0.2c2.5,0.2,4.9,0.5,7.4,0.7c0,0,9.6,1.2,10.2,1
														c0.6-0.3,0.6-1.3,0.2-1.2c-0.4,0-8.8-0.4-8.8-0.4s-6.8-0.4-7.7-0.4c-0.7,0-1.4-0.1-2.1-0.2c-0.6-0.1-1.3-0.2-1.9-0.2
														c0,0-0.5-0.1-0.6,0.2c-0.6,1.7,0.3,3.3,2.1,3.7c3.2,0.6,6.5,1.1,9.7,1.6c2.4,0.3,4.8,0.4,7.2,0.7
														c1.5,0.2,3.1,0.1,4.5-0.4c1.5-0.5,2.9-1.1,4.2-1.6c0.5,0.4,0.8,0.9,1.3,0.9c0.9,0.1,1.9,0.1,2.9,0
														c0.8-0.1,1.4-0.7,1.6-1.5c0.2-0.8,0.7-1.1,1.4-1.3c1.6-0.5,3.2-1.1,4.9-1.7c0.4,1,1.7,1.4,2.5,0.7
														c0.6-0.6,1.4-1.1,1.6-2c0-0.2,0.2-0.3,0.3-0.5l0.1,0.1c0,0.2-0.1,0.3-0.2,0.6c0.9-1,0.9-2,0.9-3
														c-0.2-3-0.3-4.5-0.6-7.4h-0.1l0.3,4.8c-0.2,0-0.3,0-0.3,0c-3.2-0.7-6.3-1.4-9.5-2.1v0.6c1.7,0.2,8.1,1.6,9.8,1.9
														c0,0.8,0.1,1.6,0.1,2.3c0,1.5,0,1.5-1.4,1.7c0,0-0.1,0,0,0c-0.1-0.7-0.2-1.4-0.4-2c-0.1-0.3-0.4-0.6-0.7-0.6
														c-0.2,0-0.6,0.3-0.7,0.6c-0.3,0.6-0.4,1.3-0.6,2c-0.1,0.4-0.2,0.5-0.6,0.5c-1,0.2-2,0.4-3,0.5c-1,0.2-2,0.3-3,0.5v-0.8
														c1.7-0.3,3.4-0.6,5.1-0.9v-0.1c-1.7,0.2-3.4,0.3-5.1,0.5c-0.2-0.7-0.3-1.4-0.5-2.1c-0.3-1-0.9-1.9-2-2
														c-1.2-0.1-1.8,0.8-2.2,1.7c-0.7,1.5-0.9,3.1-0.8,4.8c0,0.4,0.1,1.6-0.3,1.7L95,121.6z M103.2,118.2
														c-0.2-1.7,0-3.3,0.7-4.9c0.2-0.5,0.5-1,1.2-1s1,0.5,1.2,1.1c0.2,0.8,0.4,1.6,0.4,2.4v1.5c-0.1,1-0.3,2.1-0.8,3.1
														c-0.1,0.2-0.4,0.6-0.6,0.6l0,0c-0.3,0.1-0.5,0-0.8-0.1c-0.1-0.1-0.2-0.1-0.2-0.2c-0.5-0.4-0.7-1.1-0.9-1.6L103.2,118.2
														z M107.4,118.8c0-0.1-0.1-0.1-0.1-0.2c0.2-0.1,0.3-0.3,0.5-0.3c1.8-0.5,3.6-1,5.3-1.4c0.4-0.1,0.7-0.2,1.1-0.3
														c0,0.1,0,0.2,0,0.2C112,117.5,109.7,118.2,107.4,118.8z M115.3,118c-0.3,0-0.3-0.2-0.5-0.5c-0.1-0.2-0.2-0.5-0.2-0.7
														s-0.1-0.3-0.1-0.5c0-0.1,0-0.2,0-0.3c0,0.1,0-0.1,0-0.1c0-0.1,0-0.2,0-0.3c-0.1-0.8,0.1-1.5,0.5-2.2
														c0.2-0.4,0.6-0.3,0.8,0c0.3,0.6,0.5,2.1,0.3,3.2l0,0C115.8,117.3,115.7,118,115.3,118z"/>
													<path class="st0" d="M75.1,104.6l-0.6,2.9c0,0-0.2,0.8,1.2,0.9c1.4,0.1,18,0.4,18,0.4s1.7,0.1,1.7-1.8
														c0-1.5-0.1-9.2-0.1-9.2s-0.2-1.2-1.7-1.1s-15.8,0.8-15.8,0.8s-1,0-1.3,1C76,99.6,75.1,104.6,75.1,104.6z"/>
													<path class="st0" d="M115.7,104c-3.4-2.1-6.7-4.3-10.2-6.4c-2.2-1.3-4.6-2.5-6.9-3.7c-1.2-0.6-2.5-0.9-3.8-0.8
														c-3.4,0.1-6.7,0.3-10.1,0.5c-2.2,0.2-4.4,0.5-6.7,0.6c0.7-0.3,1.5-0.7,2.2-0.9c2.6-0.8,5.8-0.8,9.2-1
														c2.1-0.1,3.9-0.2,5.9-0.3c1.8-0.1,3.3,0.7,4.8,1.5c3.5,2,6.7,3.5,9.9,6c1.8,1.4,3.5,2.8,5.2,4.1
														C115.5,103.7,115.6,103.9,115.7,104L115.7,104z"/>
												</g>
											</g>
											<path class="st0" d="M94.8,115.5c0-0.9-0.4-1-0.7-1.1c-0.3-0.1-1.7,0-2.9,0c-1.3,0-1.4,0-2.7-0.1c-1-0.1-1.1,0.4-1.1,0.8
												s0,1.3,1.1,1.4c1.4,0.2,3.6,0.2,4.9,0.1C94.8,116.6,94.8,116.4,94.8,115.5z M88.8,115.9c-0.4,0-0.6-0.3-0.7-0.6
												c0-0.3,0.3-0.6,0.6-0.7c0.3,0,0.6,0.3,0.7,0.6C89.4,115.6,89.1,115.9,88.8,115.9z M91.1,116.4c-0.5,0-0.9-0.4-0.9-0.9
												s0.4-0.9,0.9-0.9s0.9,0.4,0.9,0.9C92,116,91.6,116.4,91.1,116.4z M93.4,116c-0.4,0-0.6-0.3-0.7-0.6
												c0-0.4,0.3-0.6,0.6-0.7c0.4,0,0.6,0.3,0.7,0.6C94,115.7,93.7,116,93.4,116z"/>
											<path class="st0" d="M79,115.1c0-0.7-0.2-0.8-0.4-0.9c-0.2-0.1-1.3-0.1-2.1-0.2c-0.9-0.1-1-0.1-2-0.2
												c-0.7-0.1-0.9,0.3-0.9,0.6c0,0.3-0.1,1,0.7,1.2c1,0.2,2.6,0.3,3.7,0.3C78.9,116,78.9,115.8,79,115.1z M74.4,115.2
												c-0.3,0-0.5-0.2-0.5-0.5s0.2-0.5,0.5-0.5s0.5,0.2,0.5,0.5S74.7,115.2,74.4,115.2z M76.1,115.6c-0.4,0-0.6-0.3-0.6-0.7
												c0-0.4,0.3-0.7,0.7-0.7c0.4,0,0.7,0.3,0.6,0.7C76.8,115.3,76.5,115.6,76.1,115.6z M77.9,115.5c-0.3,0-0.5-0.2-0.5-0.5
												s0.2-0.5,0.5-0.5s0.5,0.2,0.5,0.5C78.3,115.3,78.1,115.5,77.9,115.5z"/>
											<path class="st0" d="M100.9,109.3l-1.4-0.2c0,0,0.6-2.8,0.6-6.1c0-3.3-0.8-5.9-1-6.2c-0.2-0.3,0-0.5-0.8-0.7
												s-1.4-0.4-1.2,0.3c0.3,0.7,1.1,3.9,0.9,8c-0.2,4.2-0.6,6.3-0.7,6.6c0,0.3,0,0.5,0.5,0.6c0.4,0.1,0.7,0.1,0.9,0.1
												c0.3,0,0.4-0.2,0.6-1.4l1.4,0.1L100.9,109.3z"/>
											<g>
												<path class="st0" d="M107.4,108.7l-5.7-1.1c-0.4-0.1-0.7-0.1-0.7-0.4s0-0.8,0.1-1.7c0.1-1.1,0.1-2.4,0-3.6
													c-0.1-1.3-0.3-2.5-0.6-3.8c-0.1-0.3-0.3-0.7-0.1-0.9c0.2-0.4,1,0,1.2,0.1c1.5,0.6,3.9,1.7,4.6,2.4
													c0.3,0.3,0.4,1.4,0.5,1.9c0.2,2,0.4,3.4,0.6,5.3L107.4,108.7z"/>
											</g>
										</g>
									</g>
								</g>
								<g>
									<ellipse class="st10" cx="105" cy="116.8" rx="2" ry="4.5"/>
									<ellipse class="st0" cx="105.2" cy="116.8" rx="1.4" ry="3.5"/>
								</g>
							</g>
							<g>
								<path class="st0" d="M73.3,117.4c-0.2-0.4-0.2-1.4-0.1-2.5c0-0.3,0-0.5,0-0.7c0-0.5,0.2-0.8,0.4-1c0.1-0.1,0.2-0.2,0.2-0.3
									c0-0.2-0.3-0.5-0.4-0.6l-0.1-0.1v-4.4c0-0.1,2-10.5,2.3-11.9c0.3-1.5,2.4-1.9,2.5-2L78,94.1c-0.2-0.1-1.7,0.5-2,1.8
									c-0.3,1.4-2.3,11.8-2.3,11.9v4.2c0.1,0.1,0.4,0.4,0.4,0.8c0,0.2-0.1,0.3-0.2,0.5s-0.3,0.4-0.3,0.8c0,0.2,0,0.4,0,0.7
									c0,0.8-0.1,2,0.1,2.3L73.3,117.4z"/>
							</g>
						</g>
					</g>
				</g>
				<g>
					
						<linearGradient id="SVGID_15_" gradientUnits="userSpaceOnUse" x1="18.39" y1="92.34" x2="138.9977" y2="92.34" gradientTransform="matrix(1 0 0 -1 0 229.1)">
						<stop  offset="0" style="stop-color:#000000"/>
						<stop  offset="0.1557" style="stop-color:#BC2227"/>
						<stop  offset="0.3156" style="stop-color:#D0202D"/>
						<stop  offset="0.666" style="stop-color:#D0202D"/>
						<stop  offset="0.8393" style="stop-color:#BC2227"/>
						<stop  offset="1" style="stop-color:#000000"/>
					</linearGradient>
					<path class="st16" d="M127.6,134.3c-1.4-2-9.7-7.7-25-10.9c-11.4-2.4-22.1-2.3-25.1-2.2c-2.9-0.1-13.7-0.2-25.1,2.2
						c-15.3,3.2-23.5,8.9-25,10.9c-1.4,1.9-0.9,4.2-0.5,5.6c0.8,2.7,2.7,8.8,3.9,12.5c-0.2-0.9,0-3.5,6.6-6.2
						c8.1-3.3,17.7-6.1,40-6.2c22.3,0.1,31.9,2.9,40,6.2c6.6,2.7,6.8,5.3,6.6,6.2c1.2-3.8,3.1-9.8,3.9-12.5
						C128.5,138.5,129.1,136.2,127.6,134.3z"/>
					
						<linearGradient id="SVGID_16_" gradientUnits="userSpaceOnUse" x1="30.0243" y1="84.62" x2="125.2243" y2="84.62" gradientTransform="matrix(1 0 0 -1 0 229.1)">
						<stop  offset="0" style="stop-color:#4D1212"/>
						<stop  offset="0.1605" style="stop-color:#F7F7F7"/>
						<stop  offset="0.8276" style="stop-color:#FFFFFF"/>
						<stop  offset="1" style="stop-color:#4D1212"/>
					</linearGradient>
					<path class="st17" d="M98,139.3c-7.5-1-17.5-1-20.4-1s-12.9,0-20.4,1C32.1,142.7,30,149.7,30,149.7l0.3,1
						c0.4-2.6,6.5-6.5,16.9-8.8c10-2.2,21.1-2.8,30.4-2.9c9.2,0,20.4,0.7,30.4,2.9c10.4,2.3,16.5,6.2,16.9,8.8l0.3-1
						C125.2,149.7,123.1,142.7,98,139.3z"/>
					<g>
						<path class="st18" d="M26.8,138.9C26.8,139,26.8,139,26.8,138.9C26.8,139,26.8,138.9,26.8,138.9z"/>
						
							<linearGradient id="SVGID_17_" gradientUnits="userSpaceOnUse" x1="26.6963" y1="98.47" x2="128.5523" y2="98.47" gradientTransform="matrix(1 0 0 -1 0 229.1)">
							<stop  offset="0" style="stop-color:#4D1212"/>
							<stop  offset="0.1605" style="stop-color:#F7F7F7"/>
							<stop  offset="0.8276" style="stop-color:#FFFFFF"/>
							<stop  offset="1" style="stop-color:#4D1212"/>
						</linearGradient>
						<path class="st19" d="M128.5,137.1c0,0-1.3-2.7-6.3-5.5c-5.1-2.8-15.5-6.7-25.7-8.1c-6.9-0.9-14.8-1.1-18.1-1.1l0,0
							c0,0-0.3,0-0.8,0s-0.8,0-0.8,0l0,0c-3.3,0-11.3,0.2-18.1,1.1c-10.2,1.3-20.6,5.3-25.7,8.1s-6.3,5.5-6.3,5.5
							c-0.1,0.5,0.1,1.7,0.1,1.8c0-0.2-0.2-2.4,4.8-5.5c5.1-3.2,15.6-7.4,26-8.8c7.6-1.1,17.3-1.2,20-1.2c2.8,0,12.4,0.1,20,1.2
							c10.4,1.4,20.9,5.6,26,8.8c5,3.1,4.8,5.4,4.8,5.5C128.4,138.8,128.6,137.6,128.5,137.1z"/>
						<path class="st18" d="M128.4,138.9C128.4,138.9,128.4,139,128.4,138.9C128.4,139,128.4,139,128.4,138.9z"/>
					</g>
				</g>
				<g>
					<path class="st10" d="M50.5,130.9l3-0.5l0.2,0.9l-2,0.4l0.1,0.8l1.9-0.3l0.2,0.9l-1.9,0.3l0.2,0.9l2.1-0.4l0.2,0.9l-3.1,0.6
						L50.5,130.9z"/>
					<path class="st10" d="M56.4,132.1c-0.2-0.1-0.4-0.2-0.6-0.2c-0.1,0-0.2,0-0.2,0.1c-0.1,0-0.1,0.1-0.1,0.2
						c0,0.1,0.1,0.1,0.1,0.1c0.1,0,0.2,0,0.3,0c0.1,0,0.3,0,0.4,0s0.3,0,0.4,0.1c0.1,0,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.2,0.2,0.4
						s0,0.4-0.1,0.5s-0.2,0.3-0.3,0.4c-0.1,0.1-0.3,0.2-0.4,0.2c-0.2,0.1-0.3,0.1-0.5,0.1s-0.4,0-0.7,0c-0.2,0-0.4-0.1-0.6-0.2
						l0.5-0.7c0.1,0.1,0.2,0.2,0.3,0.2c0.1,0,0.2,0,0.4,0c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1-0.1,0.1-0.2c0-0.1-0.1-0.1-0.1-0.2
						c-0.1,0-0.2,0-0.3-0.1c-0.1,0-0.3,0-0.4,0s-0.3,0-0.4-0.1c-0.1,0-0.2-0.1-0.3-0.2c-0.1-0.1-0.2-0.2-0.2-0.4s0-0.3,0-0.5
						c0.1-0.1,0.1-0.3,0.2-0.4c0.1-0.1,0.2-0.2,0.4-0.2c0.1-0.1,0.3-0.1,0.5-0.1s0.4,0,0.6,0s0.4,0.1,0.6,0.2L56.4,132.1z"/>
					<path class="st10" d="M59.7,131.6l-0.8,0.1l0.1,1c0,0.1,0,0.2,0,0.2c0,0.1,0,0.1,0.1,0.2l0.1,0.1c0.1,0,0.1,0,0.2,0
						c0,0,0.1,0,0.2,0s0.1-0.1,0.2-0.1l0.1,0.8c-0.1,0.1-0.2,0.1-0.3,0.1c-0.1,0-0.2,0-0.3,0.1c-0.2,0-0.3,0-0.4,0s-0.3-0.1-0.4-0.1
						c-0.1-0.1-0.2-0.1-0.3-0.2c-0.1-0.1-0.1-0.2-0.1-0.4l-0.2-1.4l-0.6,0.1l-0.1-0.7l0.6-0.1l-0.1-0.9l0.9-0.1l0.1,0.9l0.8-0.1
						L59.7,131.6z"/>
					<path class="st10" d="M62.3,133.2L62.3,133.2c-0.1,0.2-0.2,0.3-0.4,0.4c-0.2,0.1-0.3,0.1-0.5,0.2c-0.1,0-0.3,0-0.4,0
						s-0.3-0.1-0.4-0.1c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.1-0.2-0.1-0.4s0-0.3,0.1-0.4c0.1-0.1,0.1-0.2,0.2-0.3s0.2-0.2,0.4-0.2
						c0.1-0.1,0.3-0.1,0.4-0.1s0.3-0.1,0.5-0.1s0.3,0,0.4-0.1c0-0.2-0.1-0.3-0.2-0.4c-0.1-0.1-0.3-0.1-0.4-0.1
						c-0.2,0-0.3,0.1-0.4,0.1c-0.1,0.1-0.2,0.2-0.3,0.3l-0.6-0.4c0.2-0.2,0.4-0.3,0.6-0.4c0.2-0.1,0.5-0.2,0.7-0.2
						c0.3,0,0.5,0,0.7,0s0.3,0.1,0.5,0.3c0.1,0.1,0.2,0.3,0.3,0.5c0.1,0.2,0.1,0.4,0.1,0.7l0.2,1.5l-0.9,0.1L62.3,133.2z M62,132.2
						c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0.1c-0.1,0-0.2,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.1,0.3c0,0.1,0.1,0.2,0.2,0.2
						s0.2,0.1,0.3,0c0.1,0,0.2,0,0.3-0.1c0.1,0,0.2-0.1,0.2-0.1c0.1-0.1,0.1-0.1,0.1-0.2c0-0.1,0-0.2,0-0.3v-0.2L62,132.2z"/>
					<path class="st10" d="M63.5,128.7l0.9-0.1l0.2,2l0,0c0.1-0.2,0.2-0.3,0.4-0.3c0.2-0.1,0.4-0.1,0.5-0.1c0.2,0,0.4,0,0.6,0.1
						s0.3,0.2,0.5,0.3c0.1,0.1,0.2,0.3,0.3,0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.2,0,0.4-0.1,0.6c-0.1,0.2-0.1,0.4-0.3,0.5
						c-0.1,0.1-0.3,0.3-0.5,0.4c-0.2,0.1-0.4,0.1-0.6,0.2c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2-0.1-0.3-0.1c-0.1,0-0.2-0.1-0.2-0.1
						c-0.1-0.1-0.1-0.1-0.2-0.2l0,0v0.4l-0.9,0.1L63.5,128.7z M64.6,131.8c0,0.2,0.1,0.4,0.2,0.5s0.3,0.2,0.6,0.2
						c0.2,0,0.4-0.1,0.5-0.3c0.1-0.1,0.2-0.3,0.2-0.6c0-0.2-0.1-0.4-0.2-0.5s-0.3-0.2-0.6-0.2c-0.2,0-0.4,0.1-0.5,0.3
						S64.6,131.6,64.6,131.8z"/>
					<path class="st10" d="M67.5,128.4l0.9-0.1l0.3,4.7l-1,0.1L67.5,128.4z"/>
					<path class="st10" d="M69.2,129c0-0.1,0-0.3,0.1-0.4c0.1-0.1,0.2-0.2,0.4-0.2c0.2,0,0.3,0,0.4,0.1s0.2,0.2,0.2,0.4
						s0,0.3-0.1,0.4c-0.1,0.1-0.2,0.2-0.4,0.2c-0.2,0-0.3,0-0.4-0.1C69.2,129.3,69.2,129.2,69.2,129z M69.3,130h0.9l0.1,3h-1V130z"
						/>
					<path class="st10" d="M72.7,130.7c-0.1-0.2-0.3-0.2-0.6-0.2c-0.1,0-0.2,0-0.2,0.1c-0.1,0-0.1,0.1-0.1,0.2c0,0.1,0,0.1,0.1,0.2
						c0.1,0,0.2,0.1,0.3,0.1c0.1,0,0.3,0,0.4,0.1c0.1,0,0.3,0.1,0.4,0.1c0.1,0.1,0.2,0.1,0.3,0.3c0.1,0.1,0.1,0.3,0.1,0.4
						c0,0.2,0,0.4-0.1,0.5s-0.2,0.2-0.3,0.3c-0.1,0.1-0.3,0.1-0.5,0.2c-0.2,0-0.3,0.1-0.5,0.1s-0.4,0-0.7-0.1
						c-0.2-0.1-0.4-0.2-0.6-0.3l0.6-0.6c0.1,0.1,0.2,0.2,0.3,0.2c0.1,0.1,0.2,0.1,0.4,0.1c0.1,0,0.2,0,0.3-0.1
						c0.1,0,0.1-0.1,0.1-0.2c0-0.1,0-0.1-0.1-0.2c-0.1,0-0.2-0.1-0.3-0.1c-0.1,0-0.3,0-0.4-0.1c-0.1,0-0.3-0.1-0.4-0.1
						c-0.1-0.1-0.2-0.1-0.3-0.3c-0.1-0.1-0.1-0.3-0.1-0.5s0-0.3,0.1-0.5c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.3-0.2,0.4-0.2
						c0.2,0,0.3-0.1,0.5-0.1s0.4,0,0.6,0.1s0.4,0.2,0.5,0.3L72.7,130.7z"/>
					<path class="st10" d="M74.9,128.2v2.1l0,0c0-0.1,0.1-0.1,0.1-0.2c0.1-0.1,0.1-0.1,0.2-0.2c0.1,0,0.2-0.1,0.3-0.1
						c0.1,0,0.2,0,0.3,0c0.2,0,0.4,0,0.6,0.1c0.1,0.1,0.3,0.2,0.3,0.3c0.1,0.1,0.1,0.3,0.2,0.5c0,0.2,0,0.4,0,0.6v1.7h-1v-1.5
						c0-0.1,0-0.2,0-0.3s0-0.2-0.1-0.3c0-0.1-0.1-0.1-0.1-0.2c-0.1-0.1-0.2-0.1-0.3-0.1c-0.1,0-0.2,0-0.3,0.1
						c-0.1,0-0.1,0.1-0.2,0.2c0,0.1-0.1,0.2-0.1,0.2c0,0.1,0,0.2,0,0.3v1.5h-1v-4.7L74.9,128.2L74.9,128.2z"/>
					<path class="st10" d="M80.5,132.4c-0.2,0.2-0.4,0.3-0.6,0.4c-0.2,0.1-0.5,0.1-0.7,0.1s-0.5,0-0.7-0.1s-0.4-0.2-0.5-0.3
						c-0.2-0.1-0.3-0.3-0.4-0.5s-0.1-0.4-0.1-0.7c0-0.2,0.1-0.5,0.2-0.6c0.1-0.2,0.2-0.4,0.4-0.5c0.2-0.1,0.3-0.2,0.6-0.3
						c0.2-0.1,0.4-0.1,0.7-0.1c0.2,0,0.4,0,0.6,0.1s0.3,0.2,0.4,0.3s0.2,0.3,0.3,0.5c0.1,0.2,0.1,0.4,0.1,0.7v0.3l-2.2-0.1
						c0,0.2,0.1,0.3,0.2,0.4c0.1,0.1,0.3,0.2,0.5,0.2s0.3,0,0.4-0.1s0.2-0.1,0.3-0.3L80.5,132.4z M79.7,131.1c0-0.2,0-0.3-0.1-0.4
						c-0.1-0.1-0.2-0.2-0.4-0.2c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0.1-0.2,0.1c-0.1,0.1-0.1,0.1-0.1,0.2c0,0.1-0.1,0.1-0.1,0.2
						L79.7,131.1z"/>
					<path class="st10" d="M84.4,133.1l-0.9-0.1v-0.4l0,0c0,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.2,0.1s-0.2,0.1-0.3,0.1
						c-0.1,0-0.2,0-0.3,0c-0.2,0-0.4-0.1-0.6-0.2c-0.2-0.1-0.3-0.2-0.5-0.4c-0.1-0.1-0.2-0.3-0.3-0.5c-0.1-0.2-0.1-0.4-0.1-0.6
						c0-0.2,0.1-0.4,0.1-0.6c0.1-0.2,0.2-0.4,0.3-0.5c0.1-0.1,0.3-0.2,0.5-0.3s0.4-0.1,0.6-0.1s0.4,0.1,0.5,0.1
						c0.2,0.1,0.3,0.2,0.4,0.3l0,0l0.1-2l0.9,0.1V133.1z M83.6,131.6c0-0.2,0-0.4-0.2-0.6c-0.1-0.1-0.3-0.2-0.5-0.2s-0.4,0-0.6,0.2
						c-0.1,0.1-0.2,0.3-0.2,0.5s0,0.4,0.2,0.6c0.1,0.1,0.3,0.2,0.5,0.2s0.4,0,0.6-0.2C83.5,132,83.6,131.8,83.6,131.6z"/>
					<path class="st10" d="M88.8,130.2l-1,0.7l-0.5-0.7l1.6-1l0.9,0.1l-0.5,4.4l-0.9-0.1L88.8,130.2z"/>
					<path class="st10" d="M92.5,132.4c0,0-0.1,0-0.2,0s-0.1,0-0.2,0c-0.2,0-0.4-0.1-0.6-0.2c-0.2-0.1-0.3-0.2-0.4-0.4
						c-0.1-0.1-0.2-0.3-0.2-0.5s0-0.4,0-0.6c0-0.2,0.1-0.4,0.2-0.6c0.1-0.2,0.2-0.3,0.4-0.4c0.2-0.1,0.4-0.2,0.6-0.2s0.4,0,0.7,0
						c0.2,0,0.4,0.1,0.6,0.2c0.2,0.1,0.4,0.2,0.5,0.4s0.2,0.3,0.3,0.5c0.1,0.2,0.1,0.4,0,0.6c0,0.2-0.1,0.3-0.1,0.4
						c0,0.1-0.1,0.2-0.2,0.3s-0.1,0.2-0.2,0.3s-0.2,0.2-0.2,0.3l-1.2,1.3l-1.2-0.2L92.5,132.4z M91.9,131c0,0.2,0,0.4,0.1,0.5
						s0.3,0.2,0.5,0.3c0.2,0,0.4,0,0.5-0.1c0.2-0.1,0.2-0.3,0.3-0.5c0-0.2,0-0.4-0.1-0.5s-0.3-0.2-0.5-0.3c-0.2,0-0.4,0-0.5,0.1
						C92,130.7,91.9,130.8,91.9,131z"/>
					<path class="st10" d="M97,131.2l-2.2-0.4l0.1-0.8l3.2,0.6l-0.1,0.8l-2.4,3.2l-1.1-0.2L97,131.2z"/>
					<path class="st10" d="M98.3,132.8c0.1-0.3,0.1-0.5,0.2-0.8c0.1-0.2,0.2-0.5,0.4-0.7c0.2-0.2,0.4-0.3,0.6-0.4
						c0.2-0.1,0.5-0.1,0.8,0s0.6,0.2,0.8,0.4c0.2,0.2,0.3,0.4,0.4,0.6c0.1,0.2,0.1,0.5,0.1,0.8s0,0.5-0.1,0.8s-0.1,0.5-0.2,0.8
						s-0.2,0.5-0.4,0.7c-0.2,0.2-0.4,0.3-0.6,0.4c-0.2,0.1-0.5,0.1-0.8,0s-0.6-0.2-0.8-0.4c-0.2-0.2-0.3-0.4-0.4-0.6
						c-0.1-0.2-0.1-0.5-0.1-0.8C98.3,133.3,98.3,133.1,98.3,132.8z M99.3,133c0,0.1,0,0.3-0.1,0.4c0,0.2,0,0.3,0,0.5
						s0.1,0.3,0.1,0.4c0.1,0.1,0.2,0.2,0.4,0.2c0.2,0,0.3,0,0.4-0.1s0.2-0.2,0.3-0.3c0.1-0.1,0.1-0.3,0.2-0.4c0-0.2,0.1-0.3,0.1-0.4
						c0-0.1,0-0.3,0.1-0.4c0-0.2,0-0.3,0-0.5s-0.1-0.3-0.1-0.4c-0.1-0.1-0.2-0.2-0.4-0.2c-0.2,0-0.3,0-0.4,0.1s-0.2,0.2-0.3,0.3
						c-0.1,0.1-0.1,0.3-0.2,0.4S99.3,132.9,99.3,133z"/>
				</g>
				<g>
					<path class="st0" d="M80.4,95.2c0.1-0.4,0.6-0.4,0.6-0.4s8.5-0.7,9-0.7s0.4,0.5,0.4,0.5l-0.1,1.2C90.3,96,90,96,90,96
						s-9.4,0.5-9.6,0.5c0,0-0.1,0-0.1-0.2C80.3,96.3,80.4,95.2,80.4,95.2z"/>
					<path class="st0" d="M74.6,109.7c0-0.3,1.4-0.2,1.4-0.2s16.8,0.4,17.8,0.5s0.8,0.4,0.8,0.4l-0.2,0.9c-0.1,0.1-0.6,0.1-0.6,0.1
						s-18.4-0.6-18.9-0.7c0,0-0.3,0-0.3-0.1C74.5,110.5,74.6,109.7,74.6,109.7z"/>
					<path class="st0" d="M79.3,96.6c0,0,0.2-0.1,0.3-0.4c0.1-0.3,0.2-1,0.2-1s0-0.3-0.2-0.3S78.2,95,78.2,95s-0.3,0.1-0.4,0.3
						c-0.1,0.2-0.5,1.2-0.5,1.2s0,0.1,0.1,0.1C77.6,96.7,79.3,96.6,79.3,96.6z"/>
					<path class="st0" d="M91,95.8c0,0-0.1,0.2,0.2,0.2c0.3,0,2.5-0.1,2.5-0.1s0.4-0.1,0.4-0.4s0-1.4,0-1.4s0-0.4-0.3-0.4
						s-2.5,0.2-2.5,0.2s-0.3,0-0.3,0.2C91.1,94.4,91,95.8,91,95.8z"/>
					<path class="st20" d="M84.2,118.9c0,0,0.2,0,0.2,0.3s0.1,0.5,0,0.7c0,0.1-0.2,0.4-0.4,0.4c-0.2,0-2.9-0.2-2.9-0.2
						s-0.4-0.2-0.3-0.5V119c0,0,0-0.3,0.2-0.3C81.3,118.7,84.2,118.9,84.2,118.9z"/>
					<path class="st0" d="M100.5,110.4c0,0-0.2,1.1,0.5,1s6.1,0,6.1,0s0.4,0,0.4-0.3s0-0.9,0-1.4c0-0.1-0.2-0.2-0.5-0.3
						c-0.3,0-5.6-1.2-5.6-1.2s-0.5-0.1-0.6,0.3S100.5,110.4,100.5,110.4z"/>
					<path class="st0" d="M108.6,108.8c0.4,0.1,1.1,0.3,1.3,0.2c0.2,0,0.3-0.3,0.2-1s-0.5-5.1-0.6-5.4c0-0.4,0-0.6-0.3-0.9
						c-0.3-0.2-0.7-0.5-1-0.6s-0.8-0.2-0.7,0.5s0.7,6.2,0.7,6.6C108.3,108.5,108.3,108.7,108.6,108.8z"/>
					<path class="st0" d="M111.1,109.3c0.4,0.1,1.1,0.3,1.3,0.2c0.2,0,0.3-0.3,0.2-0.9s-0.5-4.5-0.5-4.8s-0.1-0.6-0.3-0.8
						c-0.3-0.2-0.7-0.4-1-0.6c-0.3-0.1-0.8-0.1-0.7,0.4c0.1,0.6,0.5,5.4,0.6,5.7C110.8,109,110.8,109.3,111.1,109.3z"/>
					<path class="st0" d="M113.7,109.9c0.4,0.1,1.1,0.2,1.3,0.2s0.3-0.3,0.2-0.8c-0.1-0.6-0.4-4-0.5-4.3c0-0.3-0.1-0.5-0.3-0.7
						c-0.3-0.2-0.7-0.4-1-0.5s-0.8-0.1-0.7,0.4s0.5,4.9,0.6,5.2C113.3,109.7,113.4,109.9,113.7,109.9z"/>
				</g>
			</g>
		</g>
	</g>
</g>
</svg>
