# frozen_string_literal: true

module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user

    def connect
      tenant = request.subdomain.split('.').first
      Apartment::Tenant.switch!(tenant)
      self.current_user = find_verified_user
      logger.add_tags 'ActionCable', Apartment::Tenant.current, current_user.class.to_s, current_user.id
    end

    protected

    def decode_token
      @jwt_token = request.cookies['fusesystems_session']
      return @decoded_token = {} unless @jwt_token

      @decoded_token = JWT.decode(@jwt_token, Rails.application.credentials.secret_key_base, true, verify_iat: true)[0].with_indifferent_access
    end

    def verified_user
      decode_token
      current_user = User.kept.find_by(id: @decoded_token[:user_id])
      if current_user
        valid_issued_at = current_user.password_changed_at.blank? || @decoded_token[:iat] >= current_user.password_changed_at.to_i
        is_valid_session = valid_issued_at && !current_user.blacklisted_tokens.exists?(token: @jwt_token)
      end
      current_user = nil unless is_valid_session
      @verified_user ||= current_user
    end

    # this checks whether a user is authenticated with devise
    def find_verified_user
      return verified_user if verified_user.present?

      reject_unauthorized_connection
    end
  end
end
