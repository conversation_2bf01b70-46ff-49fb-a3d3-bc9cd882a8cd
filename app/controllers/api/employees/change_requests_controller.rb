# frozen_string_literal: true

module Api
  module Employees
    class ChangeRequestsController < Api::BaseController
      include Api::ApiRescues
      before_action :set_change_request, only: %w[update]
      before_action :check_minimum_one_field_present, only: %w[create]

      def index
        change_requests = ChangeRequest.kept.includes(:employee)
        if (search_text = params[:search_text]).present?
          search_text = search_text == 'General Info' ? 0 : ChangeRequest.request_types.fetch(search_text&.downcase.gsub(' ', '_'), search_text)
          change_requests = change_requests.search_by_change_request(search_text)
        end
        change_requests = params[:status].present? ? change_requests.where(status: params[:status]) : change_requests.where(status: 'pending')

        change_requests = if (start_date = params[:search_date_from]).present? && (end_date = params[:search_date_to]).present?
                            search_with_dates(start_date, end_date, change_requests)
                          else
                            change_requests.order(updated_at: :desc)
                          end
        if (search_type = params[:search_type]).present?
          change_requests = change_requests.where(request_type: search_type.singularize)
        end

        pagy, change_requests = pagy(change_requests)
        render_success(data: change_requests, options: { params: { custom_keys: @current_employee ? 'employee' : 'user' }, meta: pagy_headers_hash(pagy) })
      end

      def show
        render_json(data: @change_request, options: {params: {custom_keys: @current_employee ? "employee": "user"}})
      end

      def create
        leave_requested_changes = params.to_enum.to_h.dig('change_request', 'requested_changes')
        analytics_type = leave_requested_changes&.class&.to_s == 'Array' ? leave_requested_changes&.first&.dig('leave_type') : leave_requested_changes&.dig('leave_type')
        analytics_of_employee_user = Account.find_by(subdomain: Apartment::Tenant.current).saas_json.dig('schema', 'employees', 'analytics_of_employee_user') == true
        change_request = if analytics_type.present? && analytics_of_employee_user
                           start_date = params.to_enum.to_h.dig('change_request', 'requested_changes')&.first&.dig('started_at')
                           duration_from, duration_to = params[:change_request][:leave_range_from], params[:change_request][:leave_range_to]
                           duration_from, duration_to = ChangeRequest.get_from_and_to_date(start_date, analytics_type).map { |x| x.to_s } if duration_from.blank? && duration_to.blank?
                           get_leave_change_request(resource_params[:request_type], analytics_type, duration_from, duration_to)
                         else
                           @current_employee.change_requests.kept.find_by(status: 'pending', request_type: resource_params[:request_type]) if resource_params[:request_type].present?
                         end

        if change_request.nil?
          change_request = @current_employee.change_requests.new(resource_params)
          change_request.requested_changes = change_request.get_requested_changes(params[:change_request][:requested_changes], [], request.content_type, params[:change_request][:request_type])
        else
          change_request.requested_changes = change_request.get_requested_changes(params[:change_request][:requested_changes], change_request.requested_changes, request.content_type, params[:change_request][:request_type])
        end

        # check beneficiary percentage exceeding max limit of 100
        change_request.beneficiary_percentage if resource_params[:request_type] == "beneficiary"

        if !change_request.errors.present? && change_request.save
          params[:change_request][:requested_changes].each do |requested_change|
            requested_change.each { |value| requested_change = value } if request.content_type == "multipart/form-data"
            if requested_change.key?(:files)
              change_request_upload = change_request.change_request_uploads.find_by(timestamp: requested_change[:timestamp])
              if change_request_upload.present? && requested_change[:timestamp] != nil
                change_request_upload.update(files: requested_change[:files], timestamp: requested_change[:timestamp])
              else
                change_request.change_request_uploads.create(files: requested_change[:files], timestamp: requested_change[:timestamp])
              end
            end
          end
          render_json(data: change_request, options: {params: {custom_keys: @current_employee ? "employee" : "user"}})
        else
          render_error(change_request.errors.full_messages.uniq, 422)
        end
      end

      def update
        requested_changes = {}
        params[:requested_changes].each do |key,value|
          if value.is_a?(ActionController::Parameters)
            requested_changes["#{key}"] = value.to_enum.to_h
          elsif value.is_a?(Array) && value.any? { |x| x.is_a?(ActionController::Parameters) }
            requested_changes["#{key}"] = []
            value.each_with_index do |array_value, index|
              requested_changes["#{key}"] << array_value.to_enum.to_h
            end
          else
            requested_changes["#{key}"] = value
          end
        end

        @change_request.update_status(params[:requested_changes], params[:status], params[:request_type])

        if params[:status].downcase == "completed"
          model = params[:request_type].singularize.camelize.constantize

          if requested_changes.keys.include?('files')
            requested_changes.delete('files')
            change_request_upload = ChangeRequestUpload.find_by(timestamp: requested_changes["timestamp"])
            unless change_request_upload.present?
              change_request_upload = ChangeRequestUpload.create!(timestamp: requested_changes["timestamp"], change_request_id: @change_request.id)
            end

            # Handled multiple files and single file attachments
            if %w(benefit_coverage employee_discipline_setting employee_grievance employee_discipline_step
                  employee_grievance_step life_insurance lodi_request_tab lodi).include?(params[:request_type].singularize)
              requested_changes.merge!(files: change_request_upload.files.blobs)
            else
              requested_changes.merge!(file: change_request_upload.files.blobs.first)
            end
          end

          # Removed unwanted keys from requested_changes json
          status = requested_changes.delete('status') if requested_changes.keys.include?('status')
          request_id = requested_changes.delete('id') if requested_changes.keys.include?('id')
          timestamp = requested_changes.delete('timestamp') if requested_changes.keys.include?('timestamp')

          if params[:request_type] == 'lodi_request_tab'
            lodi_staus = requested_changes.delete('lodi_status') if requested_changes.keys.include?('lodi_status')
            requested_changes.merge!(status: lodi_staus)
          end

          if params[:request_type] == 'assault'
            requested_changes['witnesses_attributes']&.each do |witness_details|
              witness_details.delete('timestamp') if witness_details.keys.include?('timestamp')
            end
          end

          @model = model.kept.find(request_id) if request_id.present? && model.column_names.include?('discarded_at')
          @model = model.find(request_id) if request_id.present? && !model.column_names.include?('discarded_at')

          if @model.present?
            if params[:request_type] == 'benefit_coverage'
              @model.assign_attributes(requested_changes.to_enum.to_h)
              @model.save(validate: false)
            else
              @model.update(requested_changes.to_enum.to_h)
            end
          else
            record = model.new(requested_changes.to_enum.to_h)

            # Handle allow_multiple_present_status
            if %w(employee_employment_status employee_officer_status employee_office delegate_assignment employee_position employee_rank).include?(params[:request_type].singularize)
              save_record(record, model)
            else
              if params[:request_type] == 'benefit_coverage'
                record.save(validate: false)
              else
                record.save
              end
            end
          end

          if ((record.present? && !record.errors.present?) || (@model.present? && !@model.errors.present?)) && @change_request.save!
            current_account = Account.find_by(subdomain: Apartment::Tenant.current)
            if current_account.saas_json.dig('schema', 'notifications', 'change_request_notification') == true
              ChangeRequestNotificationJob.perform_later(requested_changes, params[:request_type], @change_request.employee_id, request_id)
            end
            render_json(data: @change_request)
          else
            record.errors.full_messages.each { |error| @change_request.errors.add(:base, error) } if record.present? && record.errors.present?
            @model.errors.full_messages.each { |error| @change_request.errors.add(:base, error) } if @model.present? && @model.errors.present?

            @change_request.requested_changes.each do |requested_change|
              if (requested_change.include?('id') && requested_change['id'] == request_id) || (requested_change.include?('timestamp') && requested_change['timestamp'] == timestamp)
                requested_change.merge!(status: "pending", errors: @change_request.errors.full_messages)
              end
            end

            render_error(@change_request.errors.full_messages, 422)
            @change_request.save
          end
        else
          render_json(data: @change_request, options: {params: {custom_keys: @current_employee ? "employee" : "user"}}) if @change_request.save
        end
      end

      private

      def resource_params
        params.require(:change_request).permit(:request_type)
      end

      def set_change_request
        @change_request = ChangeRequest.kept.find(params[:id])
      end

      def save_record(record, model)
        ActiveRecord::Base.transaction do
          current_account = Account.find_by(subdomain: Apartment::Tenant.current)
          allow_multiple_present_status = current_account.saas_json.dig('schema', 'employees', 'allow_multiple_present_status')
          if allow_multiple_present_status == 'true'
            record.save
          else
            last_status = nil

            if record.valid? && record.end_date.blank?
              last_status = model.kept.where(employee_id: record.employee_id, end_date: nil).last
            end

            if record.save && last_status.present?
              last_status.update(end_date: record.start_date)

              if last_status.errors['Date range'].present?
                record.errors.add('Date range is invalid', " - could not update the end date of the status(#{last_status.name})") unless params[:request_type] == 'delegate_assignment'
                record.errors.add('Date range is invalid', " - could not update the end date of the status") if params[:request_type] == 'delegate_assignment'
                raise ActiveRecord::Rollback
              end
            end
          end
        end
      end

      def get_leave_change_request(request_type, analytics_type, leave_range_from, leave_range_to)
        change_requests = @current_employee.change_requests.kept.where(status: 'pending', request_type: request_type) if request_type.present?
        change_request = nil
        date_range = (Date.parse(leave_range_from)..Date.parse(leave_range_to))
        change_requests.each do |cr|
          change_request = cr if (cr.requested_changes.select { |x| x['leave_type'] == analytics_type && date_range.include?(Date.parse(x['started_at'])) && date_range.include?(Date.parse(x['ended_at'])) }).present?
          break if change_request.present?
        end
        change_request.present? ? change_request : nil
      end

      def check_minimum_one_field_present
        current_account = Account.find_by(subdomain: Apartment::Tenant.current)
        request_type = (params[:change_request][:request_type]).pluralize
        return unless (minimum_one_required_fields = current_account.saas_json.dig('mobile', 'employees', request_type, 'custom_validations', 'minimum_one_required_fields')).present?

        minimum_one_required_fields.each do |field|
          return true if params.dig('change_request', 'requested_changes')[0][field.to_s].present?
        end
        render_error('Please fill in at least one field before submitting', 422)
      end

      def search_with_dates(from_date, to_date, change_requests)
        conditions = []
        conditions << "DATE(created_at) >= '#{from_date}'" if from_date.present?
        conditions << "DATE(created_at) <= '#{to_date}'" if to_date.present?

        change_requests.where(conditions.join(' AND '))
      end
    end
  end
end
