# frozen_string_literal: true

module Api
  module Employees
    class DisabilitiesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_disability, only: %w[update destroy]

      def index
        return render_error('Employee id is missing') if params[:employee_id].blank?

        pagy, disabilities = pagy(Disability.kept.includes(:file_attachment).where(employee_id: params[:employee_id]))

        render_success(data: disabilities, options: { change_request: "disabilities", meta: pagy_headers_hash(pagy) })
      end

      def create
        disability = Disability.new(resource_params)
        disability.save
        render_json(data: disability)
      end

      def update
        @disability.update(resource_params)
        remove_file
        render_json(data: @disability)
      end

      def destroy
        @disability.discard
        render_json(data: @disability)
      end

      private

      def resource_params
        params.require(:disability).permit(:from_date, :to_date, :last_working_date, :last_paid_date, :file,
                                           :return_to_work, :check_number, :check_date, :expires_at, :employee_id)
      end

      def set_disability
        @disability = Disability.kept.includes(:file_attachment).find(params[:id])
      end

      def remove_file
        return if params[:disability][:remove_file] != true || !@disability.file.attached?
        @disability.file.purge
      end
    end
  end
end
