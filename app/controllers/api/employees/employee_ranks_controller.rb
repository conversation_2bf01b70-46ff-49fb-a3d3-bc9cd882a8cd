# frozen_string_literal: true

module Api
  module Employees
    class EmployeeRanksController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employee_rank, only: %w[update destroy]

      def index
        return render_error('Employee id is missing') if params[:employee_id].blank?

        employee_ranks = EmployeeRank.includes(:rank).kept.where(employee_id: params[:employee_id]).order('start_date DESC')
        pagy, employee_ranks = pagy(employee_ranks)
        render_success(data: employee_ranks, options: { change_request: "employee_rank", meta: pagy_headers_hash(pagy) })
      end

      def create
        employee_rank = EmployeeRank.new(resource_params)
        current_account = Account.find_by(subdomain: Apartment::Tenant.current)

        ActiveRecord::Base.transaction do
          last_status = nil

          if employee_rank.valid? && employee_rank.end_date.blank?
            last_status = EmployeeRank.kept.where(employee_id: employee_rank.employee_id, end_date: nil).last
          end
          allow_multiple_present_status = current_account.saas_json.dig('schema', 'employees', 'allow_multiple_present_status')

          if allow_multiple_present_status == 'true'
            employee_rank.save
          elsif employee_rank.save && last_status.present?
            last_status.update(end_date: employee_rank.start_date)
            if last_status.errors['Date range'].present?
              employee_rank.errors.add('Date range is invalid', " - could not update the end date of the status(#{last_status.name})")
              raise ActiveRecord::Rollback
            end
          end
        end

        render_json(data: employee_rank)
      end

      def update
        @employee_rank.update(resource_params)
        render_json(data: @employee_rank)
      end

      def destroy
        @employee_rank.discard
        render_json(data: @employee_rank)
      end

      private

      def resource_params
        params.require(:employee_rank).permit(:employee_id, :end_date, :notes, :rank_id, :start_date)
      end

      def set_employee_rank
        @employee_rank = EmployeeRank.kept.find(params[:id])
      end
    end
  end
end
