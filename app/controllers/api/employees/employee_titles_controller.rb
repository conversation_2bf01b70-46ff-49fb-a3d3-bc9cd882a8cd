# frozen_string_literal: true

module Api
  module Employees
    class EmployeeTitlesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_employee_title, only: %w[update destroy]

      def index
        return render_error('Employee id is missing') if params[:employee_id].blank?

        employee_titles = EmployeeTitle.includes(:section, :department, :title).kept
                                       .where(employee_id: params[:employee_id])
        pagy, employee_titles = pagy(employee_titles)
        render_success(data: employee_titles, options: { change_request: "employee_title", meta: pagy_headers_hash(pagy) })
      end

      def create
        employee_title = EmployeeTitle.new(resource_params)

        ActiveRecord::Base.transaction do
          last_status = nil

          if employee_title.valid? && employee_title.end_date.blank?
            last_status = EmployeeTitle.kept.where(employee_id: employee_title.employee_id, end_date: nil).last
          end

          if employee_title.save && last_status.present?
            last_status.update(end_date: employee_title.start_date)

            if last_status.errors['Date range'].present?
              employee_title.errors.add('Date range is invalid', " - could not update the end date of the status(#{last_status.name})")
              raise ActiveRecord::Rollback
            end
          end
        end

        render_json(data: employee_title)
      end

      def update
        @employee_title.update(resource_params)
        render_json(data: @employee_title)
      end

      def destroy
        @employee_title.discard
        render_json(data: @employee_title)
      end

      private

      def resource_params
        params.require(:employee_title).permit(:employee_id, :section_id, :department_id, :title_id, :start_date,
                                               :end_date, :notes)
      end

      def set_employee_title
        @employee_title = EmployeeTitle.kept.find(params[:id])
      end
    end
  end
end
