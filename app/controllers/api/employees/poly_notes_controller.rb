# frozen_string_literal: true

module Api
  module Employees
    class PolyNotesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_poly_note, only: %w[update destroy]
      before_action :check_notable_present, only: %w[update create]

      def index
        if params[:notable_id].blank? || params[:notable_type].blank?
          error = 'Association ID is missing'
        elsif params[:user_id].present? && current_user.id != params[:user_id].to_i
          error = 'User ID does not match'
        end
        return render_error(error) if error.present?

        poly_notes = if params[:user_id].present?
                       PolyNote.kept.where(notable_type: params[:notable_type], notable_id: params[:notable_id], user_id: params[:user_id]).order(updated_at: :desc)
                     else
                       PolyNote.kept.where(notable_type: params[:notable_type], notable_id: params[:notable_id], user_id: nil).order(updated_at: :desc)
                     end

        render_success(data: poly_notes)
      end

      def create
        poly_note = PolyNote.new(resource_params)
        poly_note.save!
        render_json(data: poly_note)
      end

      def update
        @poly_note.update(resource_params)
        render_json(data: @poly_note)
      end

      def destroy
        @poly_note.discard
        render_json(data: @poly_note)
      end

      private

      def resource_params
        params.require(:poly_notes).permit(:notes, :notable_type, :notable_id, :user_id)
      end

      def set_poly_note
        @poly_note = PolyNote.kept.find(params[:id])
      end

      def check_notable_present
        render_error('Association ID is missing') if params[:poly_notes][:notable_id].blank? || params[:poly_notes][:notable_type].blank?
      end
    end
  end
end
