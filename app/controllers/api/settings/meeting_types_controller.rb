# frozen_string_literal: true

module Api
  module Settings
    class MeetingTypesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_meeting_type, only: %w[update destroy]

      def index
        meeting_types = MeetingType.kept
        pagy, meeting_types = if params[:search_text].present?
                                pagy(meeting_types.search_by_name(params[:search_text]), items: params[:per_page])
                              else
                                pagy(meeting_types.order('name ASC'), items: params[:per_page])
                              end
        render_success(data: meeting_types, options: { meta: pagy_headers_hash(pagy) })
      end

      def create
        meeting_type = MeetingType.new(resource_params)
        meeting_type.save
        render_json(data: meeting_type)
      end

      def update
        @meeting_type.update(resource_params)
        render_json(data: @meeting_type)
      end

      def destroy
        @meeting_type.discard
        render_json(data: @meeting_type)
      end

      private

      def resource_params
        params.require(:meeting_type).permit(:name, :description)
      end

      def set_meeting_type
        @meeting_type = MeetingType.kept.friendly.find(params[:id])
      end
    end
  end
end
