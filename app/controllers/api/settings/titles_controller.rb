# frozen_string_literal: true

module Api
  module Settings
    class TitlesController < Api::BaseController
      include Api::ApiRescues
      before_action :set_title, only: %w[edit update destroy show]

      def index
        titles = Title.includes(:department, :section).kept
        pagy, titles = if params[:search_text].present?
                         pagy(titles.search_by_name(params[:search_text]), items: params[:per_page])
                       else
                         pagy(titles.order('name ASC'), items: params[:per_page])
                       end
        render_success(data: titles, options: { meta: pagy_headers_hash(pagy) })
      end

      def create
        title = Title.new(resource_params)
        title.save
        render_json(data: title)
      end

      def update
        @title.update(resource_params)
        render_json(data: @title)
      end

      def destroy
        @title.discard
        render_json(data: @title)
      end

      private

      def resource_params
        params.require(:title).permit(:name, :department_id, :section_id, :description, :title_code)
      end

      def set_title
        @title = Title.kept.friendly.find(params[:id])
      end
    end
  end
end
