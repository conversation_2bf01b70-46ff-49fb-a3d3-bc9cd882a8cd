# frozen_string_literal: true

module Api
  class ShortenUrlsController < Api::BaseController
      skip_load_and_authorize_resource only: :redirect
      skip_before_action :authenticate_and_set_user, only: :redirect
      before_action :validate_url, only: :create
      before_action :set_shorten_url_by_key, only: :redirect

      def create
        shorten_url = ShortenUrl.new(resource_params)
        shorten_url.hashed_key = SecureRandom.alphanumeric(6)
        # shorten_url.shortened_url = "#{ENV['REDIRECT_URL']}#{shorten_url.hashed_key}"
        shorten_url.shortened_url = "http://nyccoba.lvh.me:3001/shorten_urls/#{shorten_url.hashed_key}"
        shorten_url.save!
        render_json(data: shorten_url)
      end

      def redirect
        return render_error('Invalid URL') unless @shorten_url.present?
        redirect_to @shorten_url.origin_url
      end

      private

      def resource_params
        params.require(:shorten_url).permit(:origin_url)
      end

      def set_shorten_url_by_key
        @shorten_url = ShortenUrl.find_by(hashed_key: params[:key])
      end

      def validate_url
        render_error('Invalid URL') unless params[:shorten_url][:origin_url].present? && params[:shorten_url][:origin_url].start_with?('http')
      end
  end
end
