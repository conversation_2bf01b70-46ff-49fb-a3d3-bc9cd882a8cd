# frozen_string_literal: true

module Api
  class UserRightsController < Api::BaseController
    skip_load_and_authorize_resource

    def index
      rights = @current_user.role.rights.pluck(:name)
      if (domain = request.headers['HTTP_NATS_SUBDOMAIN']) && domain == 'utlo'
        utlo_rights = %w[read_employee write_employee report_single_employee read_report_single_employee
                         read_change_request write_change_request]
        rights = @current_user.role.rights.pluck(:name) & [ utlo_rights + Right::NOTIFICATION_RIGHTS + Right::USER_RIGHTS + Right::SUPER_USER_RIGHTS ].flatten
        rights += Right::SETTINGS_RIGHTS if @current_user.role&.name&.downcase == 'admin'

        ## The above logics implemented based on the below asana card:
        ## https://app.asana.com/0/1199593218818077/1209004097277767/f
      end
      # rights -= %w[write_super_user read_super_user] if (rights & %w[write_user read_user]).blank?
      render_success(data: rights)
    end

    def roles_list
      render_success(data: Role.kept.all)
    end
  end
end
