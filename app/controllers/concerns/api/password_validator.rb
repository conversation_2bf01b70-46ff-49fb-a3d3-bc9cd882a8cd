# frozen_string_literal: true

module Api
  module PasswordValidator
    def password_params_validator
      if params[:user][:email].blank?
        render_error('Email can\'t be blank')
      else
        @current_user = User.kept.find_by(email: params[:user][:email].downcase)
        render_error('Invalid email') unless @current_user
      end
    end

    def employee_password_params_validator
      if params[:employee][:email].blank?
        render_error('Email can\'t be blank')
      else
        work_or_personal = current_account.saas_json.dig('schema', 'employees', 'login_credentials', 'sent_credentials_to_work_email') == true ? 'work' : 'personal'
        contact = Contact.kept.where("lower(value) = ? AND contact_for = '#{work_or_personal&.downcase}'", params[:employee][:email].downcase).first
        @current_employee = Employee.kept.find_by(id: contact.employee_id) if contact.present?
        render_error('Your Email address is missing. Please contact your Admin') if @current_employee == nil
      end
    end
  end
end
