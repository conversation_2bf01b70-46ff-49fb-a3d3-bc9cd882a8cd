# frozen_string_literal: true

class UserDecorator
  def initialize(user)
    @user = user
  end

  def name
    return @name if @name.present?

    @name = user.first_name
    @name = "#{@name} #{user.last_name}"
  end

  def cellphone
    @cellphone ||= user.user_contacts.where(contact_type: 'phone', contact_for: 'personal').first&.value
  end

  def home_phone
    @home_phone ||= user.user_contacts.where(contact_type: 'phone', contact_for: 'home').first&.value
  end

  def work_phone
    @work_phone ||= user.user_contacts.where(contact_type: 'phone', contact_for: 'work').first&.value
  end

  def profile_image
    subdomain = ENV['MANDATORY_SUBDOMAIN'].present? ? "#{Apartment::Tenant.current}.#{ENV['MANDATORY_SUBDOMAIN']}" : Apartment::Tenant.current # rubocop:disable Layout/LineLength
    Rails.application.routes.url_helpers.rails_representation_url(user.avatar.variant(resize: '80x80'), subdomain: subdomain)
  end

  def method_missing(meth, *args) # rubocop:disable Style/MissingRespondToMissing
    meth_arr_str = meth.to_s.split('_')

    return super unless meth_arr_str.last == 'name'

    final_meth = "latest_#{(meth_arr_str.first meth_arr_str.size - 1).join('_')}"

    return super unless defined?(final_meth)

    data = send(final_meth, *args)

    return data.name if data.present?

    ''
  end

  private

  attr_reader :user
end
