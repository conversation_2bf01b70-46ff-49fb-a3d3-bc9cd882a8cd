module ChangeRequest<PERSON><PERSON><PERSON>
  def requested_changes(object, params, status)
    array_of_requested_changes = []
    object.requested_changes&.each do |requested_change|
      @name_hash = {}
      @benefit_name_and_full_name_hash = {}

      if params[:custom_keys] == "employee" && params[:custom_keys] != "user"
        requested_change.each do |key, value|
          possible_ids_hash = %w(affiliation_id benefit_id delegate_series_id department_id discipline_charge_id
                                    discipline_setting_id discipline_status_id employment_status_id firearm_status_id
                                    gender_id grievance_status_id grievance_id marital_status_id meeting_type_id
                                    officer_status_id office_id pacf_id payment_type_id platoon_id position_id rank_id
                                    section_id title_id tour_of_duty_id unit_id officer_id delegate_employee_id)

          if possible_ids_hash.include?(key)
            split_key = key.split('_')
            split_key.pop
            if key == 'delegate_employee_id'
              model_name_value = Employee.kept.find_by(id: value).name if Employee.kept.find_by(id: value).present?
            else
              model = split_key.join('_').singularize.camelize.constantize unless key == 'delegate_employee_id'
              model_name_value = model.kept.find_by(id: value).name if model.kept.find_by(id: value).present?
            end
            push_key_value = split_key.push('name')
            model_name = push_key_value.join('_')
            @name_hash.merge!(model_name => model_name_value) if model_name.present? && model_name_value.present?
          end
        end
        requested_change.merge!(@name_hash) if @name_hash.present?
      end

      requested_change.each do |key, value|
        if object.request_type == 'benefit_coverage' && key == 'employee_benefit_id'
          benefit_name = EmployeeBenefit.where(id: value).first.benefit.name
          @benefit_name_and_full_name_hash['benefit_name'] = benefit_name if benefit_name.present?
        end
        if object.request_type == 'benefit_coverage'
          next unless requested_change.key?('id')

          benefit_coverage_id = requested_change['id']
          benefit_coverage = BenefitCoverage.find(benefit_coverage_id)
          full_name = [ benefit_coverage.first_name, benefit_coverage.suffix, benefit_coverage.last_name ].join(' ')
          full_name = benefit_coverage.name if full_name.blank?
          @benefit_name_and_full_name_hash['full_name'] = full_name if benefit_coverage.present?
        end
      end

      requested_change.merge!(@benefit_name_and_full_name_hash) if @benefit_name_and_full_name_hash.present?
      requested_change['files'] = FileBuilder.process_multi_files(object.change_request_uploads.find_by(timestamp: requested_change['timestamp'])) if requested_change['files'].present?
      array_of_requested_changes << requested_change if requested_change['status'] == status
    end

    array_of_requested_changes
  end
end