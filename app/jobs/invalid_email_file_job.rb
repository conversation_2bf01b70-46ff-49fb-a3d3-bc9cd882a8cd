# frozen_string_literal: true
require 'csv'
class InvalidEmailFile<PERSON>ob < ApplicationJob
  def perform(account, invalid_emails, time)
    filename = "#{account}_invalid_emails#{Time.now.strftime('%Y%m%d%H%M')}.csv"
    filepath ||= "#{Rails.root}/#{filename}"

    CSV.open(filepath, 'w') do |csv|
      csv << ["Emails"]
      invalid_emails.each do |email|
        csv << [ email ]
      end
    end
    if Account.find_by(subdomain: account)&.saas_json&.dig('schema', 'employees', 'sendgrid_templates') == true
      SendgridService.new.send_single_email(
        recipient_email: '<EMAIL>', dynamic_contents: { 'email' => 'Report for invalid emails', 'account' => account, 'date_time' => time.strftime("%Y/%m/%d %H:%M:%S") },
        template_id: ENV['INVALID_EMAIL_REPORT_TEMPLATE_ID'], current_tenant: account, subject: 'Report for invalid emails', attachments: [filename]
        )
    else
      SendInvalidEmailReportMailer.send_invalid_emails(account, filename, time).deliver_later
    end
  end
end