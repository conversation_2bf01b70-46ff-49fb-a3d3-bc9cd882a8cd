# frozen_string_literal: true

class ReminderJob < ApplicationJob
  queue_as :default

  after_perform :update_reminder_status

  def perform(reminder_id, domain_name)
    @reminder_id = reminder_id
    reminder = Reminder.find(reminder_id)
    return if reminder.status == 'completed'

    @current_tenant = domain_name
    return unless @current_tenant.present?

    email_user_ids = reminder.users.map(&:id).uniq

    Apartment::Tenant.switch(@current_tenant) do
      email_user_ids.each do |email_user_id|
        user = User.kept.find_by(id: email_user_id)
        next unless user&.email.present?

        reminder_tracker_id = fetch_tracker(user.id, reminder.id)
        if Account.find_by(subdomain: @current_tenant)&.saas_json&.dig('schema', 'employees', 'sendgrid_templates') == true
          SendgridService.new.send_single_email(
            recipient_email: user.email, dynamic_contents: { "header_text_1" => reminder.description || reminder.title },
            sender_address: reminder.creator&.email&.split('@')&.last&.downcase == @current_tenant&.downcase && reminder.employee_grievance_id.nil? ? reminder.creator&.email : nil,
            template_id: ENV['REMINDER_TEMPLATE_ID'], current_tenant: @current_tenant, subject: reminder.title,
            )
        else
          ReminderMailer.send_reminder(reminder_tracker_id, reminder.id, user.id, user.email).deliver_later
        end

        ActionCable.server.broadcast(
          "#{@current_tenant}_reminder_notifications_#{user.id}",
          JSON.parse(reminder_json(reminder)).merge!(status_code: 200)
        )
      end
    end
  end

  private

  def reminder_json(reminder)
    Api::ReminderSerializer.new(reminder).serialized_json
  end

  def fetch_tracker(user_id, reminder_id)
    Apartment::Tenant.switch(@current_tenant) do
      reminder_tracker = ReminderTracker.create!(user_id: user_id, reminder_id: reminder_id)
      reminder_tracker.id
    end
  end

  def update_reminder_status
    Apartment::Tenant.switch(@current_tenant) do
      reminder = Reminder.find(@reminder_id)
      return if reminder.status == 'completed'

      if reminder.repeat.nil? || (reminder.repeat['type'] == 'never') || (reminder.reminder_end_date && reminder.reminder_end_date.to_date <= Date.today)
        reminder.update_columns(status: 'completed', job_id: nil)
      else
        reminder.update_columns(status: 'pending', job_id: nil)
      end
    end
  end
end
