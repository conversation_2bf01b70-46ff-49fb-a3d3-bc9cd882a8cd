# frozen_string_literal: true

class ResetPasswordInstructionsMailer < ApplicationMailer
  def reset_password_instructions(record, token)
    @current_user = record
    @token = token

    # Domain based from email configuration
    domain = Account.find_by_subdomain(Apartment::Tenant.current).domain_name || 'myfusesystems.com'
    from_email = 'no-reply@' + domain

    mail from: from_email, to: @current_user.email, subject: 'Reset password instructions'
  end
end
