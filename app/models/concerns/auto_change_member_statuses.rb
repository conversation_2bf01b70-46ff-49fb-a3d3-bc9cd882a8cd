# frozen_string_literal: true

module AutoChangeMemberStatuses

  def member_status
    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    auto_dues_status = current_account.saas_json.dig('schema', 'employee_pacfs', 'auto_dues_status')

    if auto_dues_status.present? && auto_dues_status == true
      employment_statuses = employee_employment_status
      if employment_statuses.present?
        employment_statuses.each do |employment_status|
          name = employment_status.name.split(' - ') if employment_status.present?

          due = EmploymentStatus.kept.where('name ilike ?', "#{name[0]} - Dues paying") if name.present?
          non_due = EmploymentStatus.kept.where('name ilike ?', "#{name[0]} - Non-Dues paying") if name.present?
          due_id = due.first.id if due.present?
          non_due_id = non_due.first.id if non_due.present?

          if self.pacf.name.downcase == "dues paying" && (due_id.present? || non_due_id.present?)
            employee_employment_status = employment_statuses.where('employment_status_id = ?', non_due_id)
            employee_employment_status.update(employment_status_id: due_id) if employee_employment_status.present?
          elsif self.pacf.name.downcase == "non-dues paying" && (due_id.present? || non_due_id.present?)
            employee_employment_status = employment_statuses.where('employment_status_id = ?', due_id)
            employee_employment_status.update(employment_status_id: non_due_id) if employee_employment_status.present?
          end
        end
      elsif !employment_statuses.present?
        member_status_due = EmploymentStatus.kept.where('name ilike ?', "Active - Dues paying")
        member_status_non_due = EmploymentStatus.kept.where('name ilike ?', "Active - Non-Dues paying")

        if self.pacf.name.downcase == "dues paying" && (member_status_due.present? || member_status_non_due.present?)
          employee_employment_status = self.employee.employee_employment_statuses.new(employment_status_id: member_status_due.first.id)
          employee_employment_status.save(validate: false)
        elsif self.pacf.name.downcase == "non-dues paying" && (member_status_due.present? || member_status_non_due.present?)
          employee_employment_status = self.employee.employee_employment_statuses.new(employment_status_id: member_status_non_due.first.id)
          employee_employment_status.save(validate: false)
        end
      end
    end
  end

  def employee_employment_status
    employee_employment_status = nil

    employment_statuses = self.employee.employee_employment_statuses.where('end_date is NUll OR end_date >= ?', Date.today)
    employment_status_ids = employment_statuses.pluck(:employment_status_id) if employment_statuses.present?
    employment_status = EmploymentStatus.where(id: employment_status_ids).where("name ilike '%paying'") if employment_status_ids.present?
    employee_employment_status = employment_statuses.where(employment_status_id: employment_status.ids) if employment_status.present?

    employee_employment_status
  end
end