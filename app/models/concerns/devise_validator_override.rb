# frozen_string_literal: true

module DeviseValidatorOverride
  extend ActiveSupport::Concern

  included do
    # Customised validations from devise validatable
    extend Devise::Models::Validatable::ClassMethods

    validates_presence_of :email, if: :email_required?
    validates_uniqueness_of :email,
                            allow_blank: true, case_sensitive: true,
                            conditions: -> { where(discarded_at: nil) }, if: :will_save_change_to_email?
    validates_format_of :email, with: email_regexp, allow_blank: true, if: :will_save_change_to_email?

    validates_presence_of :password, if: :password_required?
    validates_confirmation_of :password, if: :password_required?
    validates_length_of :password, within: password_length, allow_blank: true
  end

  def email_required?
    true
  end

  def password_required?
    !persisted? || !password.nil? || !password_confirmation.nil?
  end
end
