# frozen_string_literal: true

class Department < ApplicationRecord
  include SearchByName
  extend FriendlyId

  friendly_id :name, use: :slugged

  # ============================= Associations =========================================================================
  has_many :sections, -> { where(sections: { discarded_at: nil }) }
  has_many :titles, -> { where(titles: { discarded_at: nil }) }
  has_many :employee_departments, -> { where(employee_departments: { discarded_at: nil }) }
  has_many :employee_titles, -> { where(employee_titles: { discarded_at: nil }) }, through: :titles
  has_many :employee_sections, -> { where(employee_sections: { discarded_at: nil }) }, through: :sections
  has_many :employees, -> { where(employees: { discarded_at: nil }) }

  # ============================= Validations ==========================================================================
  validates :name, uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false },
                   length: { maximum: 100 }, if: -> { name_changed? }

  # ============================= Callbacks ============================================================================
  after_discard do
    employee_departments.update_all(discarded_at: Time.current)
    employee_sections.update_all(discarded_at: Time.current)
    employee_titles.update_all(discarded_at: Time.current)
    sections.update_all(discarded_at: Time.current)
    titles.update_all(discarded_at: Time.current)
    employees.unscoped.where(department_id: self.id).update_all(department_id: :nil)
  end
end
