class Dependent < ApplicationRecord
  include Validation
  # ============================= Associations =========================================================================
  belongs_to :employee
  belongs_to :life_insurance

  # ============================= Callbacks ============================================================================
  before_save :add_default_age, :spouse_contribution_value, :check_relationship
  before_save :check_amount, if: -> { amount_changed? or relationship_changed? }

  private

  def add_default_age
    now = Time.now
    self.age = now.year - self.date.to_time.year - (self.date.to_time.change(:year => now.year) > now ? 1 : 0) if date
  end

  def spouse_contribution_value
    dependent_amount = current_account.saas_json.dig('schema', 'dependents', 'spouse_contribution_value', 'amount')

    if self.amount.present? && dependent_amount.present?
      dependent_amount.each do |key_amount|
        if key_amount[0].to_i == self.amount.round
          self.spouse_contribution = key_amount[1]
        end
      end
    end
  end
end
