# frozen_string_literal: true

class Device < ApplicationRecord
  # ============================= Enum =================================================================================
  enum status: { enabled: 0, disabled: 1 }

  # ============================= Associations =========================================================================
  belongs_to :employee

  # ============================= Validations ==========================================================================
  validates :os_type, :os_version, :device_token, :endpoint_arn, presence: true

  # ============================= Callbacks ============================================================================
  before_destroy :delete_device_endpoint
  after_save :update_app_downloaded

  # ============================= Methods ==============================================================================
  def delete_device_endpoint
    return if Rails.env.test?

    push_notification_service = PushNotificationService.new
    push_notification_service.delete_endpoint_arn(endpoint_arn)
  end

  def update_app_downloaded
    return if employee.app_downloaded == true

    employee.update_columns(app_downloaded: true)
  end
end
