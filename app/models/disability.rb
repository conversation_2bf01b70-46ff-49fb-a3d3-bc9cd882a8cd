# frozen_string_literal: true

class Disability < ApplicationRecord
  # ============================= Associations =========================================================================
  belongs_to :employee
  has_one_attached :file

  # ============================= Callbacks ============================================================================
  before_save :disability_expiration

  private

  def disability_expiration
    disability_auto_expire = current_account.saas_json.dig('schema', 'disabilities', 'disability_auto_expire')
    auto_expire_week = current_account.saas_json.dig('schema', 'disabilities', 'auto_expire_week')
    auto_expire_week ||= 26
    self.auto_expire = false if expires_at_changed? && self.auto_expire == true
    if disability_auto_expire == true && self.from_date.present? && self.auto_expire == true
      self.expires_at = self.from_date + (auto_expire_week * 7)
    end
    end_date = self.to_date.present? ? self.to_date : Date.today
    self.duration = (end_date - self.from_date).to_i if self.from_date.present?
  end
end
