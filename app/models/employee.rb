# frozen_string_literal: true

class Employee < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable, :recoverable, :rememberable, :trackable
  attr_accessor :is_auto_prescription
  attr_accessor :update_beneficiary_address
  include PgSearch::Model
  include EmployeeDeviseValidatorOverride
  include Api::SetBenefitCoverageExpiration
  extend FriendlyId

  friendly_id :slug_candidates, use: :slugged

  delegate(
    :benefit_disbursements_total_amount,
    :cellphone,
    :employment_status_name,
    :firearm_status_name,
    :home_phone,
    :name,
    :last_first_middle_name,
    :office_address,
    :office_name,
    :officer_status_name,
    :personal_email,
    :work_email,
    :position_name,
    :rank_name,
    :title_name,
    :department_name,
    :section_name,
    :pacf_name,
    :discipline_setting_name,
    :grievance_name,
    :work_phone,
    to: :employee_decorator
  )

  # ============================= Scopes ===============================================================================
  scope :order_by_name, -> { reorder('employees.last_name ASC, employees.first_name ASC, employees.middle_name ASC') }
  default_scope -> { where(category: 'employee') }
  scope :filter_contact_persons, -> { where(category: 'contact_person') }
  scope :filter_legislative_contacts, -> { where(category: 'legislative_contacts') }

  # TODO: Check and make the associated tables dynamic based on the account preference as we have done for 'against' option.
  # Github issue - https://github.com/Casecommons/pg_search/issues/440
  pg_search_scope :search_by_employee, lambda { |against, associated_against, query|
    {
      against: against,
      associated_against: associated_against,
      query: query,
      using: { tsearch: { prefix: true, normalization: 2 }, trigram: { word_similarity: true, threshold: 0.3 } },
      ranked_by: ":tsearch + :trigram + :tsearch"
    }
  }
  pg_search_scope :search_by_full_name,
                  against: %i[first_name middle_name last_name suffix maiden_name],
                  using: { tsearch: { prefix: true, normalization: 2  }, trigram: { word_similarity: true, threshold: 0.3 } },
                  ranked_by: ":trigram + :tsearch"

  pg_search_scope :employee_exact_search, lambda { |query, options = {}|
    against = options[:against]
    associated_against = options[:associated_against] || {}
    {
      against: against,
      associated_against: associated_against,
      query: query,
      order_within_rank: 'employees.first_name ASC, employees.last_name ASC, employees.middle_name ASC'
    }
  }

  # ============================= Associations =========================================================================

  enum t_shirt_size: %w[Small Medium Large XL XXL XXXL]
  enum category: [:employee, :contact_person, :legislative_contacts]

  belongs_to :gender, optional: true
  belongs_to :marital_status, optional: true
  belongs_to :unit, optional: true
  belongs_to :affiliation, optional: true
  belongs_to :tour_of_duty, optional: true
  belongs_to :platoon, optional: true
  belongs_to :department, optional: true
  belongs_to :legislative_address, optional: true
  has_many :devices, -> { where(devices: { discarded_at: nil }) }, dependent: :destroy
  has_many :analytics_configurations, -> { where(analytics_configurations: { discarded_at: nil }) }
  has_many :awards
  has_many :assaults, -> { where(assaults: { discarded_at: nil }) }
  has_many :beneficiaries, -> { where(beneficiaries: { discarded_at: nil }) }
  has_many :benefit_coverages, -> { where(benefit_coverages: { discarded_at: nil }) }
  has_many :benefit_disbursements, -> { where(benefit_disbursements: { discarded_at: nil }) }
  has_many :comp_leaves, -> { where(leaves: { type: Leave::LeaveTypes::COMP_TIME, discarded_at: nil }) }, class_name: 'Leave'
  has_many :contacts, -> { where(contacts: { discarded_at: nil }) }, inverse_of: :employee
  has_many :disciplines
  has_many :disabilities
  has_many :delegate_assignments, -> { where(delegate_assignments: { discarded_at: nil }) }
  has_many :employee_benefits, -> { where(employee_benefits: { discarded_at: nil }) }
  has_many :employee_discipline_settings, -> { where(employee_discipline_settings: { discarded_at: nil }) }
  has_many :employee_grievances, -> { where(employee_grievances: { discarded_at: nil }) }
  has_many :employee_employment_statuses, -> { where(employee_employment_statuses: { discarded_at: nil }) }
  has_many :employee_firearm_statuses, -> { where(employee_firearm_statuses: { discarded_at: nil }) }
  has_many :employee_pacfs, -> { where(employee_pacfs: { discarded_at: nil }) }
  has_many :employee_meeting_types, -> { where(employee_meeting_types: { discarded_at: nil }) }
  has_many :employee_officer_statuses, -> { where(employee_officer_statuses: { discarded_at: nil }) }
  has_many :employee_offices, -> { where(employee_offices: { discarded_at: nil }) }
  has_many :employee_positions, -> { where(employee_positions: { discarded_at: nil }) }
  has_many :employee_ranks, -> { where(employee_ranks: { discarded_at: nil }) }
  has_many :employee_departments, -> { where(employee_departments: { discarded_at: nil }) }
  has_many :employee_sections, -> { where(employee_sections: { discarded_at: nil }) }
  has_many :employee_titles, -> { where(employee_titles: { discarded_at: nil }) }
  has_many :employee_facilities, -> { where(employee_facilities: { discarded_at: nil }) }
  has_many :firearm_range_scores, -> { where(firearm_range_scores: { discarded_at: nil }) }
  has_many :leaves, -> { where(leaves: { discarded_at: nil }) }, class_name: 'Leave'
  has_many :lodis, -> { where(lodis: { discarded_at: nil }) }
  has_many :officer_statuses, through: :employee_officer_statuses
  has_many :offices, through: :employee_offices
  has_many :ranks, through: :employee_ranks
  has_many :overtimes, -> { where(leaves: { type: Leave::LeaveTypes::OVERTIME, discarded_at: nil }) }, class_name: 'Leave'
  has_many :personal_leaves, -> { where(leaves: { type: Leave::LeaveTypes::PERSONAL, discarded_at: nil }) }, class_name: 'Leave'
  has_many :sections, -> { where(sections: { discarded_at: nil }) }
  has_many :sick_leaves, -> { where(leaves: { type: Leave::LeaveTypes::SICK, discarded_at: nil }) }, class_name: 'Leave'
  has_many :totalities, -> { where(totalities: { discarded_at: nil }) }
  has_many :vacation_leaves, -> { where(leaves: { type: Leave::LeaveTypes::VACATION, discarded_at: nil }) }, class_name: 'Leave'
  has_many :uploads
  has_many :employee_delegate_assignments, class_name: 'DelegateAssignment', foreign_key: 'delegate_employee_id'
  has_many :titles, through: :employee_titles
  has_many :facilities, through: :employee_facilities
  has_many :employment_statuses, through: :employee_employment_statuses
  has_many :positions, through: :employee_positions
  has_many :dependents, -> { where(dependents: { discarded_at: nil }) }
  has_many :life_insurances, -> { where(life_insurances: { discarded_at: nil }) }, dependent: :destroy
  has_many :peshes, -> { where(peshes: { discarded_at: nil }) }
  has_one_attached :avatar
  has_many :change_requests, -> { where(change_requests: { discarded_at: nil }) }
  has_one :mailing_address
  has_one :poly_note, as: :notable
  has_many :user_audits
  accepts_nested_attributes_for :contacts
  accepts_nested_attributes_for :mailing_address

  # ============================= Validations ==========================================================================
  validates :avatar, blob: { content_type: /\Aimage\/.*\z/ }
  validates_length_of :zipcode, maximum: 5, allow_blank: true
  # Validates uniqueness of SSN only when whole number(9 characters) is being saved in columns.
  validates :social_security_number, uniqueness: { conditions: -> { where(discarded_at: nil) } },
                                     if: -> { social_security_number_changed? && current_account.saas_json.dig('schema', 'employees', 'social_security_number_format') == '9' }, allow_blank: true
  validates :password_confirmation, presence: true, if: -> { encrypted_password_changed? }
  validates :category, presence: true
  validates :first_name, :last_name, format: { with: /\A[a-zA-Z\d\s\u00C0-\u017F'*&()_,.%\#\`\/-]+\z/ },
                                     unless: -> { category == 'contact_person' && current_account.saas_json.dig('schema', 'contact_persons', 'skip_name_validations') == true }
  validates :middle_name, format: { with: /\A[a-zA-Z\d\s\u00C0-\u017F'*&()_,.%\#\`\/-]+\z/ }, allow_blank: true
  validates :street, :city, :state, format: { with: /\A[a-zA-Z\d\s\u00C0-\u017F'*&()_,.%\#\`\/-]+\z/ }, allow_blank: true

  # ============================= Callbacks ============================================================================
  before_validation :payroll_id_autoload
  before_save :a_number_autoload
  before_save :update_issued_at_time, if: :encrypted_password_changed?
  before_save :update_prescription
  before_save :update_address_to_mailing_address
  after_save :create_analytic_config, if: -> { previous_changes.key?('staff_member') }
  after_save :set_expiration_for_mobile_access_token, if: -> { enable_mobile_access == false }
  after_save :update_benefit_address, if: -> { address_changed? && update_beneficiary_address }
  before_update :update_previous_address, if: -> { address_changed?('before') && current_account.saas_json.dig('schema', 'employees', 'update_previous_address') == true }
  after_save :update_coverage_marital_status
  after_commit :update_legislative_detail, if: -> { address_changed? && current_account.saas_json.dig('schema', 'legislative_addresses').present? }

  after_discard do
    awards.update_all(discarded_at: Time.current)
    analytics_configurations.update_all(discarded_at: Time.current)
    benefit_coverages.update_all(discarded_at: Time.current)
    dependents.update_all(discarded_at: Time.current)
    benefit_disbursements.update_all(discarded_at: Time.current)
    contacts.update_all(discarded_at: Time.current)
    delegate_assignments.update_all(discarded_at: Time.current)
    disciplines.update_all(discarded_at: Time.current)
    disabilities.update_all(discarded_at: Time.current)
    employee_benefits.update_all(discarded_at: Time.current)
    employee_employment_statuses.update_all(discarded_at: Time.current)
    employee_firearm_statuses.update_all(discarded_at: Time.current)
    employee_meeting_types.update_all(discarded_at: Time.current)
    employee_discipline_settings.update_all(discarded_at: Time.current)
    employee_grievances.update_all(discarded_at: Time.current)
    employee_pacfs.update_all(discarded_at: Time.current)
    employee_departments.update_all(discarded_at: Time.current)
    employee_sections.update_all(discarded_at: Time.current)
    employee_titles.update_all(discarded_at: Time.current)
    employee_officer_statuses.update_all(discarded_at: Time.current)
    employee_offices.update_all(discarded_at: Time.current)
    employee_positions.update_all(discarded_at: Time.current)
    employee_ranks.update_all(discarded_at: Time.current)
    employee_facilities.update_all(discarded_at: Time.current)
    firearm_range_scores.update_all(discarded_at: Time.current)
    leaves.update_all(discarded_at: Time.current)
    lodis.update_all(discarded_at: Time.current)
    life_insurances.update_all(discarded_at: Time.current)
    peshes.update_all(discarded_at: Time.current)
    assaults.discard_all
    peshes.update_all(discarded_at: Time.current)
    totalities.update_all(discarded_at: Time.current)
    change_requests.update_all(discarded_at: Time.current)
    mailing_address&.update_column(:discarded_at, Time.current)
    poly_note&.update_column(:discarded_at, Time.current)
    uploads.destroy_all
    set_expiration_for_mobile_access_token
    devices.destroy_all
    update(username: nil)
  end

  def slug_candidates
    [
      :first_name,
      %i[first_name middle_name],
      %i[first_name middle_name last_name]
    ]
  end

  def employee_address
    address = []
    address.push(city) if city.present?
    address.push(state) if state.present?
    address = address.join(', ')
    address += ' ' + zipcode if zipcode.present?
    address
  end

  def full_address
    address = []
    address << street if street.present?
    address << apartment if apartment.present?
    address << city if city.present?
    address << state if state.present?
    address = address.join(', ')
    address += ' ' + zipcode if zipcode.present?
    address << ', ' + county if county.present?
    address
  end

  def full_name
    [first_name, middle_name, last_name, suffix].reject { |x| x.blank? }.join(' ')
    # if middle_name.blank?
    #   name = first_name + ' ' + last_name
    # else
    #   name = first_name + ' ' + middle_name + ' ' + last_name
    # end
    #
    # name
  end

  def send_reset_password_instructions(email, otp)
    token = set_reset_password_token
    if current_account.saas_json.dig('schema', 'employees', 'sendgrid_templates') == true
      SendgridService.new.send_single_email(
        recipient_email: email, template_id: ENV['EMPLOYEE_FORGOT_PASSWORD_TEMPLATE_ID'], current_tenant: Apartment::Tenant.current, subject: 'Reset password instructions',
        dynamic_contents: { email: email, reset_link:  "#{ENV['ACTIVE_DOMAIN_PROTOCOL']}://#{Apartment::Tenant.current}.#{ENV['REACT_HOST']}/employees/reset-password?reset_password_token=#{token}", }
      )
    else
      ResetEmployeePasswordInstructionsMailer.employee_reset_password_instructions(token, email, otp).deliver_later
    end

    token
  end

  def update_issued_at_time
    self.password_changed_at = Time.now.utc
  end

  def self.only_edit_fields
    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    current_account.saas_json['schema']['employees']['permitted_editable_fields']&.values&.flatten
  end

  def self.notes_search_filter(employee, search_text)
    note_hash = []
    if search_text.present?
      employee_notes = employee.versions.where('object_changes ilike ?', "%notes%#{search_text}%").reorder('created_at DESC')
      employee_notes.each do |version|
        object = YAML.load(version.object_changes)
        note = object['notes'].last

        next unless note.downcase.include?(search_text.downcase)

        user = User.find_by(id: version.whodunnit) unless version.whodunnit.nil?
        note_hash << {
          name: user&.username,
          timestamp: version.created_at,
          updated_text: note
        }
      end
    end
    note_hash
  end

  private

  def employee_decorator
    @employee_decorator ||= EmployeeDecorator.new(self)
  end

  def benefit_disbursements_decorator
    @benefit_disbursements_decorator ||= BenefitDisbursementsDecorator.new(benefit_disbursements)
  end

  def a_number_autoload
    healthplex_autoload = current_account.saas_json['schema']['employees']['healthplex_autoload']
    if healthplex_autoload.present? && healthplex_autoload == true && ((self.a_number.present? && self.a_number == 'BTO') || self.a_number.blank?)
      if Employee.kept.last.present? && Employee.kept.maximum(:a_number).present?
        healthplex_number = Employee.kept.maximum(:a_number)
        object = healthplex_number.delete(' ').split(/(?<=[A-Za-z])(?=\d)/)
        object[0].concat((object[1].to_i + 1).to_s.rjust(object[1].length, '0'))
        self.a_number = object[0]
      else
        self.a_number = 'BTO000001'
      end
    end
  end

  def update_prescription
    employees = Account.find_by(subdomain: Apartment::Tenant.current).saas_json['schema']['employees']
    if employees['autoload_prescription'].present? && employees['autoload_prescription'] == true && self.social_security_number.present? && self.is_auto_prescription == 'true' && self.prescription.present? == false
      ssn_value = self.social_security_number.gsub('-', '').to_i
      calculated_value = (ssn_value / employees['prescription_calc']).round.to_s
      self.prescription = calculated_value.rjust(9, '0')
    end
  end

  def payroll_id_autoload
    member_id_autoload = current_account.saas_json['schema']['employees']['member_id_autoload']
    payroll_id_auto_align = current_account.saas_json.dig('schema', 'employees', 'payroll_auto_align')
    if member_id_autoload.present? && member_id_autoload == true && self.payroll_id.blank?
      if Employee.kept.last.present? && Employee.kept.maximum(:payroll_id).present?
        object_concat = ->(x) { (x.to_i + 1).to_s.rjust(x.length, '0') }
        employee_ids = Employee.kept.where.not("payroll_id IS NOT NULL AND payroll_id ~ '^\\d+$'").ids ## Here, We are considering only the non numeric values only to exclude in the next line.
        employee_ids << self.id
        payroll_number = Employee.kept.where.not(id: employee_ids).maximum(:payroll_id)
        object = payroll_number.delete(' ').split(/(?<=[A-Za-z])(?=\d)/)
        payroll_id = object[0].concat(object_concat.call(object[1])) if object.length > 1
        payroll_id = object_concat.call(payroll_number) if payroll_id.blank?
        if payroll_id.to_i <= 1159
          self.payroll_id = '*********'
        else
          self.payroll_id = payroll_id
        end
      end
    end
    if payroll_id_auto_align.present? && payroll_id_auto_align == true && self.payroll_id.present?
      self.payroll_id = self.payroll_id.rjust(9, '0')
    end
  end

  def create_analytic_config
    analytics_of_employee_user = current_account.saas_json.dig('schema', 'employees', 'analytics_of_employee_user') if current_account.present?
    if analytics_of_employee_user.present? && analytics_of_employee_user == true
      Leave::LEAVE_TYPES.each do |leave_type|
        duration_from, duration_to = AnalyticsConfiguration.analytic_current_year_range(leave_type)
        AnalyticsConfiguration.create(employee_id: id, days_earned: Leave::EARNED_LEAVES[leave_type], days_used: 0,
                                      analytics_type: leave_type, duration_to: duration_to, duration_from: duration_from)
      end
    end
  end

  def set_expiration_for_mobile_access_token
    Doorkeeper::AccessToken.where(resource_owner_id: self.id).update_all(revoked_at: Time.now)
  end

  def update_benefit_address
    beneficiaries.update_all(address: full_address)
    benefit_coverages.update_all(address: full_address)
  end

  public

  def update_legislative_detail
    AddLegislativeDetailJob.perform_later(self)
  end

  def update_previous_address
    if mailing_address.nil?
      create_mailing_address(
        street: street_was,
        apartment: apartment_was,
        state: state_was,
        city: city_was,
        zipcode: zipcode_was
      )
    else
      mailing_address.update!(
        street: street_was,
        apartment: apartment_was,
        state: state_was,
        city: city_was,
        zipcode: zipcode_was
      )
    end
  end

  def forgot_password_otp_generate
    otp = rand(999999).to_s.center(6, rand(9).to_s)
    self.forgot_password_otp = otp
    self.forgot_password_otp_send_at = Time.now.utc
    save(validate: false)
    otp
  end

  def forgot_password_otp_valid?
    (self.forgot_password_otp_send_at + 30.minutes) > Time.now.utc
  end

  def address_changed?(context = 'after')
    if context == 'before'
      will_save_change_to_city? || will_save_change_to_state? || will_save_change_to_apartment? ||
        will_save_change_to_street? || will_save_change_to_zipcode?
    else
      previous_changes.key?('city') || previous_changes.key?('state') || previous_changes.key?('apartment') ||
        previous_changes.key?('street') || previous_changes.key?('zipcode')
    end
  end

  # rubocop:disable Style/RedundantSelf: Redundant
  def update_coverage_marital_status
    return unless check_marital_status_divorced == true
    self.versions.reverse.each do |version|
      changeset = version.changeset
      if changeset.key?('marital_status_id')
        @previous_marital_status_id = changeset[:marital_status_id].first
        break
      end
    end
    return unless @previous_marital_status_id.present?

    old_marital_status_name = MaritalStatus.find(@previous_marital_status_id).name.downcase
    update_coverage(self, old_marital_status_name) if old_marital_status_name == 'married'
  end

  def update_address_to_mailing_address
    return unless same_as_mailing_address == true && current_account.saas_json.dig('schema', 'employees', 'same_as_mailing_address').present?

    self.create_mailing_address if self.mailing_address.blank?
    self.mailing_address.apartment = self.apartment
    self.mailing_address.city = self.city
    self.mailing_address.street = self.street
    self.mailing_address.zipcode = self.zipcode
    self.mailing_address.state = self.state
  end

  def self.app_links
    {
      btoba: {
        android: 'https://play.google.com/store/apps/details?id=com.app.fusebtoba',
        ios: 'https://apps.apple.com/us/app/BTOBA-FUSE/id1630914016'
      },
      nyscoa: {
        android: 'https://play.google.com/store/apps/details?id=com.app.nyscoafuse',
        ios: 'https://apps.apple.com/us/app/nyscoa-fuse/id6444044288'
      },
      papba: {
        android: 'https://play.google.com/store/apps/details?id=com.app.papbafuse',
        ios: 'https://apps.apple.com/us/app/papba-fuse/id6450109910'
      },
      utlo: {
        android: 'https://play.google.com/store/apps/details?id=com.app.utlofuse',
        ios: 'https://apps.apple.com/us/app/utlo-fuse/id6449016176'
      },
      nysscoa: {
        android: 'https://play.google.com/store/apps/details?id=com.app.nysscoafuse',
        ios: 'https://apps.apple.com/us/app/nysscoa-fuse/id1598312409'
      },
      sccoa: {
        android: 'https://play.google.com/store/apps/details?id=com.app.sccoafuse',
        ios: 'https://apps.apple.com/us/app/sccoa-fuse/id6450924033'
      },
      nyccoba: {
        android: 'https://play.google.com/store/apps/details?id=com.app.nyccobafuse',
        ios: 'https://apps.apple.com/us/app/nyc-coba-fuse/id6466620823'
      },
      local2507: {
        android: 'https://play.google.com/store/apps/details?id=com.app.local2507fuse',
        ios: 'https://apps.apple.com/us/app/local-2507-fuse/id6466620501'
      },
      sssa: {
        android: 'https://play.google.com/store/apps/details?id=com.app.sssafuse',
        ios: 'https://apps.apple.com/us/app/sssa-fuse/id6742632886'
      }
    }
  end

  def self.employee_status_detail
    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    employment_statuses_count = current_account.saas_json.dig('ui', 'employees', 'employment_statuses_count')
    valid_employees = Employee.kept.joins(employee_employment_statuses: :employment_status)
                                      .references(employee_employment_statuses: :employment_status)
                                      .where('employee_employment_statuses.end_date is null or employee_employment_statuses.end_date >= ?', Date.today)

    employment_status_count = valid_employees.where('lower(employment_statuses.name) in (?)', employment_statuses_count.map(&:downcase)) if employment_statuses_count.present?
    employment_status_count = employment_status_count.group('employment_statuses.name').count if employment_status_count.exists?
    dependents_count = current_account.saas_json.dig('ui', 'employees', 'dependents_count')

    if dependents_count.present? && employment_status_count.present?
      get_employment_status_id = ->(x) { EmploymentStatus.kept.where('lower(name) = ?', x).first&.id }
      active_status_id = get_employment_status_id.call('active')
      retire_status_id = get_employment_status_id.call('retired')
      disability_retire_status_id = get_employment_status_id.call('disability retired')
      military_leave_id = get_employment_status_id.call('military leave')
      benefit_coverages = BenefitCoverage.kept.joins(employee: :employee_employment_statuses)
                                         .where('employee_employment_statuses.end_date is NULL OR employee_employment_statuses.end_date > ?', Date.today)
      active_members_benefit_coverages = benefit_coverages.where('employee_employment_statuses.employment_status_id in (?)', [active_status_id, military_leave_id])
      key_to_modify = employment_status_count.keys.find { |k| k.to_s.downcase == 'active' }
      employment_status_count[key_to_modify] += valid_employees.where('employment_statuses.id = ?', military_leave_id).count
      employment_status_count['Active Member Dependents'] = active_members_benefit_coverages.where('lower(relationship) NOT IN (?)', ['member', 'divorced', 'not_eligible', 'death']).group(:first_name, :last_name, :employee_id).count.size
      employment_status_count['Retired Dependents'] = benefit_coverages.where('employee_employment_statuses.employment_status_id = ?', retire_status_id).group(:first_name, :last_name, :employee_id).count.size
      employment_status_count['Active Member Spouse'] = active_members_benefit_coverages.where('lower(relationship) = ?', 'spouse').group(:first_name, :last_name, :employee_id).count.size
      employment_status_count['Active Member Domestic Partner'] = active_members_benefit_coverages.where('lower(relationship) = ?', 'domestic_partner').group(:first_name, :last_name, :employee_id).count.size
      employment_status_count['Active Member Child Dependents'] = active_members_benefit_coverages.where('lower(relationship) in (?)', ['child', 'disabled_child', 'step_child', 'disabled_step_child']).group(:first_name, :last_name, :employee_id).count.size
      employment_status_count['Students'] = benefit_coverages.where(student: true).group(:first_name, :last_name, :employee_id).count.size
      employment_status_count['Active Member Students'] = benefit_coverages.where('employee_employment_statuses.employment_status_id in (?) and benefit_coverages.student = ?',
                                                                                  [active_status_id, military_leave_id], true).group(:first_name, :last_name, :employee_id).count.size
      employment_status_count['Retired Member Students'] = benefit_coverages.where('employee_employment_statuses.employment_status_id in (?) and benefit_coverages.student = ?',
                                                                                   [retire_status_id], true).group(:first_name, :last_name, :employee_id).count.size
      employment_status_count['Disability Retired Member Students'] = benefit_coverages.where('employee_employment_statuses.employment_status_id in (?) and benefit_coverages.student = ?',
                                                                                              [disability_retire_status_id], true).group(:first_name, :last_name, :employee_id).count.size
    end
    employment_status_count
  end

  # rubocop:enable Style/RedundantSelf: Redundant
end
