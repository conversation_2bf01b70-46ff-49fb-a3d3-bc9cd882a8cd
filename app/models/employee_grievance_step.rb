# frozen_string_literal: true

class EmployeeGrievanceStep < ApplicationRecord
  delegate :name, to: :grievance_status, allow_nil: true

  enum step: { step_1: 0, step_2: 1, step_3: 2, arbritration: 3 }

  # ================== Associations ====================================================================================
  belongs_to :employee_grievance
  belongs_to :grievance_status, optional: true
  has_many :hearings
  has_many_attached :files
  accepts_nested_attributes_for :hearings

  # ================== Validations =====================================================================================
  validates :files, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }
  validates :step, uniqueness: { conditions: -> { where(discarded_at: nil) }, scope: [:employee_grievance_id],
                                 message: 'Same step already exists' }
  # To not to allow updating step in update
  validates :step, inclusion: { in: ->(i) { [i.step_was] }, message: 'Step can\'t be updated' }, on: :update
  validate :validate_step_name
  validate :validate_total_steps, on: :create
  validate :validate_hearings_attributes, if: -> { current_account.saas_json.dig('schema', 'employee_grievances', 'grievances_specific_logics_for_papba') == true }

  #============================= Call Backs ============================================
  after_discard do
    hearings.update_all(discarded_at: Time.current)
  end
  def validate_total_steps
    return if errors.any?

    total_steps = employee_grievance.employee_grievance_steps.count
    return if total_steps <= grievances_steps.length

    errors.add(:step, 'Exceeded allowed steps')
  end

  def validate_step_name
    return if errors.any?

    return if grievances_steps.include?(step)

    errors.add(:step, 'Invalid step')
  end

  def validate_hearings_attributes
    return if errors.any? || hearings.blank?

    errors.add('Hearings', 'can only be created on step 3') unless self.step == 'step_2'
  end

  def grievances_steps
    @grievance_tabs ||= current_account.saas_json.dig('ui', 'employees', 'grievances', 'tabs') - ['grievances']
  end
end
