# frozen_string_literal: true

class Gender < ApplicationRecord
  include SearchByName
  extend FriendlyId

  friendly_id :name, use: :slugged

  # ============================= Associations =========================================================================
  has_many :employees
  has_many :benefit_coverages
  has_many :beneficiaries

  # ============================= Validations ==========================================================================
  validates :name, uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false },
                   length: { maximum: 100 }, if: -> { name_changed? }

  # ============================= Callbacks ============================================================================
  after_discard do
    employees.update_all(gender_id: :nil)
    benefit_coverages.update_all(gender_id: :nil)
    beneficiaries.update_all(gender_id: :nil)
  end
end
