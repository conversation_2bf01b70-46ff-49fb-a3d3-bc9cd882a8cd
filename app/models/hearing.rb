# frozen_string_literal: true

class Hearing < ApplicationRecord
  # ================== Associations ====================================================================================
  belongs_to :employee_grievance_step
  has_many_attached :transcripts
  has_many_attached :evidences
  has_many_attached :briefs
  attr_accessor :remove_all_transcripts
  attr_accessor :remove_all_evidences
  attr_accessor :remove_all_briefs

  # ================== Validations =====================================================================================
  validates :transcripts, :briefs, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }
  validates :evidences, blob: { content_type: ACCEPTABLE_CONTENT_TYPES + ['video/mp4'], size_range: 1..100.megabytes }
end
