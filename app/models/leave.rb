# frozen_string_literal: true

class Leave < ApplicationRecord
  attr_accessor :ended_at_date, :ended_at_time, :started_at_date, :started_at_time

  module LeaveTypes
    AVA = 'ava'
    COMP_TIME = 'comp_time'
    OVERTIME = 'overtime'
    PERSONAL = 'personal'
    SICK = 'sick'
    VACATION = 'vacation'
  end

  LEAVE_TYPES = [
    LeaveTypes::AVA,
    LeaveTypes::COMP_TIME,
    LeaveTypes::OVERTIME,
    LeaveTypes::PERSONAL,
    LeaveTypes::SICK,
    LeaveTypes::VACATION
  ].freeze

  EARNED_LEAVES = {LeaveTypes::AVA => 0, LeaveTypes::COMP_TIME => 0, LeaveTypes::OVERTIME => 0, LeaveTypes::PERSONAL => 0, LeaveTypes::SICK => 12, LeaveTypes::VACATION => 25}.freeze

  scope :ava_leaves, -> { kept.where(leave_type: LeaveTypes::AVA) }
  scope :comp_leaves, -> { kept.where(leave_type: LeaveTypes::COMP_TIME) }
  scope :overtime, -> { kept.where(leave_type: LeaveTypes::OVERTIME) }
  scope :personal_leaves, -> { kept.where(leave_type: LeaveTypes::PERSONAL) }
  scope :sick_leaves, -> { kept.where(leave_type: LeaveTypes::SICK) }
  scope :vacation_leaves, -> { kept.where(leave_type: LeaveTypes::VACATION) }

  delegate :total_hours_used, :totality, to: :leave_decorator

  # ============================= Associations =========================================================================
  belongs_to :employee
  has_many_attached :files

  # ============================= Validations ==========================================================================
  validates :leave_type, inclusion: { in: LEAVE_TYPES }
  validate :validate_date_range, if: :employee_user
  validates :files, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }

  # ============================= Callbacks ============================================================================
  after_save :compute_total_hours_used
  before_save :set_default_values, if: :employee_user
  after_save :calculate_total_days_used, unless: -> { previous_changes.key?('discarded_at') }, if: :employee_user
  after_update :recalculate_days_used_with_old_values, if: :employee_user
  after_save :update_analytic_configurations, if: :employee_user
  # ============================= Methods ==============================================================================
  private

  def leave_decorator
    @leave_decorator ||= LeaveDecorator.new(self)
  end

  def set_datetime(method, date, time)
    return if date.blank?

    final_time = time.present? ? time : '00:00:00'
    public_send("#{method}=", Time.parse("#{date} #{final_time} +0000"))
  end

  def compute_total_hours_used
    @totality = if totality.present?
                  totality.update(value: total_hours_used)
                else
                  Totality.create(employee_id: employee_id, totalable_type: leave_type, value: total_hours_used)
                end
    # reload # REMOVING ```reload``` SINCE IT WILL RELOAD THE ATTRIBUTES AND DELETED THE FILES BEFORE ITS BEEN PROCESSED
  end

  def validate_date_range
    return if errors.any?

    if started_at.present? && ended_at.present?
      end_date_is_greater_than_start_date = started_at > ended_at
      errors.add(:base, 'Date range is invalid - To date is greater than From date') if end_date_is_greater_than_start_date
      return if errors.any?

      diff_more_than_a_year = ended_at > (started_at + 1.year - 1.day)
      errors.add(:base, 'Date range is invalid - Date range is exceeds the allowed limit(1 year)') if diff_more_than_a_year
      return if errors.any?

      business_days = (started_at..ended_at).count { |date| (1..5).include?(date.wday) }
      errors.add(:base, 'Days given exceeds the business days in the selected date range') if business_days < days
    end
  end

  def set_default_values
    @leave_split_up = {}
    if leave_type == 'sick'
      set_default_for_fiscal_year
    else
      # Sets default value as zero for the years in which leave duration.
      if started_at.present? && ended_at.present?
        [started_at.year, ended_at.year, started_at_was&.year, ended_at_was&.year].compact.each do |year|
          @leave_split_up[year] = 0
        end
      end
    end
  end

  # Add 'used days' with the default value(0)
  def calculate_total_days_used
    if leave_type == 'sick'
      # Check if the started_at and ended_at falls in same fiscal year.
      if @started_at_beginning_date.year == @ended_at_beginning_date.year
        @leave_split_up[@started_at_beginning_date.year..@started_at_ending_date.year] += days
      else
        days_fall_in_first_yr, days_fall_in_next_yr = split_used_leaves_by_years(started_at, ended_at, days, 'fiscal')
        @leave_split_up[@started_at_beginning_date.year..@started_at_ending_date.year] += days_fall_in_first_yr
        @leave_split_up[@ended_at_beginning_date.year..@ended_at_ending_date.year] += days_fall_in_next_yr
      end
    elsif started_at.present? && ended_at.present?
      if started_at.year == ended_at.year
        @leave_split_up[started_at.year] += days
      else
        days_fall_in_first_yr, days_fall_in_next_yr = split_used_leaves_by_years(started_at, ended_at, days, 'calender')
        @leave_split_up[started_at.year] += days_fall_in_first_yr
        @leave_split_up[ended_at.year] += days_fall_in_next_yr
      end
    end
  end

  def recalculate_days_used_with_old_values
    old_started_at = previous_changes['started_at']&.first.presence || started_at
    old_ended_at = previous_changes['ended_at']&.first || ended_at
    old_days = previous_changes['days']&.first || days
    if leave_type == 'sick'
      started_at_beginning_date_was, started_at_ending_date_was = AnalyticsConfiguration.fiscal_year_range(old_started_at)
      ended_at_beginning_date_was, ended_at_ending_date_was = AnalyticsConfiguration.fiscal_year_range(old_ended_at)
      if started_at_beginning_date_was.year == ended_at_beginning_date_was.year
        @leave_split_up[started_at_beginning_date_was.year..started_at_ending_date_was.year] -= old_days
      else
        days_fall_in_first_yr, days_fall_in_next_yr = split_used_leaves_by_years(old_started_at, old_ended_at, old_days, 'fiscal')
        @leave_split_up[started_at_beginning_date_was.year..started_at_ending_date_was.year] -= days_fall_in_first_yr
        @leave_split_up[ended_at_beginning_date_was.year..ended_at_ending_date_was.year] -= days_fall_in_next_yr
      end
    else
      if old_started_at.year == old_ended_at.year
        @leave_split_up[old_started_at.year] -= old_days if @leave_split_up[old_started_at.year].present?
      else
        days_fall_in_first_yr, days_fall_in_next_yr = split_used_leaves_by_years(old_started_at, old_ended_at, old_days, 'calender')
        @leave_split_up[old_started_at.year] -= days_fall_in_first_yr
        @leave_split_up[old_ended_at.year] -= days_fall_in_next_yr
      end
    end
  end

  # A fiscal year can cover two calendar years, as it can start at any time during a calendar year and end 12 months
  # later in the following calendar year. So we are saving the leave used in range format.
  # Eg: { 2019..2020 => 0, 2020..2021 => 0}
  def set_default_for_fiscal_year
    @started_at_beginning_date, @started_at_ending_date = AnalyticsConfiguration.fiscal_year_range(started_at)
    @ended_at_beginning_date, @ended_at_ending_date = AnalyticsConfiguration.fiscal_year_range(ended_at)
    years_range = [@started_at_beginning_date.year..@started_at_ending_date.year,
                   @ended_at_beginning_date.year..@ended_at_ending_date.year]
    if started_at_was.present?
      start_at_beginning_date_was, started_at_ending_date_was = AnalyticsConfiguration.fiscal_year_range(started_at_was)
      years_range << [start_at_beginning_date_was.year..started_at_ending_date_was.year]
    end
    if ended_at_was.present?
      end_at_beginning_date_was, end_at_ending_date_was = AnalyticsConfiguration.fiscal_year_range(ended_at_was)
      years_range << [end_at_beginning_date_was.year..end_at_ending_date_was.year]
    end
    years_range.flatten.each do |year_range|
      @leave_split_up[year_range] = 0
    end
  end

  # Update 'used leaves' in analytics configurations with calculated leaves used.
  def update_analytic_configurations
    if leave_type == 'sick'
      @leave_split_up.each do |year, used_leaves|
        next if used_leaves.zero?

        analytics_configuration = analytics_config(year.step(2).to_a.first)
        if analytics_configuration.present?
          analytics_configuration.update(days_used: analytics_configuration.days_used + used_leaves)
        else
          create_analytic_config(started_at.change(year: year.step(2).to_a.first, day: 1, month: 5),
                                 started_at.change(year: year.step(2).to_a.first + 1, day: 30, month: 4), used_leaves)
        end
      end
    else
      @leave_split_up.each do |year, used_leaves|
        next if used_leaves.zero?

        analytics_configuration = analytics_config(year)
        if analytics_configuration.present?
          analytics_configuration.update(days_used: analytics_configuration.days_used + used_leaves)
        else
          create_analytic_config(started_at.change(year: year).beginning_of_year, started_at.change(year: year).end_of_year, used_leaves)
        end
      end
    end
  end

  def split_used_leaves_by_years(start_date, _end_date, used_days, year_type)
    days_fall_in_first_year = if year_type == 'fiscal'
                                (start_date..Date.parse(start_date.strftime('30-04-%Y')))
                              elsif year_type == 'calender'
                                (start_date..start_date.end_of_year)
                              end.count { |date| (1..5).include?(date.wday) }

    # days_fall_in_next_year = (Date.parse(end_date.strftime('01-05-%Y'))..end_date).count { |date| (1..5).include?(date.wday) }
    if days_fall_in_first_year >= used_days
      [used_days, 0]
    else
      [days_fall_in_first_year, used_days - days_fall_in_first_year]
    end
  end

  def analytics_config(year)
    AnalyticsConfiguration.kept.where('extract(year from duration_from) = ? AND analytics_type = ? AND employee_id = ?',
                                      year, leave_type, employee_id).first
  end

  def create_analytic_config(duration_from, duration_to, used_leaves)
    is_current_or_future_year = duration_from.year >= Date.today.year || duration_to.year >= Date.today.year
    days_earned = is_current_or_future_year ? Leave::EARNED_LEAVES[leave_type] : 0
    AnalyticsConfiguration.create(employee_id: employee_id, days_earned: days_earned, days_used: used_leaves, notes: notes,
                                  analytics_type: leave_type, duration_from: duration_from, duration_to: duration_to)
  end

  def employee_user
    analytics_of_employee_user = nil
    analytics_of_employee_user = current_account.saas_json.dig('schema', 'employees', 'analytics_of_employee_user') if current_account.present?

    analytics_of_employee_user
  end
end
