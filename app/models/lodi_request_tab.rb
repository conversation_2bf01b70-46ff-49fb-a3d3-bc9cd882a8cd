class LodiRequestTab < ApplicationRecord

  # ============================= Associations =========================================================================
  enum status: ["Granted", "Denied", "Pending", "Settled", "Moved to Arbitration", "Moved to 207c Hearing", "Other"]
  enum request_type: %w[medscope hearing arbitration]
  enum reason: ["Causal Connection", "Recurrence/Aggravation", "Police Surgeon Dispute", "Paragraph-13 Disputes", "Did not occur in the Performance of Duties"]

  # ============================= Associations =========================================================================
  belongs_to :lodi
  has_many_attached :files

  # ============================= Validations =========================================================================
  validates :request_type, uniqueness: { conditions: -> { where(discarded_at: nil) }, scope: [:lodi_id],
                                 message: 'Same request already exists' }
end
