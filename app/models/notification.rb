# frozen_string_literal: true

class Notification < ApplicationRecord
  enum email_to: { work: 0, personal: 1, both: 2 }
  enum sms_to: %i[work personal home all], _prefix: true
  enum status: %i[pending scheduled completed]
  MMS_ACCEPTABLE_CONTENT_TYPES = %w[image/png image/jpg image/jpeg image/gif].freeze
  DOMAINS_WITH_SCHEDULED_NOTIFICATIONS = Account.where("saas_json -> 'schema' -> 'notifications' IS NOT NULL").pluck(:subdomain).freeze

  # ============================= Associations =========================================================================
  has_many :notification_trackers
  has_many_attached :files
  has_many_attached :sms_attachments
  belongs_to :user, optional: true
  has_many :email_preview_file_uploads

  # ============================= Callbacks ============================================================================
  before_validation :set_mms_flag, if: -> { sms }
  before_save :validate_filters

  # ============================= Validations ==========================================================================
  validates_presence_of :subject, :email_message, :email_to, if: -> { email }
  validate :validate_sms_message_links, if: -> { sms }
  validates :push_message, presence: true, if: -> { push }
  validates :sms_attachments, blob: { content_type: MMS_ACCEPTABLE_CONTENT_TYPES, size: { less_than: 600.kilobytes } }
  validate :sms_attachments_length, :sms_attachments_overall_size, if: -> { sms }
  validates :files, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }
  validates :scheduled_date, :scheduled_time, presence: true, if: :is_scheduled?
  validate :scheduled_datetime_must_be_in_future, if: :is_scheduled?

  # ============================= Scopes ===============================================================================
  scope :filter_by_date, lambda { |from_date, to_date = nil|
    if from_date.present? && to_date.present?
      where('DATE(created_at) >= ? AND DATE(created_at) <= ?', from_date, to_date)
    elsif from_date.present?
      where('DATE(created_at) >= ?', from_date)
    elsif to_date.present?
      where('DATE(created_at) <= ?', to_date)
    end
  }
  scope :this_year, -> { where(created_at: Time.now.beginning_of_year..Time.now.end_of_year) }

  # ============================= Methods ==============================================================================
  def sms_attachments_length
    return if sms_attachments.length <= 10

    errors.add('sms_attachments', '- Only 10 attachments are supported')
  end

  def validate_sms_message_links
    return if sms_message.blank?

    url_pattern = %r{https?://[^\s]+}

    sms_message.scan(url_pattern).each do |url|
      if url.length > 200
        errors.add('Please shorten this link: ' + url)
      end
    end
  end

  def sms_attachments_overall_size
    total_file_size = 0
    sms_attachments.each do |attachment|
      total_file_size += attachment.blob.byte_size
    end
    return if total_file_size < 5_242_880

    errors.add('sms_attachments', '- Maximum of 5 MB can be attached')
  end

  def set_mms_flag
    return if sms_attachments.length.zero?

    self.is_mms = true
  end

  def validate_filters
    if self.filters.values.reject(&:blank?).size.zero?
      errors.add(:filters, ' - We could not generate notifications without filters. Please apply atleast one filter.')
      throw :abort
    end
  end

  def self.utc_time(date, time)
    ActiveSupport::TimeZone['America/New_York'].parse("#{date} #{time.strftime('%H:%M:%S')}").utc.to_datetime
  end

  def scheduled_datetime_must_be_in_future
    scheduled_datetime = Notification.utc_time(scheduled_date, scheduled_time)

    if scheduled_datetime <= Time.current
      errors.add("Scheduled Date and Time", "Must be in Future")
    end
  end
end
