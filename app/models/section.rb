# frozen_string_literal: true

class Section < ApplicationRecord
  include SearchByName
  extend FriendlyId

  friendly_id :name, use: :slugged

  # ============================= Associations =========================================================================
  belongs_to :department, optional: true
  has_many :titles, -> { where(titles: { discarded_at: nil }) }
  has_many :employee_sections, -> { where(employee_sections: { discarded_at: nil }) }
  has_many :employee_titles, -> { where(employee_titles: { discarded_at: nil }) }, through: :titles

  # ============================= Validations ==========================================================================
  validates :name, uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false, scope: :department_id },
                   length: { maximum: 100 }, if: -> { name_changed? || (department_id_changed? && name.present?) }

  # ============================= Callbacks ============================================================================
  after_discard do
    employee_titles.update_all(discarded_at: Time.current)
    employee_sections.update_all(discarded_at: Time.current)
    titles.update_all(discarded_at: Time.current)
  end
end
