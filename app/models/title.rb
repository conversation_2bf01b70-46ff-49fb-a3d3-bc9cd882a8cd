# frozen_string_literal: true

class Title < ApplicationRecord
  include SearchByName
  extend FriendlyId

  friendly_id :name, use: :slugged

  # ============================= Associations =========================================================================
  belongs_to :department
  belongs_to :section
  has_many :employee_titles, -> { where(employee_titles: { discarded_at: nil }) }

  # ============================= Validations ==========================================================================
  validates :name, uniqueness: { conditions: -> { where(discarded_at: nil) }, case_sensitive: false, scope: :section_id },
                   length: { maximum: 100 }, if: -> { name_changed? || (section_id_changed? && name.present?) }
  validates_uniqueness_of :title_code, conditions: -> { where(discarded_at: nil) }, allow_blank: true

  # ============================= Callbacks ============================================================================
  after_discard do
    employee_titles.update_all(discarded_at: Time.current)
    employee_title_code = Employee.kept.find_by(title_code: title_code)
    employee_title_code.update(title_code: nil) if employee_title_code.present?
  end
end
