# frozen_string_literal: true

class Totality < ApplicationRecord
  # ============================= Associations =========================================================================
  belongs_to :employee

  # ============================= Validations ==========================================================================
  validates :employee, presence: true
  validates :totalable_type, presence: true
  validates :value, numericality: { greater_than_or_equal_to: 0 }
end
