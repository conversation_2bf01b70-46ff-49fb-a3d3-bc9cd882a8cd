# frozen_string_literal: true

class Upload < ApplicationRecord
  # ============================= Associations =========================================================================
  belongs_to :employee
  has_one_attached :file

  # ============================= Validations =========================================================================
  validates :file, attached: true, blob: { content_type: ACCEPTABLE_CONTENT_TYPES, size_range: 1..100.megabytes }
end
