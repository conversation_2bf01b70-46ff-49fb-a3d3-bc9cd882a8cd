# frozen_string_literal: true

class User < ApplicationRecord
  include PgSearch::Model
  include DeviseValidatorOverride

  attr_accessor :restrict_login

  OTP_LOCK_DURATION = 30.minutes
  MAX_OTP_ATTEMPTS = 5

  # Include default devise modules. Others available are:
  # :confirmable, :lockable and :omniauthable
  devise :database_authenticatable, :recoverable, :rememberable, :timeoutable, :trackable

  delegate(
    :name,
    :cellphone,
    :home_phone,
    :work_phone,
    :profile_image,
    to: :user_decorator
  )

  pg_search_scope :search_by_user,
                  against: %i[first_name last_name email],
                  associated_against: { role: :name },
                  using: { tsearch: { prefix: true, normalization: 2  }, trigram: { word_similarity: true, threshold: 0.3 } },
                  ranked_by: ":trigram + :tsearch"

  # ============================= Enums =========================================================================
  enum restrict_type: { allow_login_in_weekends: 0, login_only_in_weekdays: 1 }

  # ============================= Associations =========================================================================
  belongs_to :role, optional: true
  has_many :user_contacts, -> { where(user_contacts: { discarded_at: nil }) }, inverse_of: :user
  # Ref: https://api.rubyonrails.org/classes/ActiveRecord/Associations/CollectionProxy.html#method-i-delete_all
  has_many :blacklisted_tokens, dependent: :delete_all
  has_many :poly_notes, as: :notable
  has_one_attached :avatar
  has_many :user_audits, foreign_key: "whodunnit"
  has_many :notifications
  has_many :created_reminders, class_name: "Reminder", dependent: :destroy, foreign_key: "user_id"
  has_and_belongs_to_many :reminders, join_table: :reminders_users
  has_many :reminder_trackers, through: :reminders
  accepts_nested_attributes_for :user_contacts

  # ============================= Validations ==========================================================================
  validates_uniqueness_of :username, conditions: -> { where(discarded_at: nil) },
                                     if: -> { username_changed? }
  validates :password_confirmation, presence: true, if: -> { encrypted_password_changed? }
  validates :role, presence: true
  validates :phone, presence: true, if: -> { current_account.saas_json.dig('schema', 'users', 'enable_two_factor_authentication') == true }
  validates_format_of :phone, with: /(\([0-9]{3}\)\s+[0-9]{3}\s+-\s+[0-9]{4})\z/, message: "Phone number format is invalid", if: -> { phone.present? }
  validates :avatar, blob: { content_type: /\Aimage\/.*\z/ }
  validate :validate_restrict_login

  # ============================= Callbacks ============================================================================
  after_discard do
    user_contacts.update_all(discarded_at: Time.current)
    poly_notes.update_all(discarded_at: Time.current)
    blacklisted_tokens.delete_all
  end
  before_save :update_issued_at_time, if: :encrypted_password_changed?
  before_save :update_restrict_login
  before_update :require_2fa_on_next_login, if: -> { current_account.saas_json.dig('schema', 'users', 'enable_two_factor_authentication') == true && phone_changed? }

  # ============================= Instance & class methods =============================================================
  def active_for_authentication?
    super && !discarded?
  end

  def send_reset_password_instructions
    token = set_reset_password_token
    if current_account.saas_json.dig('schema', 'employees', 'sendgrid_templates') == true
      SendgridService.new.send_single_email(
        recipient_email: email, template_id: ENV['USER_FORGOT_PASSWORD_TEMPLATE_ID'], current_tenant: Apartment::Tenant.current, subject: 'Reset password instructions',
        dynamic_contents: { header_text_1: email, body_button_url_1: "#{ENV['ACTIVE_DOMAIN_PROTOCOL']}://#{Apartment::Tenant.current}.#{ENV['REACT_HOST']}/reset-password?reset_password_token=#{token}", }
      )
    else
        ResetPasswordInstructionsMailer.reset_password_instructions(self, token).deliver_later
    end
  end

  def update_issued_at_time
    self.password_changed_at = Time.now.utc
  end

  private

  def user_decorator
    @user_decorator ||= UserDecorator.new(self)
  end

  def validate_restrict_login
    if restrict_login_out_of_office == true && (restrict_type.blank? || allow_login_from_time.blank? || allow_login_to_time.blank?)
      errors.add(:restrict_type, "Should be selected") if restrict_type.blank?
      errors.add(:allow_login_from_time, "Should be filled") if allow_login_from_time.blank?
      errors.add(:allow_login_to_time, "Should be filled") if allow_login_to_time.blank?
    end
  end

  def update_restrict_login
    if restrict_login_out_of_office == false && restrict_login == true
      self.restrict_type = nil
      self.allow_login_from_time = nil
      self.allow_login_to_time = nil
    end
  end

  def require_2fa_on_next_login
    update_columns(next_2fa_in: Time.current)
  end
end
