# frozen_string_literal: true

class UserAudit < ApplicationRecord
  self.table_name = 'versions'

  # ====================================================== Associations =================================================
  belongs_to :user, foreign_key: 'whodunnit', optional: true
  belongs_to :item, polymorphic: true
  belongs_to :employee, optional: true

  # ====================================================== Scope ========================================================
  default_scope -> { order(id: :desc) }

  # ============================= Methods ===============================================================================
  def self.models_without_rights
    %w[MailingAddress LodiRequestTab]
  end
end
