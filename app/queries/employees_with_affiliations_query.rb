# frozen_string_literal: true

class EmployeesWithAffiliationsQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    affiliation_ids = params[:affiliation_ids]
    @relation = relation
    @affiliation_ids = if affiliation_ids.present? && affiliation_ids.any?('0')
                         Affiliation.kept.ids
                       else
                         affiliation_ids
                       end
    @report_columns = params[:columns] || []
  end

  def call
    return relation if affiliation_ids_blank?

    relation.includes!(:affiliation) if report_columns.include?('affiliation')
    relation.where!(affiliation_id: affiliation_ids)
    relation
  end

  private

  attr_reader :relation, :affiliation_ids, :report_columns

  def affiliation_ids_blank?
    (affiliation_ids.is_a?(Array) && (affiliation_ids.length == 1) && affiliation_ids.first.blank?) || affiliation_ids.blank?
  end
end
