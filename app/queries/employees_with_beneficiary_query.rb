# frozen_string_literal: true

class EmployeesWithBeneficiaryQuery
  class << self
    delegate(
      :call,

      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(:beneficiaries)

    relation.where!(beneficiaries: { employee_id: employee_ids }).references!(:beneficiaries) unless employee_ids_blank?

    relation.distinct
  end

  private

  attr_reader(
    :relation,
    :params
  )

  def employee_ids
    params[:employee_ids]
  end
  def employee_ids_blank?
    (employee_ids.is_a?(Array) && (employee_ids.length == 1) && employee_ids.first.blank?) ||
      employee_ids.blank?
  end
end
