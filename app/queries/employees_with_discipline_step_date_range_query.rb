# frozen_string_literal: true

class EmployeesWithDisciplineStepDateRangeQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(employee_discipline_settings: [:discipline_setting, { employee_discipline_steps: :discipline_status }])
    relation.where!(employee_discipline_settings: { employee_discipline_steps:
                                                      { discipline_status_id: :discipline_status_ids } }) unless discipline_status_ids_blank?

    relation.where!(employee_discipline_steps: {is_settled: is_settled, step: settled_steps}) if is_settled.present? && settled_steps.present?
    relation.where!(employee_discipline_steps: {is_pending: is_pending, step: pending_steps}) if is_pending.present? && pending_steps.present?

    # Step 1 related queries
    relation.where!('employee_discipline_steps.date >= ? && employee_discipline_steps.step == ?', step_1_from_date, 1)
            .references!(:employee_discipline_steps) if step_1_from_date.present?
    relation.where!('employee_discipline_steps.date <= ? && employee_discipline_steps.step == ?', step_1_to_date, 1)
            .references!(:employee_discipline_steps) if step_1_to_date.present?

    # Step 2 related queries
    relation.where!('employee_discipline_steps.date >= ? && employee_discipline_steps.step == ?', step_2_from_date, 2)
            .references!(:employee_discipline_steps) if step_2_from_date.present?
    relation.where!('employee_discipline_steps.date <= ? && employee_discipline_steps.step == ?', step_2_to_date,2)
            .references!(:employee_discipline_steps) if step_2_to_date.present?

    # Step 3 related queries
    relation.where!('employee_discipline_steps.date >= ? && employee_discipline_steps.step = ?', step_3_from_date,3)
            .references!(:employee_discipline_steps) if step_3_from_date.present?
    relation.where!('employee_discipline_steps.date <= ? && employee_discipline_steps.step = ?', step_3_to_date,3)
            .references!(:employee_discipline_steps) if step_3_to_date.present?

    # Arbritration related queries
    relation.where!('employee_discipline_steps.date >= ? && employee_discipline_steps.step = ?', arbritration_from_date,4)
            .references!(:employee_discipline_steps) if arbritration_from_date.present?
    relation.where!('employee_discipline_steps.date <= ? && employee_discipline_steps.step = ?', arbritration_to_date,4)
            .references!(:employee_discipline_steps) if arbritration_to_date.present?

    relation
  end

  private

  attr_reader :relation, :params

  def discipline_status_ids
    if params[:discipline_status_ids].present? && params[:discipline_status_ids].any?('0')
      DisciplineStatus.kept.ids
    else
      params[:discipline_status_ids]
    end
  end

  def is_settled
    params[:settled] == 'true'
  end

  def is_pending
    params[:pending] == 'true'
  end

  def settled_steps
    params[:settled_steps]
  end

  def pending_steps
    params[:pending_steps]
  end

  def step_1_from_date
    params[:step_1_from_date]
  end

  def step_1_to_date
    params[:step_1_to_date]
  end

  def step_2_from_date
    params[:step_2_from_date]
  end

  def step_2_to_date
    params[:step_2_to_date]
  end

  def step_3_from_date
    params[:step_3_from_date]
  end

  def step_3_to_date
    params[:step_3_to_date]
  end

  def arbritration_from_date
    params[:arbritration_from_date]
  end

  def arbritration_to_date
    params[:arbritration_to_date]
  end

  def discipline_status_ids_blank?
    (discipline_status_ids.is_a?(Array) && (discipline_status_ids.length == 1) && discipline_status_ids.first.blank?) || discipline_status_ids.blank?
  end
end
