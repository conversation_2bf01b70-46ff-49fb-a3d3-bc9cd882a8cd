# frozen_string_literal: true

class EmployeesWithGrievanceWinLossQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call # rubocop:disable Metrics/AbcSize
    relation.includes!(employee_grievances: :employee_grievance_steps)
    relation.where!(employee_grievances: { employee_grievance_steps: { step: "arbritration", win: true } }) if win.present?
    relation.where!(employee_grievances: { employee_grievance_steps: { step: "arbritration", loss: true } }) if loss.present?

    relation
  end

  private

  attr_reader :relation, :params

  def win
    params[:win] == "true"
  end

  def loss
    params[:loss] == "true"
  end
end
