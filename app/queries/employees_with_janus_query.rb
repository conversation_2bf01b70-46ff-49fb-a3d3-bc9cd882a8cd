# frozen_string_literal: true

class EmployeesWithJanusQuery
  class << self
    delegate(
      :call,
      to: :new
    )
  end

  def initialize(relation = Employee.kept, params = {})
    @relation = relation
    @params = params
  end

  def call
    relation.where!(janus_card: janus_card)
    relation.where!('employees.janus_card_opt_out_date >= ?', janus_from) if janus_from.present?
    relation.where!('employees.janus_card_opt_out_date <= ?', janus_to) if janus_to.present?
    relation
  end

  private

  attr_reader(
    :relation,
    :params
  )

  def janus_card
    params[:janus_opt_out]
  end

  def janus_from
    params[:opt_out_start_date]
  end

  def janus_to
    params[:opt_out_end_date]
  end
end
