# frozen_string_literal: true

class EmployeesWithPositionsQuery
  class << self
    delegate :call, to: :new
  end

  def initialize(relation = Employee.kept, position_ids = nil)
    @relation = relation
    @position_ids = if position_ids.present? && position_ids.any?('0')
                      Position.kept.ids
                    else
                      position_ids
                    end
  end

  def call
    if (position_ids.is_a?(Array) && (position_ids.length == 1) && position_ids.first.blank?) || position_ids.blank?
      return relation
    end

    relation.includes!(:employee_positions).where!(employee_positions: { position_id: position_ids })
            .where!('employee_positions.end_date is NULL OR employee_positions.end_date >= ?', Date.today)
  end

  private

  attr_reader :relation, :position_ids
end
