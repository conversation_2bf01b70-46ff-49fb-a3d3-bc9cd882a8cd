# frozen_string_literal: true

module Api
  class BenefitDisbursementSerializer < NotesSerializer
    attributes :amount, :date, :employee_benefit_id, :id, :notes, :payment_type_id,
               :reference_number, :year, :relationship, :benefit_coverage_id, :is_member, :entry_date

    attribute :file do |object|
      FileBuilder.process_file(object)
    end

    attribute :benefit_name do |object|
      employee_benefit = object.employee_benefit

      employee_benefit.name if employee_benefit.present?
    end

    attribute :payment_type_name do |object|
      payment_type = object.payment_type

      payment_type.name if payment_type.present?
    end

    attribute :benefit_coverage_name do |object|
      coverage_name = nil
      if object.benefit_coverage_id.blank? && object.is_member == true
        coverage_name = "Member"
      elsif object.benefit_coverage.present? && object.benefit_coverage.first_name.present? && object.benefit_coverage.last_name.present?
        benefit_coverage = object.benefit_coverage
        coverage_name = [benefit_coverage.first_name, benefit_coverage.suffix, benefit_coverage.last_name].join(" ")
      else
        benefit_coverage = object.benefit_coverage
        coverage_name = benefit_coverage.name if benefit_coverage.present?
      end

      coverage_name
    end
  end
end
