# frozen_string_literal: true

module Api
  class EmployeeDepartmentSerializer < ApplicationSerializer
    attributes :end_date, :id, :department_id, :start_date

    attribute :department_name do |object|
      department = object.department

      department.name if department.present?
    end

    attribute :department_address do |object|
      department = object.department

      department.address if department.present?
    end
  end
end
