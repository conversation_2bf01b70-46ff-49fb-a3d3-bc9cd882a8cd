# frozen_string_literal: true

module Api
  class HearingSerializer < NotesSerializer
    attributes :hearing_date, :notes, :description, :brief_date, :brief, :employee_grievance_step_id, :id

    attribute :transcripts do |object|
      FileBuilder.process_multi_files(object, 'transcripts', true)
    end

    attribute :evidences do |object|
      FileBuilder.process_multi_files(object, 'evidences', true)
    end

    attribute :briefs do |object|
      FileBuilder.process_multi_files(object, 'briefs', true)
    end
  end
end
