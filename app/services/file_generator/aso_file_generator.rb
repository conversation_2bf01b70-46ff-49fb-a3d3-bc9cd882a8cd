# frozen_string_literal: true

module FileGenerator
  class AsoFileGenerator
    def generate(account, benefit)
      Apartment::Tenant.switch!(account)
      condition = "employee_benefits.end_date is NULL or employee_benefits.end_date > '#{Date.today}'"

      dental_benefit_id = Benefit.kept.where('lower(name) = ?', benefit.downcase).first.id
      members = Employee.kept.includes(employee_employment_statuses: :employment_status, employee_benefits: [:benefit_coverages])
                        .order(:previous_shield_number).where(employee_benefits: { benefit_id: dental_benefit_id }).send(:where, condition)

      filename = "ASO_#{Time.now.strftime('%Y%m%d%H%M')}.xls"
      filepath ||= "#{Rails.root}/#{filename}"

      File.open(filepath, 'wb') do |file|
        @current_account = Account.find_by(subdomain: Apartment::Tenant.current)
        ReportGenerator::Excel::AsoExcel.create(members, filepath, @current_account, account, benefit)
      end

      filename # Returning Filename to upload it to FTP server
    end
  end
end
