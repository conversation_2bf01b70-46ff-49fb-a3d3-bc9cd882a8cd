module FileGenerator
  class GvsFileGenerator
    def generate(account, benefit)
      Apartment::Tenant.switch!(account)
      current_account = Apartment::Tenant.current
      condition = "employee_benefits.end_date is NULL or employee_benefits.end_date > '#{Date.today}'"

      optical_benefit = Benefit.where(name: benefit).first
      members = Employee.kept.includes(:gender, :contacts, employee_employment_statuses: :employment_status, employee_officer_statuses: :officer_status, employee_benefits: [:benefit_coverages])
                    .order(:a_number).where(employee_benefits: {benefit_id: optical_benefit.id}).send(:where, condition)


      filename = "GVS_#{Time.now.strftime('%Y%m%d%H%M')}.xls"
      filepath ||= "#{Rails.root}/#{filename}"

      File.open(filepath, 'wb') do |file|
        @current_account = Account.find_by(subdomain: Apartment::Tenant.current)
        ReportGenerator::Excel::GvsExcel.create(members, filepath, @current_account, account, benefit)
      end

      filename # Returning Filename to upload it to FTP server
    end
  end
end
