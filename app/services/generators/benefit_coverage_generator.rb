# frozen_string_literal: true

module Generators
  class BenefitCoverageGenerator < BenefitBaseGenerator
    def generate
      return unless employee_exists?
      return unless benefit_exists?
      return unless employee_benefit_exists?

      data.name = params['name']
      data.relationship = params['relationship']
      data.social_security_number = params['social_security_number']
      data.address = params['address']
      data.birthday = format_import_date(params['date_of_birth'])
      data.expires_at = format_import_date(params['end_date'])
      data.employee_benefit = employee_benefit
      data.employee = employee
      save_data
    end

    private

    def data
      @data ||= BenefitCoverage.new
    end
  end
end
