# frozen_string_literal: true

class ImageUrlService
  require 'aws-sdk-s3'

  def initialize
    @s3_client = Aws::S3::Resource.new(
      region: ENV['AWS_S3_REGION'],
      access_key_id: ENV['AWS_ACCESS_KEY_ID'],
      secret_access_key: ENV['AWS_SECRET_ACCESS_KEY']
    )
    @bucket = @s3_client.bucket(ENV['AWS_S3_EMAIL_PREVIEW_BUCKET'])
  end


  def self.delete_object(url)
    s3_client = Aws::S3::Client.new(
      region: ENV['AWS_S3_REGION'],
      access_key_id: ENV['AWS_ACCESS_KEY_ID'],
      secret_access_key: ENV['AWS_SECRET_ACCESS_KEY']
    )
    bucket = ENV['AWS_S3_EMAIL_PREVIEW_BUCKET']
    object = url.split('amazonaws.com/')
    s3_client.delete_object(bucket: bucket, key: object.last)
  end

  def generate_url(email_content)
    content = email_content.clone

    # Find all keys that might contain image files
    image_keys = email_content.keys.select { |key| key.to_s.include?('image') }

    # Process each potential image key
    image_keys.each do |key|
      next unless email_content[key].present? && file?(email_content[key])

      url = upload_to_s3(email_content[key])
      content[key] = url
    end

    content
  end

  def generate_url_with_file(file)
    upload_to_s3(file) if file?(file)
  end

  private

  def file?(object)
    object.respond_to?(:tempfile) ||
      object.is_a?(ActionDispatch::Http::UploadedFile) ||
      object.is_a?(Rack::Test::UploadedFile)
  end

  def upload_to_s3(file)
    filename = "#{Time.now.to_i}-#{SecureRandom.uuid}-#{file.original_filename}"

    object_key = "email_preview_file_upload/#{Apartment::Tenant.current}/#{filename}"
    obj = @bucket.object(object_key)

    obj.upload_file(
      file.tempfile.path,
      content_type: file.content_type,
      acl: 'public-read'
    )

    obj.public_url
  end
end
