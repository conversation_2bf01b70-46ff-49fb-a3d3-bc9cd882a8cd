# frozen_string_literal: true

module Importers
  class BenefitDisbursementsImporter < ApplicationImporter
    private

    def columns
      %w[
        officer_id
        shield_num
        first_name
        middle_initial
        last_name
        benefit_name
        payment_type_id
        reference_number
        amount
        date
        year
        notes
        errors
      ]
    end

    def errors_filename
      'benefit_disbursements_errors'
    end

    def process_rows
      csv_file.each do |row|
        bar.inc

        generator = Generators::BenefitDisbursementGenerator.new(row)
        generator.generate

        if generator.errors.present?
          row['errors'] = generator.errors
          errors << row
        end
      end
    end
  end
end
