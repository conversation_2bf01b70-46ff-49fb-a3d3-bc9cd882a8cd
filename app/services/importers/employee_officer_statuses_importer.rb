# frozen_string_literal: true

module Importers
  class EmployeeOfficerStatusesImporter < ApplicationImporter
    private

    def columns
      %w[
        officer_id
        shield_num
        first_name
        middle_initial
        last_name
        status
        start_date
        end_date
        errors
      ]
    end

    def errors_filename
      'employee_officer_statuses_errors'
    end

    def process_rows
      csv_file.each do |row|
        bar.inc

        generator = Generators::EmployeeOfficerStatusGenerator.new(row)
        generator.generate

        if generator.errors.present?
          row['errors'] = generator.errors
          errors << row
        end
      end
    end
  end
end
