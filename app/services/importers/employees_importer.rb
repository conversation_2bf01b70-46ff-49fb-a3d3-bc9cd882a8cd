# frozen_string_literal: true

module Importers
  class EmployeesImporter < ApplicationImporter
    private

    def columns
      %w[
        officer_id
        shield_num
        first_name
        middle_initial
        last_name
        birth_date
        gender
        social_security_number
        marital_status
        veteran_status
        state
        city
        zip_code
        address
        apartment_no
        notes
        errors
      ]
    end

    def errors_filename
      'employees_errors'
    end

    def process_rows
      csv_file.each do |row|
        bar.inc

        old_employee = Employee.where(id: row['officer_id'].to_i).first

        next if old_employee.present?

        generator = Generators::EmployeeGenerator.new(row)
        generator.generate

        if generator.errors.present?
          row['errors'] = generator.errors
          errors << row
        end
      end
    end
  end
end
