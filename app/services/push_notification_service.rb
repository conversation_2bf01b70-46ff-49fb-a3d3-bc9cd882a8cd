class PushNotificationService
  include Api::ResponseFormatter

  def initialize
    @sns = Aws::SNS::Client.new(
      region: ENV['AWS_SNS_REGION'],
      access_key_id: ENV['AWS_ACCESS_KEY_ID'],
      secret_access_key: <PERSON>NV['AWS_SECRET_ACCESS_KEY']
    )
    @account = Account.find_by(subdomain: Apartment::Tenant.current)
  end

  def publish(device, content, app_notification)
    message = if device.os_type == 'ios'
                construct_ios_payload(content, app_notification,)
              elsif device.os_type == 'android'
                construct_android_payload(content, app_notification,)
              end
    @sns.publish(
      target_arn: device.endpoint_arn,
      message: {
        default: message,
        APNS: message,
        APNS_SANDBOX: message,
        GCM: message
      }.to_json,
      message_structure: 'json'
    )
  end

  # Creates endpoint amazon resource name(arn) based on platform application(ios or android)
  def create_endpoint_arn(device_token, employee, application_arn)
    @sns.create_platform_endpoint(
      platform_application_arn: application_arn,
      token: device_token,
      custom_user_data: employee.username
    )
  end

  # Update the endpoint arn if the device changed
  def update_endpoint_arn(device_token, endpoint_arn, employee)
    @sns.set_endpoint_attributes(
      endpoint_arn: endpoint_arn,
      attributes: {
        Token: device_token,
        CustomUserData: employee.username
      }
    )
  end

  # Enable the endpoint arn if disabled
  def enable_endpoint_arn(endpoint_arn, employee)
    @sns.set_endpoint_attributes(
      endpoint_arn: endpoint_arn,
      attributes: {
        'Enabled' => 'true',
        'CustomUserData' => "#{employee.username}"
      }
    )
    Employee.find(employee.id).device.enabled!
  end

  def delete_endpoint_arn(endpoint_arn)
    @sns.delete_endpoint(
      endpoint_arn: endpoint_arn
    )
  end

  def construct_ios_payload(content, app_notification)
    {
      'aps': {
        'alert': {
          'title': "Notification from #{@account.name}",
          'body': content
        },
        'mutable-content': 1,
        'sound': 'default',
        'message': {
          'data': {
            'notification_id': app_notification
          }
        }
      }
    }.to_json
  end

  def construct_android_payload(content, app_notification)
    {
      notification: {
        title: "Notification from #{@account.name}",
        body: content
      },
      data: {
        notification_id: app_notification,
      }
    }.to_json
  end

  def send_push_notification(employee, notification, content)
    devices = employee.devices
    devices.each do |device|
      begin
        next unless device&.status == 'enabled'
        app_notification = notification.id
        publish(device, content, app_notification)
      rescue StandardError
        device.disabled!
      end
    end
    # return unless device&.status == 'enabled'
    # app_notification = notification.id
    # publish(device, content, app_notification)
    # rescue StandardError
    #   device.disabled!
  end
end
