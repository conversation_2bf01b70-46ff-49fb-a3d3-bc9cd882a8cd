# frozen_string_literal: true

module ReportGenerator
  class BeneficiaryReport < ApplicationReport
    private

    def report_type
      Report::ReportTypes::BENEFICIARY
    end

    def filename
      @filename ||= "beneficiary-report-#{report.id}-#{report_created_at}.#{report_format}"
    end

    def generate_pdf
      nil
    end
    
    def generate_xls
      return unless report_format == 'xls'

      ReportGenerator::Excel::BeneficiaryExcel.create(employees, filepath, params, current_account)
    end
  end
end
