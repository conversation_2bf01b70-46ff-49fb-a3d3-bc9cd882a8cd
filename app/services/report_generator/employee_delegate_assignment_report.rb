# frozen_string_literal: true

module ReportGenerator
  class EmployeeDelegateAssignmentReport < ApplicationReport
    private

    def report_type
      Report::ReportTypes::EMPLOYEE_DELEGATE_ASSIGNMENT
    end

    def filename
      @filename ||= "employee-delegate-assignment-report-#{report.id}-#{report_created_at}.#{report_format}"
    end

    def generate_pdf
      return unless report_format == 'pdf'

      if params[:group_by_delegate] == "true"
        ReportGenerator::Pdf::EmployeeGroupByDelegateAssignmentsPdf.create(employees, filename, filepath, current_account)
      else
        ReportGenerator::Pdf::EmployeeDelegateAssignmentsPdf.create(employees, filename, filepath, current_account)
      end
    end

    def generate_xls
      nil
    end
  end
end
