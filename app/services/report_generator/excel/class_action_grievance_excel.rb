# frozen_string_literal: true

module ReportGenerator
  module Excel
    class ClassActionGrievanceExcel < ApplicationExcel
      def self.create(object, filepath, current_account, columns = [])
        new(object, filepath, current_account, columns).create
      end

      def initialize(object, filepath, current_account, columns)
        @object = object
        @filepath = filepath
        @current_account = current_account
        @columns = columns
      end

      private

      attr_reader :object, :columns, :current_account

      def create_worksheets
        ReportGenerator::Excel::Worksheets::ClassActionGrievanceWorksheet.create(workbook, object, current_account, columns)
      end
    end
  end
end
