# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class EmployeeDisciplineSettingWorksheet < ApplicationWorksheet
        private

        def report_type
          Report::ReportTypes::DISCIPLINES
        end

        def create_rows # rubocop:disable Metrics/MethodLength
          index = 0

          object.order_by_name.each do |employee|
            # each_with_index is slower in this case
            employee.employee_discipline_settings.each.each do |employee_disciplines|
              data = [
                employee.name,
                employee_disciplines.discipline_setting_name,
                if current_account.saas_json.dig('ui', 'reports', 'disciplines', 'report_columns').present?
                  if current_account.saas_json.dig('ui', 'reports', 'disciplines', 'report_columns').include?('charge')
                    employee_disciplines.charge
                  end
                end,
                if current_account.saas_json.dig('ui', 'reports', 'disciplines', 'report_columns').present?
                  if current_account.saas_json.dig('ui', 'reports', 'disciplines', 'report_columns').include?('dan_number')
                    employee_disciplines.dan_number
                  end
                end,
                DateFormatterHelper.format_report_date(employee_disciplines.date)
              ]
              index += 1
              worksheet.insert_row(index, data.compact)
            end
          end
        end

        def format_date(date)
          date.present? ? date.strftime('%m-%d-%Y') : ''
        end
      end
    end
  end
end
