# frozen_string_literal: true

module ReportGenerator
  module Excel
    module Worksheets
      class NotificationAnalyticsWorksheet < ApplicationWorksheet
        private

        def report_type
          Report::ReportTypes::NOTIFICATION_ANALYTICS
        end

        def create_rows # rubocop:disable Metrics/MethodLength
          index = 0

          object.each do |notification_tracker|
            # each_with_index is slower in this case
            data = [
              notification_tracker&.employee&.name,
              notification_tracker.email_status,
              notification_tracker.sms_status,
              notification_tracker.sms_error_code || '-'
            ]
            index += 1
            worksheet.insert_row(index, data)
          end
        end
      end
    end
  end
end