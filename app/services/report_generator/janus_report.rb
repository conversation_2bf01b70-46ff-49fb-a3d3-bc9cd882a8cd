# frozen_string_literal: true

module ReportGenerator
  class JanusReport < ApplicationReport
    private

    def report_type
      Report::ReportTypes::JANUS
    end

    def filename
      @filename ||= "janus-report-#{report.id}-#{report_created_at}.#{report_format}"
    end

    def generate_pdf
      return unless report_format == 'pdf'

      ReportGenerator::Pdf::EmployeeJanusPdf.create(employees, filename, filepath, current_account)
    end

    def generate_xls
      return unless report_format == 'xls'

      ReportGenerator::Excel::EmployeeJanusExcel.create(employees, filepath, current_account)
    end
  end
end
