# frozen_string_literal: true

module ReportGenerator
  module Pdf
    class ClassActionGrievancePdf < ApplicationPdf
      def self.create(object, file_name ,filepath, current_account = nil, columns = nil)
        new(object, file_name, filepath, current_account, columns).create
      end

      def initialize(object, file_name, filepath, current_account, columns)
        @object = object
        @file_name = file_name
        @filepath = filepath
        @current_account = current_account
        @columns = columns
      end

      private

      attr_reader :object, :file_name, :file_path, :columns, :current_account

      def pdf # rubocop:disable Metrics/MethodLength
        ac.render_to_string(
          encoding: 'UTF-8',
          locals: {
            employee_grievances: object,
            columns: columns
          },
          assigns: {
            current_account: current_account
          },
          pdf: filename,
          template: 'templates/class_action_grievances/index_pdf',
          layout: 'pdf.html',
          padding: {
            top: 30
          },
          header: {
            html: {
              template: 'layouts/header',
              locals: {
                title: report_title_translation(Report::ReportTypes::CLASS_ACTION_GRIEVANCE)
              },
              assigns: {
                current_account: current_account
              }
            },
            spacing: 6
          },
          footer: {
            font_size: 10,
            right: '[page] of [topage]'
          }
        )
      end
    end
  end
end
