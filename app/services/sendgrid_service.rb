# frozen_string_literal: true

require 'sendgrid-ruby'

class SendgridService
  include SendGrid

  def initialize
    @sendgrid_api_key = ENV['SENDGRID_API_KEY']
    @client = SendGrid::API.new(api_key: @sendgrid_api_key).client
  end

  def send_bulk_email(notification_id: nil, recipients: [], dynamic_contents: {}, template_id: nil, sender_address: nil, current_tenant: nil, retrying_job: false)
    Apartment::Tenant.switch!(current_tenant) if current_tenant.present?

    sender_address ||= '<EMAIL>'
    notification = Notification.find(notification_id)

    attachments = []
    notification.files.each do |file|
      attachment = create_attachment(file)
      attachments.push(attachment)
    end

    recipients.each_slice(1000) do |batch|
      mail = Mail.new
      mail.from = Email.new(email: sender_address)
      mail.template_id = template_id

      batch.each do |recipient|
        personalization = Personalization.new
        personalization.add_to(Email.new(email: recipient[:email]))
        personalization.add_dynamic_template_data(
          dynamic_contents.merge(subject: notification.subject)
        )

        # Add Custom arguments
        is_pro_or_dev = ENV['APP_ENV'] == 'production' ? "production-#{recipient[:employee_id]}-#{notification_id}" : "development-#{recipient[:employee_id]}-#{notification_id}"
        personalization.add_custom_arg(CustomArg.new(key: 'employee_id', value: recipient[:employee_id]&.to_s))
        personalization.add_custom_arg(CustomArg.new(key: 'notification_id', value: notification_id.to_s))
        personalization.add_custom_arg(CustomArg.new(key: 'subdomain', value: Apartment::Tenant.current))
        personalization.add_custom_arg(CustomArg.new(key: 'message_id', value: is_pro_or_dev))

        mail.add_personalization(personalization)
      end

      # Add attachments
      attachments.each do |attachment|
        mail.add_attachment(attachment)
      end

      begin
        response = @client.mail._('send').post(request_body: mail.to_json)
        raise "SendGrid API Error: #{response.status_code} - #{response.body}" if response.status_code.to_i >= 400

      rescue => e
        message_id = response.headers['x-message-id'] || 'N/A'

        Rails.logger.error "[SendGrid][Batch API Call Failed: #{e.message}, x-message-id: #{message_id}, x-message-id #{message_id}"
        # Rails.logger.error "[SendGrid][Batch ##{index}] API Call Failed: #{e.message}, x-message-id: #{message_id}, x-message-id #{message_id}"

        # Retry later using a background job
        if !retrying_job
          SendgridNotificationRetryJob.perform_later(
            notification_id: notification_id,
            recipients: batch,
            dynamic_contents: dynamic_contents,
            template_id: template_id,
            sender_address: sender_address,
            current_tenant: Apartment::Tenant.current,
            retrying_job: true
          )
        elsif retrying_job
          return e
        end
      end

      sleep(1) unless batch.last == recipients.last
      return true if batch.last == recipients.last
    end
  end

  def send_single_email(recipient_email: nil, dynamic_contents: {}, template_id: nil, sender_address: nil, current_tenant: nil, subject: nil, attachments: nil)
    Apartment::Tenant.switch!(current_tenant) if current_tenant.present?

    sender_address = '<EMAIL>'
    mail = Mail.new
    mail.from = Email.new(email: sender_address)
    mail.template_id = template_id

    personalization = Personalization.new
    personalization.add_to(Email.new(email: recipient_email))
    personalization.add_dynamic_template_data(dynamic_contents.merge(subject: subject))
    mail.add_personalization(personalization)

    if attachments.present?
      attachments.each do |attachment|
        mail.add_attachment(create_attachment(attachment))
      end
    end

    begin
      response = @client.mail._('send').post(request_body: mail.to_json)
      p response
      raise "SendGrid API Error: #{response.status_code} - #{response.body}" if response.status_code.to_i >= 400
      return true

    rescue => e
      Rails.logger.error "[SendGrid][Single API Call Failed: #{e.message}]"
      return e
    end
  end

  def fetch_template(template_id)
    response = @client.templates._(template_id).get
    return unless response.status_code == '200'

    template_data = JSON.parse(response.body)
    template_data['versions'].find { |v| v['active'] == 1 }&.dig('html_content') ||
      template_data['versions'].first&.dig('html_content')
  end

  private

  def set_sender_address
    account = Account.find_by_subdomain(Apartment::Tenant.current)
    domain = account.domain_name || 'myfusesystems.com'

    if account.subdomain == 'nassaucoba' && ENV['APP_ENV'] == 'production'
      '<EMAIL>'
    elsif account.subdomain == 'nyspec' && ENV['APP_ENV'] == 'production'
      'pmeringolo@' + domain
    else
      'no-reply@' + domain
    end
  end

  def create_attachment(file)
    attachment = Attachment.new
    attachment.content = Base64.strict_encode64(file.blob.download)
    attachment.type = file.blob.content_type
    attachment.filename = file.blob.filename.to_s

    attachment
  end

end
