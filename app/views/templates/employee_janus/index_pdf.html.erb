<% index = 0 %>
<table class="table-regular">
  <thead>
  <tr>
    <th>&nbsp;</th>
    <% translate_report_columns(Report::ReportTypes::JANUS).each do |column| %>
      <th>
        <%= column %>
      </th>
    <% end %>
  </tr>
  </thead>
  <tbody>
  <% employees.order_by_name.each_with_index do |employee, ind| %>
    <tr>
      <% index += 1 %>
      <td>
        <%= index %>
      </td>
      <td>
        <h1>
          <%= employee.name %>
        </h1>
      </td>
      <td>
        <h1><%= employee.janus_card ? 'Yes' : 'No' %>
        </h1>
      </td>
      <td>
        <h1>
          <%= DateFormatterHelper.format_report_date(employee.janus_card_opt_out_date) %>
        </h1>
      </td>
    </tr>
  <% end %>
  </tbody>
</table>
