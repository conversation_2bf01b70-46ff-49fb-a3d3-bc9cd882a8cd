<% index = 0 %>
<table class="table-regular">
  <thead>
  <tr>
    <th>&nbsp;</th>
    <% columns = if show_dollar_amount == "true"
                   translate_report_columns(Report::ReportTypes::PACFS, ["Amount"])
                 else
                   translate_report_columns(Report::ReportTypes::PACFS)
                 end %>
    <% columns.each do |column| %>
      <th><%= column %></th>
    <% end %>
  </tr>
  </thead>
  <tbody>
  <% total_amount = 0 %>
  <% employees.order_by_name.each do |employee| %>
    <% employee.employee_pacfs.order(date: :desc).each_with_index do |pacfs, ind| %>
      <tr>
        <% if ind.zero? %>
          <% index += 1 %>
          <td>
            <%= (index) %>
          </td>
          <td>
            <h1>
              <%= (employee.name) %>
            </h1>
          </td>
        <% else %>
          <td>&nbsp;</td>
          <td>&nbsp;
          </td>
        <% end %>

        <td>
          <h1>
            <%= (pacfs.pacf.name) %>
          </h1>
        </td>

        <td>
          <h1>
            <%= (DateFormatterHelper.format_report_date(pacfs.date)) %>
          </h1>
        </td>
        <% if show_dollar_amount == "true" %>
          <td>
            <h1>
              <%= if pacfs&.amount.blank?
                    '$0.00'
                  else
                    '$' + ('%.2f' % pacfs.amount)
                  end %>

            </h1>
          </td>
          <% total_amount += pacfs.amount if pacfs&.amount.present? %>
        <% end %>

      </tr>

    <% end; end %>
  <% if show_dollar_amount == "true" %>
    <tr>
      <td colspan="3"></td>
      <td> Total Amount:</td>
      <td><%= '$' + ('%.2f' % total_amount) %></td>
    </tr>
  <% end %>
  </tbody>
</table>
