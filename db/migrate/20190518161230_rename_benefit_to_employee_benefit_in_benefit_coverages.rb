class RenameBenefitToEmployeeBenefitInBenefitCoverages < ActiveRecord::Migration[5.2]
  disable_ddl_transaction!

  def change
    remove_foreign_key :benefit_coverages, :benefits
    remove_index :benefit_coverages, :benefit_id
    safety_assured { rename_column :benefit_coverages, :benefit_id, :employee_benefit_id }
    add_index :benefit_coverages, :employee_benefit_id, algorithm: :concurrently
    safety_assured { add_foreign_key :benefit_coverages, :employee_benefits }
  end
end
