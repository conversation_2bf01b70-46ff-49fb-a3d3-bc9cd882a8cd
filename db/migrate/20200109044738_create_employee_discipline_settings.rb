class CreateEmployeeDisciplineSettings < ActiveRecord::Migration[6.0]
  def change
    create_table :employee_discipline_settings do |t|
      t.references :employee, foreign_key: true
      t.references :discipline_setting, foreign_key: true

      t.string :discipline_setting_type, null: false, default: ''
      t.text :description, null: false, default: ''
      t.date :date, null: false, default: {}
      t.datetime :discarded_at, index: true
      t.string :slug

      t.timestamps
    end
  end
end
