class CreateNotifications < ActiveRecord::Migration[6.0]
  def change
    create_table :notifications do |t|
      t.string :subject, null: false, default: ''
      t.text :sms_message, null: false, default: ''
      t.text :email_message, null: false, default: ''
      t.boolean :sms, default: false
      t.boolean :email, default: false
      t.json :filters, default: ''

      t.timestamps
    end
  end
end
