# frozen_string_literal: true

class AddScheduledNotificationColumnsToNotifications < ActiveRecord::Migration[6.0]
  disable_ddl_transaction!
  def up
    add_column :notifications, :is_scheduled, :boolean, default: false
    add_column :notifications, :scheduled_date, :date
    add_column :notifications, :scheduled_time, :time
    add_column :notifications, :job_id, :string
    add_column :notifications, :discarded_at, :datetime
    add_column :notifications, :status, :integer, default: 0
    add_index :notifications, :discarded_at, algorithm: :concurrently
  end

  def down
    safety_assured { remove_column :notifications, :is_scheduled, :boolean }
    safety_assured { remove_column :notifications, :scheduled_date, :date }
    safety_assured { remove_column :notifications, :scheduled_time, :time }
    safety_assured { remove_column :notifications, :job_id, :string }
    safety_assured { remove_column :notifications, :discarded_at, :datetime }
    safety_assured { remove_column :notifications, :status, :integer }
    if index_exists?(:notifications, :discarded_at)
      remove_index :notifications, :discarded_at
    end
  end
end