# frozen_string_literal: true

class AddColumsInReminder < ActiveRecord::Migration[6.0]
  disable_ddl_transaction!

  def up
    add_column :reminders, :admin_only, :boolean, default: false
    add_reference :reminders, :employee_grievance, foreign_key: true, null: true, index: { algorithm: :concurrently }
  end

  def down
    remove_reference :reminders, :employee_grievance, index: true, foreign_key: true
    remove_column :reminders, :admin_only
  end
end
