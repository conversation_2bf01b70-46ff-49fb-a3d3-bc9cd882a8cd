module CustomTokenResponse
  include Api::ResponseFormatter
  include ApplicationHelper

  def body
    # call original `#body` method and merge its result with the additional data hash
    super.merge(render_success(data: Employee.kept.find(token.resource_owner_id), options: { include: [:contacts, :gender, :affiliation, :unit, :marital_status, :tour_of_duty, :platoon], change_request: "employee" }))
  end
end