# frozen_string_literal: true

desc 'Delete duplicate contacts for the all accounts'
task :delete_duplicate_contacts, [:tenant] => [:environment] do |_t, args|
  ## We can pass the account as argument
  ## Arguments can be single or multiple, if multiple it should seperate with white space.
  ## Eg: bundle exec rake 'delete_duplicate_contacts[papba btoba]'
  ## If arguments is empty, it will run for all domains
  ## Eg: bundle exec rake 'delete_duplicate_contacts[]'

  @errors = {}
  subdomains = args[:tenant]&.split || ''
  subdomains = Account.pluck(:subdomain) if subdomains.blank?
  Account.where(subdomain: subdomains).select(:subdomain).each do |account|
    Apartment::Tenant.switch!(account.subdomain)
    employees = Employee.kept.includes(:contacts).select(:id, :first_name, :last_name, :suffix, :middle_name)
    employees.each do |emp|
      @row_number = emp.full_name
      personal_email = emp.contacts.kept.where(contact_type: 'email', contact_for: 'personal').order(value: :asc)
      work_email = emp.contacts.kept.where(contact_type: 'email', contact_for: 'work').order(value: :asc)
      personal_phone = emp.contacts.kept.where(contact_type: 'phone', contact_for: 'personal').order(value: :asc)
      work_phone = emp.contacts.kept.where(contact_type: 'phone', contact_for: 'work').order(value: :asc)
      home_phone = emp.contacts.kept.where(contact_type: 'phone', contact_for: 'home').order(value: :asc)
      emergency_colleague = emp.contacts.kept.where(contact_type: 'emergency', contact_for: 'colleague').order(value: :asc)
      emergency_personal = emp.contacts.kept.where(contact_type: 'emergency', contact_for: 'personal').order(value: :asc)

      contact_check_and_update(personal_email) if personal_email.count > 1
      contact_check_and_update(work_email) if work_email.count > 1
      contact_check_and_update(personal_phone) if personal_phone.count > 1
      contact_check_and_update(work_phone) if work_phone.count > 1
      contact_check_and_update(home_phone) if home_phone.count > 1
      contact_check_and_update(emergency_colleague) if emergency_colleague.count > 1
      contact_check_and_update(emergency_personal) if emergency_personal.count > 1

      personal_email.first_or_create
      work_email.first_or_create
      personal_phone.first_or_create
      work_phone.first_or_create
      home_phone.first_or_create
      emergency_colleague.first_or_create
      emergency_personal.first_or_create
    end
    generate_error_file("#{account.subdomain}_duplicate_contact_errors")
  end
end

def contact_check_and_update(contact)
  first_value = contact.first.id
  if contact.map(&:value).uniq.count == 1
    contact.where('id != ?', first_value).update_all(discarded_at: Time.now)
  elsif contact.count == 2 && contact.first.value.present? && [nil, ''].include?(contact.last.value)
    contact.where('id != ?', first_value).update_all(discarded_at: Time.now)
  elsif contact.count == 2 && contact.last.value.present? && [nil, ''].include?(contact.first.value)
    first_value = contact.last.id
    contact.where('id != ?', first_value).update_all(discarded_at: Time.now)
  elsif contact.count > 2
    dup_feed_errors("#{contact.first.contact_for} #{contact.first.contact_type} - More than two contacts found, with different values")
  end
rescue => e
  dup_feed_errors("#{contact.first.contact_for} #{contact.first.contact_type} - #{e}")
end

def generate_error_file(filename)
  CSV.open("#{Rails.root}/#{filename}.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end
end

def dup_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

## bundle exec rake 'delete_duplicate_contacts[papba btoba nysscoa]' ## Specific domains single or multiple.
## bundle exec rake 'delete_duplicate_contacts[]' ## All domains.
