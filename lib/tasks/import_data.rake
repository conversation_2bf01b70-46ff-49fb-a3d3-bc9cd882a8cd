# frozen_string_literal: true

desc 'import data'
task import_data: :environment do
  Importers::EmployeesImporter.import("#{Rails.root}/csv/employees.csv")
  Importers::AwardsImporter.import("#{Rails.root}/csv/awards.csv")
  Importers::EmployeeBenefitsImporter.import("#{Rails.root}/csv/employee_benefits.csv")
  Importers::BenefitCoveragesImporter.import("#{Rails.root}/csv/benefit_coverages.csv")
  Importers::BenefitDisbursementsImporter.import("#{Rails.root}/csv/benefit_disbursements.csv")
  Importers::ContactsImporter.import("#{Rails.root}/csv/employees.csv")
  Importers::BeneficiariesImporter.import("#{Rails.root}/csv/beneficiaries.csv")
  Importers::EmployeeEmploymentStatusesImporter.import("#{Rails.root}/csv/employees.csv")
  Importers::EmployeeFirearmStatusesImporter.import("#{Rails.root}/csv/employee_firearm_statuses.csv")
  Importers::EmployeeOfficerStatusesImporter.import("#{Rails.root}/csv/employee_officer_statuses.csv")
  Importers::EmployeeOfficesImporter.import("#{Rails.root}/csv/employee_offices.csv")
  Importers::EmployeePositionsImporter.import("#{Rails.root}/csv/employee_positions.csv")
  Importers::EmployeeRanksImporter.import("#{Rails.root}/csv/employee_ranks.csv")
  Importers::LodisImporter.import("#{Rails.root}/csv/lodis.csv")
  Importers::PlacardsImporter.import("#{Rails.root}/csv/placards.csv")
  Importers::SickBanksImporter.import("#{Rails.root}/csv/sick_banks.csv")
end
