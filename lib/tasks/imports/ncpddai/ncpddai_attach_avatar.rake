# frozen_string_literal: true

require 'csv'

desc 'import data'

task :ncpddai_attach_avatar, [:account, :dir_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  # target_folder_path = File.join(Rails.root, args[:dir_path])

  # attachment_names = Dir.children(target_folder_path)
  file_path = args[:dir_path]

  @errors = {}

  Dir.foreach(file_path).each do |attachment|
    next if attachment == '.' || attachment == '..' || attachment == '.DS_Store'

    @row_number = attachment.split('.')
    @row_number.pop
    @row_number = @row_number.join(' ')

    employee_shield_number = attachment.split.last.split('.').first
    first_name = attachment.split.first
    last_name = attachment.split.last(2).first
    last_name = attachment.split.last(2).first.split('.').last if last_name.include?('.')

    employees = Employee.where(shield_number: employee_shield_number) if employee_shield_number.present?

    if employees.count > 1 || employees.blank?
      employees = Employee.where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
      if employees.count > 1
        ncpddai_feed_errors('More than one Members found')
        next
      elsif employees.blank?
        ncpddai_feed_errors('Member not found')
        next
      end
    end
    employee = employees.first

    if employee.present?
      employee.avatar.attach(io: File.open("#{file_path}/#{attachment}"), filename: attachment)
    end
  rescue => e
    ncpddai_feed_errors(e.message)
  end

  file_name = args[:dir_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']

    @errors.each do |error|
      csv << error
    end
  end

end

def ncpddai_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

# bundle exec rake 'ncpddai_attach_avatar[ncpddai, ncpddai_images_directory_path]'
