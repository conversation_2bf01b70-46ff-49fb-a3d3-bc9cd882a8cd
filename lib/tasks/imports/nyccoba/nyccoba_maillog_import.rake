# frozen_string_literal: true

require 'csv'

desc 'import data'

task :nyccoba_maillogs_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  maillogs_hash = { date_received: '', received_from: '', entered_by: '', going_to: '', maillog_type: '' }
  total_arr = []
  count = 0
  csv_file.each do |row|
    mailogs_clone = maillogs_hash.deep_dup
    mailogs_clone[:date_received] = if row['DateReceived'].present?
                                      Date.parse(row['DateReceived'])
                                    else
                                      ''
                                    end
    mailogs_clone[:received_from] = row['ReceivedFrom'] || ''
    mailogs_clone[:entered_by] = row['EnteredBy'] || ''
    mailogs_clone[:going_to] = row['GoingTo'] || ''
    mailogs_clone[:maillog_type] = if row['ReturnReceipt'] == 'Y'
                                     'ReturnReceipt'
                                   elsif row['Certified'].present?
                                     'Certified'
                                   end

    total_arr << mailogs_clone
    if count == 100
      Maillog.import total_arr.flatten
      total_arr = []
      count = 0
    end
    count += 1
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  Maillog.import total_arr.flatten

  CSV.open("#{Rails.root}/nyccoba_maillogs_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_annuity, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }
  payment_type_hash = {}
  annuity_hash = { employee_id: '', check_no: '', date: '', tax_amount: '', payee: '', payment_type_id: '', gross_amount: '', payee_ssn: '', amount: '' }
  total_arr = []
  count = 0
  csv_file.each do |row|
    ssn = row['Member SSN'] || ''
    @row_number = ssn

    if (@row_number = row['Member SSN']).blank?
      @row_number = row['Member Name ']
      if @row_number.blank?
        nyccoba_feed_errors('Mandatory details not present')
        next
      end
    end

    if ssn.present?
      ssn_rjust = ssn.gsub('-', '').rjust(9, '0')
      employees = Employee.kept.where(social_security_number: ssn_rjust)
    end

    if ssn.blank? || employees.blank? || employees.count > 1
      first_name = row['Member Name '].split.first
      last_name = row['Member Name '].split.last
      employees = Employee.kept.where(first_name: first_name, last_name: last_name)
      if employees.blank?
        nyccoba_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyccoba_feed_errors('More than One employee found')
        next
      end
    end

    employee = employees.first

    # PaymentTypes
    payment_type_id = if row['Tax Code 1'].present? && payment_type_hash[@value_downcase.call(row['Tax Code 1']).to_sym].present?
                        payment_type_hash[@value_downcase.call(row['Tax Code 1']).to_sym]
                      elsif row['Tax Code 1'].present?
                        create_associated_model_and_values('PaymentType', row['Tax Code 1'], payment_type_hash).id
                      end

    annuity_hash_clone = annuity_hash.deep_dup
    annuity_hash_clone[:employee_id] = employee.id
    annuity_hash_clone[:check_no] = row['Payment Number'] || ''
    annuity_hash_clone[:date] = if row['Payment Date'].present?
                                  Date.strptime(row['Payment Date'], '%m/%d/%Y')
                                else
                                  ''
                                end
    annuity_hash_clone[:tax_amount] = convert_amount_to_float(row['Federal Tax']) || ''
    annuity_hash_clone[:payee] = row['Pay to'] || ''
    annuity_hash_clone[:payment_type_id] = payment_type_id
    annuity_hash_clone[:gross_amount] = convert_amount_to_float(row['Gross Amount']) || ''
    annuity_hash_clone[:amount] = convert_amount_to_float(row['Net Amount']) || ''
    annuity_hash_clone[:payee_ssn] = row['PAYEE SSN'] || ''

    total_arr << annuity_hash_clone
    if count == 100
      EmployeePacf.import total_arr.flatten
      total_arr = []
      count = 0
    end
    count += 1
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  EmployeePacf.import total_arr.flatten

  CSV.open("#{Rails.root}/#{args[:file_path]}_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_claims_import, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}
  @value_downcase = ->(x) { x&.downcase&.delete(' ') }
  benefit_type_hash = {}
  claims_hash = { employee_id: '', employee_benefit_id: '', date: '', amount: '', entry_date: '' }
  total_arr = []
  count = 0
  csv_file.each do |row|
    ssn = row['SSN'] || ''
    @row_number = ssn

    if (@row_number).blank?
      @row_number = [row['FirstName'], row['LastName']]
      if row['FirstName'].blank? || row['LastName'].blank?
        nyccoba_feed_errors('Mandatory details not present')
        next
      end
    end

    if ssn.present?
      ssn_rjust = ssn.gsub('-', '').rjust(9, '0')
      employees = Employee.kept.includes(:employee_benefits).where(social_security_number: ssn_rjust)
    end

    if ssn.blank? || employees.blank? || employees.count > 1
      first_name = row['FirstName']
      last_name = row['LastName']
      employees = Employee.kept.includes(:employee_benefits).where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
      if employees.blank?
        nyccoba_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyccoba_feed_errors('More than One employee found')
        next
      end
    end

    employee = employees.first

    # Benefit
    benefit_id = if row['ClaimNumber'].present? && benefit_type_hash[@value_downcase.call(row['ClaimNumber']).to_sym].present?
                   benefit_type_hash[@value_downcase.call(row['ClaimNumber']).to_sym]
                 elsif row['ClaimNumber'].present?
                   create_associated_model_and_values('Benefit', row['ClaimNumber'], benefit_type_hash).id
                 end

    employee_benefit = employee.employee_benefits.select { |v| v.benefit_id == benefit_id }
    employee_benefit = if employee_benefit.present?
                         employee_benefit.first
                       else
                         employee.employee_benefits.create!(benefit_id: benefit_id)
                       end
    employee_benefit_id = employee_benefit.id

    claims_hash_clone = claims_hash.deep_dup
    claims_hash_clone[:employee_id] = employee.id
    claims_hash_clone[:employee_benefit_id] = employee_benefit_id
    claims_hash_clone[:relationship] = if row['DependantNumber'].present? && row['DependantNumber']&.to_i == 0
                                         'Self'
                                       elsif row['DependantNumber'].present? && row['DependantNumber']&.to_i == 1
                                         'Spouse'
                                       elsif row['DependantNumber'].present? && row['DependantNumber']&.to_i > 1
                                         'Child'
                                       else
                                         ''
                                       end
    claims_hash_clone[:date] = if row['ServiceDate'].present?
                                 Date.strptime(row['ServiceDate'], '%Y-%m-%d')
                               else
                                 ''
                               end
    claims_hash_clone[:entry_date] = if row['EntryDate'].present?
                                       Date.strptime(row['EntryDate'], '%Y-%m-%d')
                                     else
                                       ''
                                     end

    claims_hash_clone[:amount] = convert_amount_to_float(row['ClaimAmount']) || ''

    total_arr << claims_hash_clone
    if count == 100
      BenefitDisbursement.import total_arr.flatten
      total_arr = []
      count = 0
    end
    count += 1
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  BenefitDisbursement.import total_arr.flatten

  CSV.open("#{Rails.root}/nyccoba_claims_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_benefit_coverages_expiration, [:account] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  current_account = Account.find_by(subdomain: args[:account])
  @errors = {}

  employment_status_ids = EmploymentStatus.where('lower(name) in (?)', ['deceased', 'terminated', 'transferred', 'resigned']).ids

  other_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages', 'coverage_expire_age') if current_account.present?
  other_coverage_expire_age ||= 26

  optical_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages', 'optical_coverage_expire_age') if current_account.present?
  optical_coverage_expire_age ||= 19

  dental_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages', 'dental_coverage_expire_age') if current_account.present?
  dental_coverage_expire_age ||= 23
  is_optical_dental_benefit = false

  expire_relationship_types = current_account.saas_json.dig('schema', 'benefit_coverages', 'expire_relationship_types') if current_account.present?
  expire_relationship_types ||= false

  retire_benefit_coverage_expire_age = current_account.saas_json.dig('schema', 'benefit_coverages',
                                                                     'retire_benefit_coverage_expire_age') if current_account.present?

  BenefitCoverage.kept.includes(employee_benefit: :benefit,
                                employee: :employee_employment_statuses).where(unexpire: false, expires_at: nil, semester: 'N').each do |benefit_coverage|
    now = Time.now
    age = now.year - benefit_coverage.birthday.to_time.year - (benefit_coverage.birthday.to_time.change(:year => now.year) > now ? 1 : 0) if benefit_coverage.birthday

    if %w[btoba nysscoa nyccoba].include?(current_account.subdomain) && %w[optical vision].include?(benefit_coverage.employee_benefit.benefit.name.downcase)
      coverage_expire_age = optical_coverage_expire_age
      is_optical_dental_benefit = true
    elsif %w[nysscoa].include?(current_account.subdomain) && %w[dentalppo empiredental unitedhealthcaredental cookdental].include?(benefit_coverage.employee_benefit.benefit&.name&.downcase&.delete(' '))
      coverage_expire_age = dental_coverage_expire_age
      is_optical_dental_benefit = true
    else
      coverage_expire_age = other_coverage_expire_age
      is_optical_dental_benefit = false
    end

    if age.present? && age >= coverage_expire_age && ((expire_relationship_types.present? && expire_relationship_types.include?(benefit_coverage.relationship.downcase)) ||
      (benefit_coverage.relationship.downcase == "child" || (is_optical_dental_benefit && benefit_coverage.relationship.downcase == 'step_child')))
      expires_at = benefit_coverage.birthday + coverage_expire_age.year
      benefit_coverage.update(expires_at: expires_at)
    end

  rescue StandardError => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  if retire_benefit_coverage_expire_age.present?
    Employee.kept.includes(:employee_employment_statuses, :benefit_coverages).where('employee_employment_statuses.employment_status_id = ? and (benefit_coverages.expires_at is null and benefit_coverages.unexpire = ? and benefit_coverages.semester = ?)',
                                                                                    EmploymentStatus.find_by('lower(name) = ?', 'retired').id, false, 'N').references(:employee_employment_statuses, :benefit_coverages).each do |employee|
      benefit_coverages = employee.benefit_coverages
      benefit_coverages.each do |benefit_coverage|
        now = Time.now
        age = now.year - benefit_coverage.birthday.to_time.year - (benefit_coverage.birthday.to_time.change(:year => now.year) > now ? 1 : 0) if benefit_coverage.birthday
        if (age.present? && retire_benefit_coverage_expire_age.present? && age > retire_benefit_coverage_expire_age &&
          %w[child step_child].include?(benefit_coverage.relationship))
          retire_expires_at = benefit_coverage.birthday + retire_benefit_coverage_expire_age.year
          benefit_coverage.update(expires_at: retire_expires_at)
        end
      end
    end
  end
  CSV.open("#{Rails.root}/nyccoba_benefit_coverages_expiration_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors', 'MemberID']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_school_status_update, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  employee_outside_loop = nil
  member_id = nil

  @errors = {}
  csv_file.each do |row|
    ssn = row['MemberSSN'] || ''
    @row_number = ssn

    if @row_number.blank?
      @row_number = [row['MemberFirstName'], row['MemberLastName']]
      if row['MemberFirstName'].blank? || row['MemberLastName'].blank?
        nyccoba_feed_errors('Mandatory details not present')
        next
      end
    end

    if ssn.present? && member_id != row['MemberID']
      ssn_rjust = ssn.gsub('-', '').rjust(9, '0')
      employees = Employee.kept.includes(:employee_benefits, :benefit_coverages).where(social_security_number: ssn_rjust)
    end

    if (ssn.blank? || employees.blank? || employees.count > 1) && member_id != row['MemberID']
      first_name = row['MemberFirstName']
      last_name = row['MemberLastName']
      employees = Employee.kept.includes(:employee_benefits, :benefit_coverages).where('lower(first_name) = ? and lower(last_name) = ?', first_name.downcase, last_name.downcase)
      if employees.blank?
        nyccoba_feed_errors('Member not found')
        next
      elsif employees.count > 1
        nyccoba_feed_errors('More than One employee found')
        next
      end
    end

    employee = if member_id.blank? || member_id != row['MemberID']
                 employees.first
               elsif member_id == row['MemberID']
                 employee_outside_loop
               end

    benef_first_name = row['FirstName'] || ''
    benef_last_name = row['LastName'] || ''

    benefit_coverages = employee.benefit_coverages.where("lower(first_name) = ? and lower(last_name) = ?", benef_first_name.downcase, benef_last_name.downcase)
    if benefit_coverages.pluck(:birthday).uniq.count > 1 && row['DOB'].present?
      benef_birthday = Date.parse(row['DOB'])
      benefit_coverages = employee.benefit_coverages.where("lower(first_name) = ? and lower(last_name) = ? and birthday = ?", benef_first_name.downcase, benef_last_name.downcase, benef_birthday)
    end

    if benefit_coverages.pluck(:birthday).uniq.count > 1
      nyccoba_feed_errors('Multiple dependents with same name for this member')
      next
    end

    school_status = nyccoba_get_school_status(row)
    benefit_coverages.update_all(student: school_status)

    employee_outside_loop = employee if employees.present?
    member_id = row['MemberID']
  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/nyccoba_school_status_update_import_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

task :nyccoba_dependents_ordering, [:account] => :environment do |_t, args|
  employee_benefit_id = nil
  employee_benefit_id_count = nil
  total_arr = []
  count = 0
  Apartment::Tenant.switch!(args[:account])
  benefit_coverages_relationship = BenefitCoverage.pluck(:relationship).uniq.compact
  benefit_coverages_relationship.each do |relationship|
    relationship_downcase = relationship.downcase.gsub(' ', '_')
    BenefitCoverage.where(relationship: relationship).update_all(relationship: relationship_downcase)
  end

  BenefitCoverage.kept.where(relationship: 'member').update_all(dependent: 0)
  BenefitCoverage.kept.where(relationship: ['spouse', 'domestic_partner' ]).update_all(dependent: 1)

  @errors = {}
  BenefitCoverage.kept.includes(:employee, :employee_benefit).where(relationship: %w[child disabled_child disabled_step_child step_child]).order(:employee_id, :employee_benefit_id, birthday: :asc).each do |benefit_coverage|
    employee_benefit_id_count = 2 if employee_benefit_id != benefit_coverage.employee_benefit_id
    @row_number = benefit_coverage.employee.full_name
    benefit_coverage.dependent =  employee_benefit_id_count
    total_arr << benefit_coverage

    if count == 500
      BenefitCoverage.import total_arr.flatten, on_duplicate_key_update: [:dependent]
      total_arr = []
      count = 0
    end

    employee_benefit_id = benefit_coverage.employee_benefit_id
    employee_benefit_id_count += 1
    count += 1

  rescue => e
    p @row_number, e.message
    nyccoba_feed_errors(e.message)
  end

  BenefitCoverage.import total_arr.flatten, on_duplicate_key_update: [:dependent]

  CSV.open("#{Rails.root}/nyccoba_dependents_ordering_errors.csv", 'w') do |csv|
    csv << ['Row Number', 'Errors']
    @errors.each do |error|
      csv << error
    end
  end
end

def nyccoba_feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end

def create_associated_model_and_values(model_name, value, hash)
  return unless value.present?

  if (model_value = model_name.constantize.where('lower(name) = ?', value&.downcase)&.first).present?
    model_value
  else
    model_value = model_name.constantize.create!(name: value)
  end
  hash[@value_downcase.call(value).to_sym] = model_value.id if hash[value.to_sym].blank? || (hash[value.to_sym].present? && hash[value.to_sym] != model_value.id)
  model_value
end

def convert_amount_to_float(amount)
  return if amount.blank?

  amount.delete('^0-9.,').gsub(',', '').to_f
end

def nyccoba_get_school_status(row)
  date_range = Date.parse('2024-02-01')..Date.parse('2025-01-31')
  spring1 = Date.parse(row['Spring']) if row['Spring'].present?
  spring2 = Date.parse(row['Spring2']) if row['Spring2'].present?
  fall1 = Date.parse(row['Fall']) if row['Fall'].present?
  fall2 = Date.parse(row['Fall2']) if row['Fall2'].present?

  if date_range === spring1 && date_range === spring2 || date_range === fall1 && date_range === fall2
    true
  else
    false
  end
end

# bundle exec rake "nyccoba_maillogs_import[nyccoba, mailog.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-1.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-2.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-3.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-4.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-5.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-6.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-7.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-8.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-9.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-10.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-11.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-12.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-13.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-14.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-15.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-16.csv]"
# bundle exec rake "nyccoba_annuity[nyccoba, nyccoba_annuity-18.csv]"
# bundle exec rake "nyccoba_school_status_update[nyccoba,nyccoba_school_status_update_import.csv]"
# bundle exec rake "nyccoba_dependents_ordering[nyccoba]"