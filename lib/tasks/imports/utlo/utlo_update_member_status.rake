# frozen_string_literal: true

require 'csv'

desc 'import data'
task :utlo_update_member_status, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  # BSC ID

  active_emp_status = check_employment_status('Active')
  inactive_emp_status = check_employment_status('Inactive')
  today = Date.today
  shield_numbers = []

  csv_file.each do |row|

    shield_number = row['BSC ID']
    shield_numbers << shield_number

    @row_number = shield_number

    current_account = Account.find_by(subdomain: Apartment::Tenant.current)
    @current_account = Account.find_by(subdomain: Apartment::Tenant.current)

    employee = if shield_number.present?
                 Employee.where(shield_number: shield_number).first
               else
                 feed_errors('Employee not found')
               end

    last_active_emp_status = if employee.employee_employment_statuses.present?
                               employee.employee_employment_statuses.where(employment_status_id: active_emp_status.id).last
                             else
                               nil
                             end

    if (last_active_emp_status.present? && last_active_emp_status.end_date.present?) || last_active_emp_status.nil?
      employee.employee_employment_statuses.new(employment_status_id: active_emp_status.id, start_date: today).save!
    end

    if last_active_emp_status.present? && last_active_emp_status.start_date == nil
      # last_active_emp_status.update(start_date: today)
      feed_errors('Active Status\'s start date is not present')
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  Employee.where.not(shield_number: shield_numbers).each do |employee|
    last_inactive_emp_status = if employee.employee_employment_statuses.present?
                                 employee.employee_employment_statuses.where(employment_status_id: inactive_emp_status.id).last
                               else
                                 nil
                               end

    if (last_inactive_emp_status.present? && last_inactive_emp_status.end_date.present?) || last_inactive_emp_status.nil?
      employee.employee_employment_statuses.new(employment_status_id: inactive_emp_status.id, start_date: today).save!
    end

    if last_inactive_emp_status.present? && last_inactive_emp_status.start_date == nil
      # last_inactive_emp_status.update(start_date: today)
      feed_errors('Inactive Status\'s start date is not present')
    end
  end

  file_name = args[:file_path].split('.').first

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_#{file_name}_import_errors.csv", 'w') do |csv|
    csv << ["Row Number", "Errors"]

    @errors.each do |error|
      csv << error
    end
  end

end

def check_employment_status(employment_status_name)
  return nil unless employment_status_name.present?

  employment_status_name = EmploymentStatus.where(name: employment_status_name).first_or_create

  employment_status_name

rescue => e
  feed_errors('EMPLOYEMENT STATUS ' + employment_status_name.errors.full_messages)
end

def feed_errors(message)
  if @errors[@row_number].present?
    @errors[@row_number] << message
  else
    @errors[@row_number] = [message]
  end
end
