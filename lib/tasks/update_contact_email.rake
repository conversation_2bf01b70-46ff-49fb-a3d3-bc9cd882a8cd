# frozen_string_literal: true

require 'csv'

desc 'import data'
task :update_contact_email, [:account, :file_path] => :environment do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)

  @errors = {}

  csv_file.each do |row|
    @first_name = row['First Name']
    @last_name = row['Last Name']
    @email = row['Email address']

    employees = Employee.where("first_name ilike ? and last_name ilike ?", (@first_name + '%'), (@last_name + '%'))

    if employees.present?
      if employees.count > 1
        feed_errors('Multiple occurrences for name')
      else
        employee = employees.first
        employee.contacts.new(contact_for: Contact::ContactFor::WORK, contact_type: Contact::ContactType::EMAIL, value: @email).save! if @email
      end
    else
      feed_errors("Employee doesn't exist")
    end

  rescue => e
    p @row_number, e.message
    feed_errors(e.message)
  end

  CSV.open("#{Rails.root}/#{args[:account]}_members_email_import_errors.csv", 'w') do |csv|
    csv << ["Email", "First Name", "Last Name", "Error"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :get_legislative_address, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  token = get_token
  count = 0
  @errors = []

  csv_file.each do |row|
    member_name    = row['Member Name']
    member_address = row['Address']
    member_city    = row['City']
    member_state   = row['State']
    member_zipcode = row['Zip']
    council_district_from_csv = row['Council District Name']
    p member_name

    address = "#{member_address}, #{member_city}, #{member_state} #{member_zipcode}"

    # URL encode the address
    encoded_address = URI.encode_www_form_component(address)

    # Build the correct GET request URL
    url = "https://locatenyc.io/arcgis/rest/services/locateNYC/v1/GeocodeServer/findAddressCandidates?SingleLine=#{encoded_address}&token=#{token}"

    uri = URI.parse(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Get.new(uri.request_uri, {
      'Accept' => 'application/json'
    })

    response = http.request(request)
    if response.code != "200"
      token = get_token
      next
    end
    result = JSON.parse(response.body)

    city_council_district = result["candidates"][0]["attributes"]["cityCouncilDistrict"] if result["candidates"].present? && result["candidates"][0]["attributes"].present? && result["candidates"][0]["attributes"]["cityCouncilDistrict"].present?
    puts city_council_district
    # text1.scan(/\d+/).last
    council_district_from_csv = council_district_from_csv.scan(/\d+/).last.to_i if council_district_from_csv.present?

    city_council_district = city_council_district.to_i if city_council_district.present?

    next if city_council_district.present? && council_district_from_csv.present? && city_council_district == council_district_from_csv

    @errors << [member_name, address, council_district_from_csv, city_council_district] if city_council_district.present?
    @errors << [member_name, address, council_district_from_csv, "No Council District Found from API"] if city_council_district.blank?

    puts "================================="

  end
  CSV.open("#{Rails.root}/legislative_address_errors.csv", 'w') do |csv|
    csv << ["Member Name", "Address", "Council District from CSV", "Council District from API"]

    @errors.each do |error|
      csv << error
    end
  end

end

task :get_legislative_address_duplicate, [:account, :file_path] => :environment do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  file = File.read(args[:file_path])
  csv_file ||= CSV.parse(file, headers: true)
  token = get_token
  count = 0
  @errors = []

  csv_file.each do |row|
    member_name    = row['Member Name']
    member_address = row['Address']
    member_city    = row['City']
    member_state   = row['State']
    member_zipcode = row['Zip']
    council_district_from_csv = row['Council District Name']
    p member_name

    address = "#{member_address}, #{member_city}, #{member_state} #{member_zipcode}"

    # URL encode the address
    encoded_address = URI.encode_www_form_component(address)

    # Build the correct GET request URL
    url = "https://locatenyc.io/arcgis/rest/services/locateNYC/v1/GeocodeServer/findAddressCandidates?SingleLine=#{encoded_address}&token=#{token}"

    uri = URI.parse(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true

    request = Net::HTTP::Get.new(uri.request_uri, {
      'Accept' => 'application/json'
    })

    response = http.request(request)
    if response.code != "200"
      token = get_token
      next
    end
    result = JSON.parse(response.body)

    # city_council_district = result["candidates"][0]["attributes"]["cityCouncilDistrict"] if result["candidates"].present? && result["candidates"][0]["attributes"].present? && result["candidates"][0]["attributes"]["cityCouncilDistrict"].present?


    city_council_district = nil
    candidates = result["candidates"] if result["candidates"].present?

    if candidates.is_a?(Array) && candidates.any?
      attributes = candidates.first["attributes"]
      city_council_district = attributes["cityCouncilDistrict"] if attributes.present?
    end
    puts city_council_district
    # text1.scan(/\d+/).last
    council_district_from_csv = council_district_from_csv.scan(/\d+/).last.to_i if council_district_from_csv.present?

    city_council_district = city_council_district.to_i if city_council_district.present?

    next if city_council_district.present? && council_district_from_csv.present? && city_council_district == council_district_from_csv

    @errors << [member_name, address, council_district_from_csv, city_council_district] if city_council_district.present?
    @errors << [member_name, address, council_district_from_csv, "No Council District Found from API"] if city_council_district.blank?

    count += 1
    if count == 1000
      csv_download
      count = 0
      @errors = []
    end
  end
  csv_download
end

def csv_download
  CSV.open("#{Rails.root}/legislative_address_errors#{Time.now.to_i}.csv", 'w') do |csv|
    csv << ["Member Name", "Address", "Council District from CSV", "Council District from API"]

    @errors.each do |error|
      csv << error
    end
  end
end


def get_token
  payload = {
    username: "eeversman",
    password: "District@Fuse0925!"
  }

  # Prepare URI and HTTP request
  uri = URI.parse('https://locatenyc.io/arcgis/tokens/generateToken')
  http = Net::HTTP.new(uri.host, uri.port)
  http.use_ssl = true

  request = Net::HTTP::Post.new(uri.path, {
    'Content-Type' => 'application/json',
    'Accept' => 'application/json'
  })

  request.body = payload.to_json

  # Perform the API call
  response = http.request(request)

  # Parse and use the response as needed
  result = JSON.parse(response.body)
  result['token']
end

def feed_errors(message)
  @errors[@email] = [@first_name, @last_name, message]
end

# bundle exec rake 'update_contact_email[nyccoba, /Users/<USER>/Downloads/nyccoba_members_email.csv]'
