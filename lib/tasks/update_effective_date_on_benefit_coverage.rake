# frozen_string_literal: true

desc 'Update effective date on Benefit Coverages'
task :update_effective_date_on_benefit_coverage, [:account] =>[:environment] do |_t, args|

  Apartment::Tenant.switch!(args[:account])

  BenefitCoverage.kept.includes(:employee).each do |benefit_coverage|
    if benefit_coverage.relationship.downcase == 'spouse'
      employee = benefit_coverage.employee
      if employee.marital_status_date.present?
        benefit_coverage.update(effective_date: employee.marital_status_date)
      else
        benefit_coverage.update(effective_date: employee.start_date)
      end
    elsif benefit_coverage.birthday.present? && %w(child disabled_child step_child step_disabled_child).include?(benefit_coverage.relationship.downcase)
      benefit_coverage.update(effective_date: benefit_coverage.birthday)
    end
  end

end
