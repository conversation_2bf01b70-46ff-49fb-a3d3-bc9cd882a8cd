# frozen_string_literal: true

desc 'Update Undefined placard numbers for employees'
task :update_undefined_placard_numbers, [:account] => [:environment] do |_t, args|
  Apartment::Tenant.switch!(args[:account])
  @errors = {}

  employees = Employee.kept.where.not(placard_number: '')
  employees.each do |employee|
    @employee_name = "#{employee.first_name} #{employee.last_name}"
    placard_numbers = employee.placard_number.split(',', -1)
    next unless placard_numbers.include?('undefined')

    placard_numbers.map! { |placard_number| placard_number == 'undefined' ? '' : placard_number }
    updated_placard_number = placard_numbers.join(',')
    begin
      ActiveRecord::Base.transaction do
        employee.update_columns(placard_number: updated_placard_number)
      end
    rescue StandardError => e
      member_feed_errors("Error updating username for #{@employee_name}: #{e.message}")
    end
  end

  CSV.open("#{Rails.root}/#{args[:account]}_#{Date.today}_update_undefined_placard_numbers_errors.csv", 'w') do |csv|
    p csv << ['Employee Name', 'Errors']

    @errors.each do |error|
      p csv << error
    end
  end
end

def member_feed_errors(message)
  if @errors[@employee_name].present?
    @errors[@employee_name] << message
  else
    @errors[@employee_name] = [message]
  end
end
