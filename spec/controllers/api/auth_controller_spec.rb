# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::AuthController, type: :controller do
  let(:user) { Fabricate(:user) }
  let(:params){ {email: user.email, password: user.password} }

  describe 'POST #create' do

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Checks the email in response' do
        post :create, params: params
        expect(parsed_response['attributes']['email']).to eq(user.email)
        success_response
      end
    end

    context 'InValid cases', authenticated: true do
      it 'Checks the email in response' do
        post :create, params: { email: "<EMAIL>", password: "password" }
        unprocessable_entity
      end
    end
  end

  describe 'GET #refresh_jwt_token' do

    context 'Valid cases - authenticated used', authenticated: true do

      it 'Checks the success response' do
        get :refresh_jwt_token, params: params
        success_response
      end
    end
  end

  describe 'DELETE #destroy' do

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Checks the email in response' do
        delete :destroy
        success_response
      end
    end
  end
end