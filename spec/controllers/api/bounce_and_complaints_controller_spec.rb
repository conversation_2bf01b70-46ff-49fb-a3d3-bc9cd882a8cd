# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::Boun<PERSON>AndComplaintsController, type: :controller do
  let(:employee) { Fabricate(:employee) }
  let(:params) { Fabricate.attributes_for(:bounce_and_complaint) }

  def send_email_params(types, type, recipient)
    {
      "Type": 'Notification',
      "Message": {
        "notificationType": types,
        "#{type}": {
          "bounceType": 'Permanent',
          "bounceSubType": 'General',
          "#{recipient}Recipients": [
            {
              "emailAddress": '<EMAIL>'
            }
          ],
          "timestamp": '2017-08-05T00:40:02.012Z'
        },
        "mail": {
          "headers": [
            {
              "name": 'employee-id',
              "value": employee.id
            }
          ],
          "messageId": 'EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000',
          "commonHeaders": {
            "subject": 'Message sent from Amazon SES'
          }
        }
      }.to_json
    }
  end

  describe 'POST #create' do
    context 'Valid cases - authenticated user', authenticated: true do
      after :each do
        expect(BounceAndComplaint.last.subject).to eq('Message sent from Amazon SES')
        expect(BounceAndComplaint.last.message_id).to eq('EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000')
        success_response
      end

      it 'creates bounce' do
        expect do
          post :create, body: send_email_params('Bounce', 'bounce', 'bounced').to_json
        end.to change(BounceAndComplaint, :count).by(1)
        expect(BounceAndComplaint.last.bounce).to eq(true)
        expect(BounceAndComplaint.last.notification_type).to eq('Bounce')
      end

      it 'creates complaint' do
        expect do
          post :create, body: send_email_params('Complaint', 'complaint', 'complained').to_json
        end.to change(BounceAndComplaint, :count).by(1)
        expect(BounceAndComplaint.last.complaint).to eq(true)
        expect(BounceAndComplaint.last.notification_type).to eq('Complaint')
      end
    end
  end
end
