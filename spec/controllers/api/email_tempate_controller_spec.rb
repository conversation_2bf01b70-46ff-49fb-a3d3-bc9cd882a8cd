# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::EmailTemplatesController, type: :controller do
  let(:email_template) { EmailTemplate.create!(template_id: "sendgrid_template_id_#{SecureRandom.hex(4)}", template_html: "<html><body><p>Header: {{header}}</p><p>Message: {{message}}</p></body></html>", dynamic_fields: { 'header' => { 'type' => 'string', 'placeholder' => 'Header' }, 'message' => { 'type' => 'string', 'placeholder' => 'Message' } }) }

  describe 'GET #index' do
    it 'returns a list of email templates' do
      EmailTemplate.create!(template_id: "sendgrid_template_id_#{SecureRandom.hex(4)}", template_html: "<html><body><p>Header: {{header}}</p><p>Message: {{message}}</p></body></html>", dynamic_fields: { 'header' => { 'type' => 'string', 'placeholder' => 'Header' }, 'message' => { 'type' => 'string', 'placeholder' => 'Message' } })
      EmailTemplate.create!(template_id: "sendgrid_template_id_#{SecureRandom.hex(4)}", template_html: "<html><body><p>Header: {{header}}</p><p>Message: {{message}}</p></body></html>", dynamic_fields: { 'header' => { 'type' => 'string', 'placeholder' => 'Header' }, 'message' => { 'type' => 'string', 'placeholder' => 'Message' } })
      EmailTemplate.create!(template_id: "sendgrid_template_id_#{SecureRandom.hex(4)}", template_html: "<html><body><p>Header: {{header}}</p><p>Message: {{message}}</p></body></html>", dynamic_fields: { 'header' => { 'type' => 'string', 'placeholder' => 'Header' }, 'message' => { 'type' => 'string', 'placeholder' => 'Message' } })

      get :index

      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body).length).to eq(3)
    end
  end

  describe 'GET #show' do
    it 'returns a single email template by id' do
      template = EmailTemplate.create!(template_id: "sendgrid_template_id_#{SecureRandom.hex(4)}", template_html: "<html><body><p>Header: {{header}}</p><p>Message: {{message}}</p></body></html>", dynamic_fields: { 'header' => { 'type' => 'string', 'placeholder' => 'Header' }, 'message' => { 'type' => 'string', 'placeholder' => 'Message' } })

      get :show, params: { id: template.id }

      expect(response).to have_http_status(:ok)
      body = JSON.parse(response.body)

      expect(body['id']).to eq(template.id)
      expect(body['sendgrid_template_id']).to eq(template.template_id)
      expect(body['dynamic_fields']).to eq(template.dynamic_fields)
      expect(body['html_content']).to eq(template.template_html)
    end

    it 'returns not found when template does not exist' do
      get :show, params: { id: 9999 }

      expect(response).to have_http_status(:not_found)
    end
  end
end
