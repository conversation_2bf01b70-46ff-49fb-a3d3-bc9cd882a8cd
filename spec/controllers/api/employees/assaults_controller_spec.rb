require 'rails_helper'

RSpec.describe Api::Employees::AssaultsController do
  let(:employee) { Fabricate(:employee) }
  let(:assault) { Fabricate(:assault) }
  let(:params) { Fabricate.attributes_for(:assault) }

  describe "GET #index" do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index',
                    args: { object_name: 'assault' }

    before(:each) do
      Fabricate.times(30, :assault, employee: employee, discarded_at: nil)
    end


    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond assaults list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of assault page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond assaults list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of assault page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

  end

  describe "POST #create" do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create',
                    args: { object_name: 'assault'}

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create assault with valid params' do
        expect { post :create, params: { assault: params, format: :json } }.to change(Assault, :count).by(1)
        expect(parsed_response['attributes']['location']).to eq(params['location'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create assault with blank date' do
        validation_columns = ['date']
        modify_account_schema_validations(model: 'assaults', fields: validation_columns)
        post :create, params: { assault: params.merge(date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create assault with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'assaults', fields: validation_columns)
        post :create, params: { assault: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee_id: ["can't be blank"] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should check assault validation if required fields present' do
        validation_columns = %w[date]
        modify_account_schema_validations(model: 'assaults', fields: validation_columns)
        post :create, params: { assault: params.merge(date: ''), format: :json }
        blank_error_responses(validation_columns)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create assault' do
        post :create, params: { assault: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe "PUT #update" do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update',
                    args: { object_name: 'assault' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update assault with valid params' do
        assault1 = Fabricate(:assault)
        patch :update, params: { id: assault1.id, assault: params, format: :json }
        expect(parsed_response['attributes']['location']).to eq(params[:location])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update assault with blank date' do
        validation_columns = %w[date]
        modify_account_schema_validations(model: 'assaults', fields: validation_columns)
        patch :update, params: { id: assault.id, assault: { date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update assault with blank employee' do
        validation_columns = %w[employee_id]
        modify_account_schema_validations(model: 'assaults', fields: validation_columns)
        patch :update, params: { id: assault.id, assault: { employee_id: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check assault validation if required fields are not present' do
        validation_columns = %w[date]
        modify_account_schema_validations(model: 'assaults', fields: validation_columns)
        patch :update, params: { id: assault.id, assault: params.merge(date: ''), format: :json }
        blank_error_responses(validation_columns)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update assault with blank id' do
        patch :update, params: { id: '', assault: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update assault' do
        assault1 = Fabricate(:assault)
        patch :update, params: { id: assault1.id, assault: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe "DELETE #destroy" do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy',
                    args: { object_name: 'assault' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete assault with valid params' do
        delete :destroy, params: { id: assault.id, format: :json }
        expect(assigns(:assault).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete assault with blank id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete assault' do
        delete :destroy, params: { id: assault.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

end
