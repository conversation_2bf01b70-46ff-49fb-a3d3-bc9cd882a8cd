# frozen_string_literal: true

RSpec.describe Api::Employees::BeneficiariesController do
  let(:employee) { Fabricate(:employee) }
  let(:beneficiary) { Fabricate(:beneficiary) }
  let(:params) { Fabricate.attributes_for(:beneficiary) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'beneficiary',
                                                                                   rights: 'employee_benefit',
                                                                                   params: { employee_id: 1,
                                                                                             employee_benefit_id: 1 } }

    before(:each) do
      Fabricate.times(30, :beneficiary, employee: employee, percentage: 1.0)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Respond beneficiaries list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of beneficiary page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Respond beneficiaries list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of beneficiary page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'beneficiary', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create beneficiary with valid params' do
        expect { post :create, params: { beneficiary: params, format: :json } }.to change(Beneficiary, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create beneficiary with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        post :create, params: { beneficiary: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create beneficiary with blank percentage' do
        validation_columns = ['percentage']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        post :create, params: { beneficiary: params.merge(percentage: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create beneficiary with blank beneficiary_type' do
        validation_columns = ['beneficiary_type']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        post :create, params: { beneficiary: params.merge(beneficiary_type: ''), format: :json }
        expect(error_response[0]).to include({ beneficiary_type: ["can't be blank", 'is not included in the list'] }.as_json)
      end

      it 'Should not create beneficiary with blank relationship' do
        validation_columns = ['relationship']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        post :create, params: { beneficiary: params.merge(relationship: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create beneficiary with blank address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        post :create, params: { beneficiary: params.merge(address: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check beneficiary validation if required fields present' do
        validation_columns = %w[address relationship percentage name beneficiary_type]
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        post :create, params: { beneficiary: params.merge(relationship: '', name: '', address: '', percentage: '', beneficiary_type: ''), format: :json }
        blank_error_responses(validation_columns - ['beneficiary_type'])
        expect(error_response[0]).to include({ beneficiary_type: ["can't be blank", 'is not included in the list'] }.as_json)
      end

      it 'Should return 200 - Should not check beneficiary validation if required fields not present' do
        modify_account_schema_validations(model: 'beneficiaries', action: 'clear')
        expect do
          post :create, params: { beneficiary: params.merge(relationship: '', name: '', address: '', percentage: '1',
                                                            beneficiary_type: 'Primary'), format: :json }
        end.to change(Beneficiary, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['address']).to eq('')
        expect(parsed_response['attributes']['relationship']).to eq('')
        expect(parsed_response['attributes']['percentage']).to eq(1.0)
        expect(parsed_response['attributes']['beneficiary_type']).to eq('Primary')
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create beneficiary with invalid percentage limit' do
        post :create, params: { beneficiary: params.merge(percentage: 101.0), format: :json }
        expect(error_response).to eq([{ 'Beneficiary': [' % for Primary exceeding a max limit of 100.'] }.as_json])
      end

      it 'Should not create beneficiary with percentage limit > 0' do
        post :create, params: { beneficiary: params.merge(percentage: -1), format: :json }
        expect(error_response).to eq([{ 'percentage' => ['must be greater than 0'] }])
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create beneficiary' do
        post :create, params: { beneficiary: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'beneficiary', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update beneficiary with valid params' do
        beneficiary1 = Fabricate(:beneficiary)
        patch :update, params: { id: beneficiary1.id, beneficiary: params, format: :json }
        expect(parsed_response['attributes']['name']).to eq(params[:name])
        expect(parsed_response['attributes']['address']).to eq(params[:address])
        expect(parsed_response['attributes']['relationship']).to eq(params[:relationship])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update beneficiary with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        patch :update, params: { id: beneficiary.id, beneficiary: { name: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update beneficiary with blank percentage' do
        validation_columns = ['percentage']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        patch :update, params: { id: beneficiary.id, beneficiary: { percentage: '' }, format: :json }
        expect(error_response[0]).to include({ percentage: ["can't be blank", 'is not a number'] }.as_json)
      end

      it 'Should not update beneficiary with blank address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        patch :update, params: { id: beneficiary.id, beneficiary: { address: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update beneficiary with blank relationship' do
        validation_columns = ['relationship']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        patch :update, params: { id: beneficiary.id, beneficiary: { relationship: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update beneficiary with blank beneficiary type' do
        validation_columns = ['beneficiary_type']
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        patch :update, params: { id: beneficiary.id, beneficiary: { beneficiary_type: '' }, format: :json }
        expect(error_response[0]).to include({ beneficiary_type: ["can't be blank", 'is not included in the list'] }.as_json)
      end

      it 'Should check beneficiary validation if required fields present' do
        validation_columns = %w[address relationship percentage name beneficiary_type]
        modify_account_schema_validations(model: 'beneficiaries', fields: validation_columns)
        patch :update, params: { id: beneficiary.id, beneficiary: params.merge(relationship: '', name: '', address: '', percentage: '', beneficiary_type: ''), format: :json }
        blank_error_responses(validation_columns - %w[beneficiary_type percentage])
        expect(error_response[0]).to include({ beneficiary_type: ["can't be blank", 'is not included in the list'],
                                               percentage: ["can't be blank", 'is not a number'] }.as_json)
      end

      it 'Should return 200 - Should not check beneficiary validation if required fields not present' do
        modify_account_schema_validations(model: 'beneficiaries', action: 'clear')
        patch :update, params: { id: beneficiary.id, beneficiary: params.merge(relationship: '', name: '', address: '', percentage: '1', beneficiary_type: 'Primary'), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['address']).to eq('')
        expect(parsed_response['attributes']['relationship']).to eq('')
        expect(parsed_response['attributes']['percentage']).to eq(1.0)
        expect(parsed_response['attributes']['beneficiary_type']).to eq('Primary')
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update beneficiary with invalid percentage limit' do
        patch :update, params: { id: beneficiary.id, beneficiary: { percentage: 101.0 }, format: :json }
        expect(error_response).to eq([{ 'Beneficiary': [' % for Primary exceeding a max limit of 100.'] }.as_json])
      end

      it 'Should not update beneficiary with percentage limit > 0' do
        patch :update, params: { id: beneficiary.id, beneficiary: { percentage: -1 }, format: :json }
        expect(error_response).to eq([{ 'percentage' => ['must be greater than 0'] }])
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update beneficiary' do
        beneficiary1 = Fabricate(:beneficiary)
        patch :update, params: { id: beneficiary1.id, beneficiary: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'beneficiary', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete beneficiary with valid params' do
        delete :destroy, params: { id: beneficiary.id, format: :json }
        expect(assigns(:beneficiary).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete beneficiary with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete beneficiary' do
        delete :destroy, params: { id: beneficiary.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
