# frozen_string_literal: true

RSpec.describe Api::Employees::BenefitCoveragesController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_benefit) { Fabricate(:employee_benefit) }
  let(:benefit_coverage) { Fabricate(:benefit_coverage) }
  let(:params) { Fabricate.attributes_for(:benefit_coverage) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'benefit_coverage',
                                                                                   rights: 'employee_benefit',
                                                                                   params: { employee_id: 1,
                                                                                             employee_benefit_id: 1 } }

    before(:each) do
      Fabricate.times(30, :benefit_coverage, employee: employee, employee_benefit: employee_benefit)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Required id is missing'])
        unprocessable_entity
      end

      it 'Respond benefit coverage list with default pagination count' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: employee_benefit.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of benefit coverage page 2' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: employee_benefit.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Required id is missing'])
        unprocessable_entity
      end

      it 'Respond benefit coverage list with default pagination count' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: employee_benefit.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of benefit coverage page 2' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: employee_benefit.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'benefit_coverage', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create benefit coverage with valid params' do
        post :create, params: { benefit_coverage: params, format: :json }
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end

      it 'Should create benefit coverage with auto expiration' do
        post :create, params: { benefit_coverage: params.merge(relationship: 'child',birthday: Date.today - 23.years, expires_at: nil), format: :json }
        expect(parsed_response['attributes']['expires_at']).to eq(Date.today.to_s)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create benefit coverage with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        post :create, params: { benefit_coverage: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit coverage with blank address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        post :create, params: { benefit_coverage: params.merge(address: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit coverage with blank birthday' do
        validation_columns = ['birthday']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        post :create, params: { benefit_coverage: params.merge(birthday: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit coverage with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        post :create, params: { benefit_coverage: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit coverage with blank employee benefit' do
        validation_columns = ['employee_benefit_id']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        post :create, params: { benefit_coverage: params.merge(employee_benefit_id: ''), format: :json }
        expect(error_response[0]).to include({ employee_benefit: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit coverage with blank relationship' do
        validation_columns = ['relationship']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        post :create, params: { benefit_coverage: params.merge(relationship: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit coverage with blank SSN' do
        validation_columns = ['social_security_number']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        post :create, params: { benefit_coverage: params.merge(social_security_number: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check benefit coverage validation if required fields present' do
        validation_columns = %w[social_security_number birthday address relationship name]
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        post :create, params: { benefit_coverage: params.merge(social_security_number: '', birthday: '', address: '',
                                                               relationship: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check benefit coverage validation if required fields not present' do
        modify_account_schema_validations(model: 'benefit_coverages', action: 'clear')
        expect do
          post :create, params: { benefit_coverage: params.merge(social_security_number: '', birthday: '', address: '',
                                                                 relationship: '', name: ''), format: :json }
        end.to change(BenefitCoverage, :count).by(1)
        expect(parsed_response['attributes']['social_security_number']).to eq('')
        expect(parsed_response['attributes']['address']).to eq('')
        expect(parsed_response['attributes']['birthday']).to eq(nil)
        expect(parsed_response['attributes']['relationship']).to eq('')
        expect(parsed_response['attributes']['name']).to eq('')
      end

      it 'Should not create benefit coverage with auto expiration' do
        post :create, params: { benefit_coverage: params.merge(relationship: 'disabled_child', birthday: Date.today - 23.years, expires_at: nil), format: :json }
        expect(parsed_response['attributes']['expires_at']).to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create benefit coverage' do
        post :create, params: { benefit_coverage: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'benefit_coverage', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update benefit coverage with valid params' do
        benefit_coverage1 = Fabricate(:benefit_coverage)
        patch :update, params: { id: benefit_coverage1.id, benefit_coverage: params, format: :json }
        expect(parsed_response['attributes']['name']).to eq(params[:name])
        expect(parsed_response['attributes']['relationship']).to eq(params[:relationship])
        expect(parsed_response['attributes']['address']).to eq(params[:address])
        expect(parsed_response['attributes']['social_security_number']).to eq(params[:social_security_number])
        success_response
      end

      it 'Should update benefit coverage with auto expiration' do
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: { relationship: 'spouse', birthday: Date.today - 23.years }, format: :json }
        expect(parsed_response['attributes']['expires_at']).to eq(Date.today.to_s)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update benefit coverage with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: { name: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit coverage with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: { employee_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit coverage with blank employee benefit' do
        validation_columns = ['employee_benefit_id']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: { employee_benefit_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee_benefit: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit coverage with blank address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: { address: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit coverage with blank Birthday' do
        validation_columns = ['birthday']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: { birthday: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit coverage with blank SSN' do
        validation_columns = ['social_security_number']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: { social_security_number: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit coverage with blank relationship' do
        validation_columns = ['relationship']
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: { relationship: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check benefit coverage validation if required fields present' do
        validation_columns = %w[social_security_number birthday address relationship name]
        modify_account_schema_validations(model: 'benefit_coverages', fields: validation_columns)
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: params.merge(social_security_number: '', birthday: '', address: '',
                                                                                         relationship: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check benefit coverage validation if required fields not present' do
        modify_account_schema_validations(model: 'benefit_coverages', action: 'clear')
        patch :update, params: { id: benefit_coverage.id, benefit_coverage: params.merge(social_security_number: '', birthday: '', address: '',
                                                                                         relationship: '', name: ''), format: :json }
        expect(parsed_response['attributes']['social_security_number']).to eq('')
        expect(parsed_response['attributes']['address']).to eq('')
        expect(parsed_response['attributes']['birthday']).to eq(nil)
        expect(parsed_response['attributes']['relationship']).to eq('')
        expect(parsed_response['attributes']['name']).to eq('')
      end

      it 'Should not update benefit coverage with auto expiration' do
        benefit_coverage1 = Fabricate(:benefit_coverage)
        patch :update, params: { id: benefit_coverage1.id, benefit_coverage: params.merge(relationship: 'disabled_child',birthday: Date.today - 23.years, expires_at: nil), format: :json }
        expect(parsed_response['attributes']['expires_at']).to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update benefit coverage' do
        benefit_coverage1 = Fabricate(:benefit_coverage)
        patch :update, params: { id: benefit_coverage1.id, benefit_coverage: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'benefit_coverage', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete benefit coverage with valid params' do
        delete :destroy, params: { id: benefit_coverage.id, format: :json }
        expect(assigns(:benefit_coverage).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete benefit coverage with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete benefit coverage' do
        delete :destroy, params: { id: benefit_coverage.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
