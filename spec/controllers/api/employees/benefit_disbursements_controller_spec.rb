# frozen_string_literal: true

RSpec.describe Api::Employees::BenefitDisbursementsController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_benefit) { Fabricate(:employee_benefit) }
  let(:benefit_disbursement) { Fabricate(:benefit_disbursement) }
  let(:params) { Fabricate.attributes_for(:benefit_disbursement) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'benefit_disbursement',
                                                                                   rights: 'employee_benefit',
                                                                                   params: { employee_id: 1,
                                                                                             employee_benefit_id: 1 } }

    before(:each) do
      Fabricate.times(30, :benefit_disbursement, employee: employee, employee_benefit: employee_benefit)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'when employee_id params is empty' do
        get :index
        unprocessable_entity
      end

      it 'Display benefit_disbursement list' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: employee_benefit.id }, format: :json
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of benefit_disbursement page 2' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: employee_benefit.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee benefit id' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: '' }, format: :json
        expect(error_response).to eq(['Required id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'when employee_id params is empty' do
        get :index
        unprocessable_entity
      end

      it 'Display benefit_disbursement list' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: employee_benefit.id }, format: :json
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of benefit_disbursement page 2' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: employee_benefit.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee benefit id' do
        get :index, params: { employee_id: employee.id, employee_benefit_id: '' }, format: :json
        expect(error_response).to eq(['Required id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'benefit_disbursement', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create benefit disbursement with valid params' do
        expect { post :create, params: { benefit_disbursement: params, format: :json } }.to change(BenefitDisbursement, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq(params['notes'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create benefit disbursement with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        post :create, params: { benefit_disbursement: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit disbursement with blank amount' do
        validation_columns = ['amount']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        post :create, params: { benefit_disbursement: params.merge(amount: ''), format: :json }
        expect(error_response).to eq([{ 'amount' => ["can't be blank"] }])
      end

      it 'Should not create benefit disbursement with blank date' do
        validation_columns = ['date']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        post :create, params: { benefit_disbursement: params.merge(date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit disbursement with blank employee benefit' do
        validation_columns = ['employee_benefit_id']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        post :create, params: { benefit_disbursement: params.merge(employee_benefit_id: ''), format: :json }
        expect(error_response[0]).to include({ employee_benefit: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit disbursement with blank payment type' do
        validation_columns = ['payment_type_id']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        post :create, params: { benefit_disbursement: params.merge(payment_type_id: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit disbursement with blank reference number' do
        validation_columns = ['reference_number']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        post :create, params: { benefit_disbursement: params.merge(reference_number: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit disbursement with blank year' do
        validation_columns = ['year']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        post :create, params: { benefit_disbursement: params.merge(year: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check benefit disbursement validation if required fields present' do
        validation_columns = %w[year reference_number date amount]
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        post :create, params: { benefit_disbursement: params.merge(year: '', date: '', amount: '', reference_number: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check benefit disbursement validation if required fields not present' do
        modify_account_schema_validations(model: 'benefit_disbursements', action: 'clear')
        expect do
          post :create, params: { benefit_disbursement: params.merge(year: '2020', date: '', amount: '1', reference_number: ''), format: :json }
        end.to change(BenefitDisbursement, :count).by(1)
        expect(parsed_response['attributes']['year']).to eq(2020)
        expect(parsed_response['attributes']['date']).to eq(nil)
        expect(parsed_response['attributes']['amount']).to eq(1)
        expect(parsed_response['attributes']['reference_number']).to eq('')
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create benefit disbursement with year integer' do
        post :create, params: { benefit_disbursement: params.merge(year: 1.0), format: :json }
        expect(error_response).to eq([{ 'year' => ['must be an integer'] }])
      end

      it 'Should not create benefit disbursement with year > 0' do
        post :create, params: { benefit_disbursement: params.merge(year: 0), format: :json }
        expect(error_response).to eq([{ 'year' => ['must be greater than 0'] }])
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create benefit disbursement' do
        post :create, params: { benefit_disbursement: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'benefit_disbursement', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update benefit disbursement with valid params' do
        benefit_disbursement1 = Fabricate(:benefit_disbursement)
        patch :update, params: { id: benefit_disbursement1.id, benefit_disbursement: params, format: :json }
        expect(parsed_response['attributes']['amount']).to eq(params[:amount])
        expect(parsed_response['attributes']['reference_number']).to eq(params[:reference_number])
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        expect(parsed_response['attributes']['year']).to eq(params[:year])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update benefit disbursement with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { employee_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit disbursement with blank amount' do
        validation_columns = ['amount']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { amount: '' }, format: :json }
        expect(error_response[0]).to include({ amount: ["can't be blank", 'is not a number'] }.as_json)
      end

      it 'Should not update benefit disbursement with blank date' do
        validation_columns = ['date']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit disbursement with blank employee benefit' do
        validation_columns = ['employee_benefit_id']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { employee_benefit_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee_benefit: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit disbursement with blank payment type' do
        validation_columns = ['payment_type_id']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { payment_type_id: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit disbursement with blank reference number' do
        validation_columns = ['reference_number']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { reference_number: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit disbursement with year' do
        validation_columns = ['year']
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { year: '' }, format: :json }
        expect(error_response[0]).to include({ year: ["can't be blank", 'is not a number'] }.as_json)
      end

      it 'Should check benefit disbursement validation if required fields present' do
        validation_columns = %w[year reference_number date amount]
        modify_account_schema_validations(model: 'benefit_disbursements', fields: validation_columns)
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { year: '', reference_number: '', date: '', amount: '' }, format: :json }
        blank_error_responses(validation_columns - %w[year amount])
        expect(error_response[0]).to include({ year: ["can't be blank", 'is not a number'], amount: ["can't be blank", 'is not a number'] }.as_json)
      end

      it 'Should return 200 - Should not check benefit disbursement validation if required fields not present' do
        modify_account_schema_validations(model: 'benefit_disbursements', action: 'clear')
        patch :update, params: { id: benefit_disbursement.id, benefit_disbursement: { year: '2020', reference_number: '',
                                                                                      date: '', amount: '1' }, format: :json }
        expect(parsed_response['attributes']['year']).to eq(2020)
        expect(parsed_response['attributes']['date']).to eq(nil)
        expect(parsed_response['attributes']['amount']).to eq(1)
        expect(parsed_response['attributes']['reference_number']).to eq('')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update benefit disbursement' do
        benefit_disbursement1 = Fabricate(:benefit_disbursement)
        patch :update, params: { id: benefit_disbursement1.id, benefit_disbursement: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'benefit_disbursement', rights: 'employee_benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete benefit disbursement with valid params' do
        delete :destroy, params: { id: benefit_disbursement.id, format: :json }
        expect(assigns(:benefit_disbursement).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete benefit disbursement with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete benefit disbursement' do
        delete :destroy, params: { id: benefit_disbursement.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
