# frozen_string_literal: true

RSpec.describe Api::Employees::ChangeRequestsController, type: :controller do
  let(:params) { Fabricate.attributes_for(:change_request) }
  let(:contact_change_request_params) { Fabricate.attributes_for(:contact_change_request) }
  let(:award_update_params) { Fabricate.attributes_for(:award_update) }
  let(:award_create_params) { Fabricate.attributes_for(:award_create) }
  let(:beneficiary_update_params) { Fabricate.attributes_for(:beneficiary_update) }
  let(:beneficiary_create_params) { Fabricate.attributes_for(:beneficiary_create) }
  let(:benefit_coverage_update_params) { Fabricate.attributes_for(:benefit_coverage_update) }
  let(:benefit_coverage_create_params) { Fabricate.attributes_for(:benefit_coverage_create) }
  let(:benefit_disbursement_update_params) { Fabricate.attributes_for(:benefit_disbursement_update) }
  let(:benefit_disbursement_create_params) { Fabricate.attributes_for(:benefit_disbursement_create) }
  let(:delegate_assignment_update_param) { Fabricate.attributes_for(:delegate_assignment_update) }
  let(:delegate_assignment_create_params) { Fabricate.attributes_for(:delegate_assignment_create) }
  let(:dependent_update_params) { Fabricate.attributes_for(:dependent_update) }
  let(:dependent_create_params) { Fabricate.attributes_for(:dependent_create) }
  let(:employee_benefit_update_params) { Fabricate.attributes_for(:employee_benefit_update) }
  let(:employee_benefit_create_params) { Fabricate.attributes_for(:employee_benefit_create) }
  let(:employee_department_update_params) { Fabricate.attributes_for(:employee_department_update) }
  let(:employee_department_create_params) { Fabricate.attributes_for(:employee_department_create) }
  let(:employee_discipline_setting_update_params) { Fabricate.attributes_for(:employee_discipline_setting_update) }
  let(:employee_discipline_setting_create_params) { Fabricate.attributes_for(:employee_discipline_setting_create) }
  let(:employee_discipline_step_update_params) { Fabricate.attributes_for(:employee_discipline_step_update) }
  let(:employee_discipline_step_create_params) { Fabricate.attributes_for(:employee_discipline_step_create) }
  let(:employee_employment_status_update_params) { Fabricate.attributes_for(:employee_employment_status_update) }
  let(:employee_employment_status_create_params) { Fabricate.attributes_for(:employee_employment_status_create) }
  let(:employee_firearm_status_update_params) { Fabricate.attributes_for(:employee_firearm_status_update) }
  let(:employee_firearm_status_create_params) { Fabricate.attributes_for(:employee_firearm_status_create) }
  let(:employee_grievance_update_params) { Fabricate.attributes_for(:employee_grievance_update) }
  let(:employee_grievance_create_params) { Fabricate.attributes_for(:employee_grievance_create) }
  let(:employee_grievance_step_update_params) { Fabricate.attributes_for(:employee_grievance_step_update) }
  let(:employee_grievance_step_create_params) { Fabricate.attributes_for(:employee_grievance_step_create) }
  let(:employee_meeting_type_update_params) { Fabricate.attributes_for(:employee_meeting_type_update) }
  let(:employee_meeting_type_create_params) { Fabricate.attributes_for(:employee_meeting_type_create) }
  let(:employee_officer_status_update_params) { Fabricate.attributes_for(:employee_officer_status_update) }
  let(:employee_officer_status_create_params) { Fabricate.attributes_for(:employee_officer_status_create) }
  let(:employee_office_update_params) { Fabricate.attributes_for(:employee_office_update) }
  let(:employee_office_create_params) { Fabricate.attributes_for(:employee_office_create) }
  let(:employee_pacf_update_params) { Fabricate.attributes_for(:employee_pacf_update) }
  let(:employee_pacf_create_params) { Fabricate.attributes_for(:employee_pacf_update) }
  let(:employee_position_update_params) { Fabricate.attributes_for(:employee_position_update) }
  let(:employee_position_create_params) { Fabricate.attributes_for(:employee_position_create) }
  let(:employee_rank_update_params) { Fabricate.attributes_for(:employee_rank_update) }
  let(:employee_rank_create_params) { Fabricate.attributes_for(:employee_rank_create) }
  let(:employee_section_update_params) { Fabricate.attributes_for(:employee_section_update) }
  let(:employee_section_create_params) { Fabricate.attributes_for(:employee_section_create) }
  let(:employee_title_update_params) { Fabricate.attributes_for(:employee_title_update) }
  let(:employee_title_create_params) { Fabricate.attributes_for(:employee_title_create) }
  let(:firearm_range_score_update_params) { Fabricate.attributes_for(:firearm_range_score_update) }
  let(:firearm_range_score_create_params) { Fabricate.attributes_for(:firearm_range_score_create) }
  let(:leave_update_params) { Fabricate.attributes_for(:leave_update) }
  let(:leave_create_params) { Fabricate.attributes_for(:leave_create) }
  let(:life_insurance_update_params) { Fabricate.attributes_for(:life_insurance_update) }
  let(:life_insurance_create_params) { Fabricate.attributes_for(:life_insurance_create) }
  let(:lodi_update_params) { Fabricate.attributes_for(:lodi_update) }
  let(:lodi_create_params) { Fabricate.attributes_for(:lodi_create) }
  let(:lodi_request_tab_update_params) { Fabricate.attributes_for(:lodi_request_tab_update) }
  let(:lodi_request_tab_create_params) { Fabricate.attributes_for(:lodi_request_tab_create) }
  let(:upload_update_params) { Fabricate.attributes_for(:upload_update) }
  let(:upload_create_params) { Fabricate.attributes_for(:upload_create) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: {rights: 'change_request', object_name: 'change_request'}

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate(:change_request)
        Fabricate(:contact_change_request)
        Fabricate(:award_update)
        Fabricate(:award_create)
        Fabricate(:beneficiary_update)
        Fabricate(:beneficiary_create)
        Fabricate(:benefit_coverage_update)
        Fabricate(:benefit_coverage_create)
        Fabricate(:benefit_disbursement_update)
        Fabricate(:benefit_disbursement_create)
        Fabricate(:delegate_assignment_update)
        Fabricate(:delegate_assignment_create)
        Fabricate(:dependent_update)
        Fabricate(:dependent_create)
        Fabricate(:employee_benefit_update)
        Fabricate(:employee_benefit_create)
        Fabricate(:employee_department_update)
        Fabricate(:employee_department_create)
        Fabricate(:employee_discipline_setting_update)
        Fabricate(:employee_discipline_setting_create)
        Fabricate(:employee_discipline_step_update)
        Fabricate(:employee_discipline_step_create)
        Fabricate(:employee_employment_status_update)
        Fabricate(:employee_employment_status_create)
        Fabricate(:employee_firearm_status_update)
        Fabricate(:employee_firearm_status_create)
        Fabricate(:employee_grievance_update)
        Fabricate(:employee_grievance_create)
        Fabricate(:employee_grievance_step_update)
        Fabricate(:employee_grievance_step_create)
        Fabricate(:employee_meeting_type_update)
        Fabricate(:employee_meeting_type_create)
        Fabricate(:employee_officer_status_update)
        Fabricate(:employee_officer_status_create)
        Fabricate(:employee_office_update)
        Fabricate(:employee_office_create)
        Fabricate(:employee_pacf_update)
        Fabricate(:employee_pacf_create)
        Fabricate(:employee_position_update)
        Fabricate(:employee_position_create)
        Fabricate(:employee_rank_update)
        Fabricate(:employee_rank_create)
        Fabricate(:employee_section_update)
        Fabricate(:employee_section_create)
        Fabricate(:employee_title_update)
        Fabricate(:employee_title_create)
        Fabricate(:firearm_range_score_update)
        Fabricate(:firearm_range_score_create)
        Fabricate(:leave_update)
        Fabricate(:leave_create)
        Fabricate(:life_insurance_update)
        Fabricate(:life_insurance_create)
        Fabricate(:lodi_update)
        Fabricate(:lodi_create)
        Fabricate(:lodi_request_tab_update)
        Fabricate(:lodi_request_tab_create)
        Fabricate(:upload_update)
        Fabricate.times(3, :upload_create)
      end

      it 'Display list of change request' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of change request page 2' do
        get :index, params: {page: 2}
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of change request page 2' do
        get :index, params: {page: 3}
        expect(parsed_response.count).to eq(10)
        success_response
      end
    end
  end

  describe 'POST #create' do
    context 'Valid cases - authenticated employee', authenticated_employee: true do
      context "Should create a change request to existing records - with valid params" do
        it 'Should create change request with valid params and request type - employee' do
          expect do
            post :create, params: {change_request: params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          expect(parsed_response['attributes']['requested_changes'][1]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - contact' do
          expect do
            post :create, params: {change_request: contact_change_request_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('contact')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          expect(parsed_response['attributes']['requested_changes'][1]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - award' do
          expect do
            post :create, params: {change_request: award_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('award')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - beneficiary' do
          expect do
            post :create, params: {change_request: beneficiary_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('beneficiary')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - benefit_coverage' do
          expect do
            post :create, params: {change_request: benefit_coverage_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('benefit_coverage')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - benefit_disbursement' do
          expect do
            post :create, params: {change_request: benefit_disbursement_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('benefit_disbursement')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - delegate_assignment' do
          expect do
            post :create, params: {change_request: delegate_assignment_update_param}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('delegate_assignment')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - dependent' do
          expect do
            post :create, params: {change_request: dependent_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('dependent')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_benefit' do
          expect do
            post :create, params: {change_request: employee_benefit_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_benefit')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_department' do
          expect do
            post :create, params: {change_request: employee_department_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_department')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_discipline_setting' do
          expect do
            post :create, params: {change_request: employee_discipline_setting_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_discipline_setting')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_discipline_step' do
          expect do
            post :create, params: {change_request: employee_discipline_step_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_discipline_step')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_employment_status' do
          expect do
            post :create, params: {change_request: employee_employment_status_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_employment_status')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_firearm_status' do
          expect do
            post :create, params: {change_request: employee_firearm_status_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_firearm_status')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_grievance' do
          expect do
            post :create, params: {change_request: employee_grievance_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_grievance')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_grievance_step' do
          expect do
            post :create, params: {change_request: employee_grievance_step_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_grievance_step')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_meeting_type' do
          expect do
            post :create, params: {change_request: employee_meeting_type_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_meeting_type')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_officer_status' do
          expect do
            post :create, params: {change_request: employee_officer_status_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_officer_status')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_office' do
          expect do
            post :create, params: {change_request: employee_office_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_office')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_pacf' do
          expect do
            post :create, params: {change_request: employee_pacf_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_pacf')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_position' do
          expect do
            post :create, params: {change_request: employee_position_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_position')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_rank' do
          expect do
            post :create, params: {change_request: employee_rank_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_rank')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_section' do
          expect do
            post :create, params: {change_request: employee_section_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_section')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_title' do
          expect do
            post :create, params: {change_request: employee_title_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_title')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - firearm_range_score' do
          expect do
            post :create, params: {change_request: firearm_range_score_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('firearm_range_score')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - leave' do
          expect do
            post :create, params: {change_request: leave_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('leave')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - life_insurance' do
          expect do
            post :create, params: {change_request: life_insurance_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('life_insurance')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - lodi' do
          expect do
            post :create, params: {change_request: lodi_update_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('lodi')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - lodi_request_tab' do
          expect do
            post :create, params: {change_request: lodi_request_tab_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('lodi_request_tab')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - upload' do
          post :create, params: {change_request: upload_update_params}
          expect(parsed_response['attributes']['request_type']).to eq('upload')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end
      end

      context "Should create a change request to new records - with valid params" do
        it 'Should create change request with valid params and request type - award' do
          expect do
            post :create, params: {change_request: award_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('award')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - beneficiary' do
          expect do
            post :create, params: {change_request: beneficiary_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('beneficiary')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - benefit_coverage' do
          expect do
            post :create, params: {change_request: benefit_coverage_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('benefit_coverage')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - benefit_disbursement' do
          expect do
            post :create, params: {change_request: benefit_disbursement_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('benefit_disbursement')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - delegate_assignment' do
          expect do
            post :create, params: {change_request: delegate_assignment_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('delegate_assignment')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - dependent' do
          expect do
            post :create, params: {change_request: dependent_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('dependent')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_benefit' do
          expect do
            post :create, params: {change_request: employee_benefit_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_benefit')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_department' do
          expect do
            post :create, params: {change_request: employee_department_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_department')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_discipline_setting' do
          expect do
            post :create, params: {change_request: employee_discipline_setting_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_discipline_setting')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_discipline_step' do
          expect do
            post :create, params: {change_request: employee_discipline_step_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_discipline_step')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_employment_status' do
          expect do
            post :create, params: {change_request: employee_employment_status_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_employment_status')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_firearm_status' do
          expect do
            post :create, params: {change_request: employee_firearm_status_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_firearm_status')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_grievance' do
          expect do
            post :create, params: {change_request: employee_grievance_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_grievance')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_grievance_step' do
          expect do
            post :create, params: {change_request: employee_grievance_step_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_grievance_step')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_meeting_type' do
          expect do
            post :create, params: {change_request: employee_meeting_type_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_meeting_type')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_officer_status' do
          expect do
            post :create, params: {change_request: employee_officer_status_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_officer_status')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_office' do
          expect do
            post :create, params: {change_request: employee_office_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_office')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_pacf' do
          expect do
            post :create, params: {change_request: employee_pacf_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_pacf')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_position' do
          expect do
            post :create, params: {change_request: employee_position_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_position')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_rank' do
          expect do
            post :create, params: {change_request: employee_rank_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_rank')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_section' do
          expect do
            post :create, params: {change_request: employee_section_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_section')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - employee_title' do
          expect do
            post :create, params: {change_request: employee_title_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('employee_title')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - firearm_range_score' do
          expect do
            post :create, params: {change_request: firearm_range_score_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('firearm_range_score')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - leave' do
          expect do
            post :create, params: {change_request: leave_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('leave')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - life_insurance' do
          expect do
            post :create, params: {change_request: life_insurance_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('life_insurance')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - lodi' do
          expect do
            post :create, params: {change_request: lodi_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('lodi')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - lodi_request_tab' do
          expect do
            post :create, params: {change_request: lodi_request_tab_create_params}
          end.to change(ChangeRequest, :count).by(1)
          expect(parsed_response['attributes']['request_type']).to eq('lodi_request_tab')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end

        it 'Should create change request with valid params and request type - upload' do
          post :create, params: {change_request: upload_create_params}
          expect(parsed_response['attributes']['request_type']).to eq('upload')
          expect(parsed_response['attributes']['requested_changes'][0]['status']).to eq('pending')
          success_response
        end
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Should not create change request with empty request type' do
        post :create, params: {change_request: params.merge(request_type: '')}
        expect(error_response[0]).to eq("Request type can't be blank")
        expect(error_response[1]).to eq("Request type is not included in the list")
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: {id: 1}

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: {rights: 'change_request', object_name: 'change_request'}

    context 'Valid cases - authenticated user', authenticated: true do
      context "Should update a change request to existing records - with valid params" do
        context "Should update change request with status - completed" do
          it 'Should update change request with valid params and request_type - employee' do
            change_request = Fabricate(:change_request)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - contact' do
            change_request = Fabricate(:contact_change_request)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "contact",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - award' do
            change_request = Fabricate(:award_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - beneficiary' do
            change_request = Fabricate(:beneficiary_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - benefit_coverage' do
            change_request = Fabricate(:benefit_coverage_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - benefit_disbursement' do
            change_request = Fabricate(:benefit_disbursement_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - delegate_assignment' do
            change_request = Fabricate(:delegate_assignment_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - dependent' do
            change_request = Fabricate(:dependent_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_benefit' do
            change_request = Fabricate(:employee_benefit_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_department' do
            change_request = Fabricate(:employee_department_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_discipline_setting' do
            change_request = Fabricate(:employee_discipline_setting_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_discipline_step' do
            change_request = Fabricate(:employee_discipline_step_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_employment_status' do
            change_request = Fabricate(:employee_employment_status_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_firearm_status' do
            change_request = Fabricate(:employee_firearm_status_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_grievance' do
            change_request = Fabricate(:employee_grievance_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_grievance_step' do
            change_request = Fabricate(:employee_grievance_step_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_meeting_type' do
            change_request = Fabricate(:employee_meeting_type_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_officer_status' do
            change_request = Fabricate(:employee_officer_status_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_office' do
            change_request = Fabricate(:employee_office_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_pacf' do
            change_request = Fabricate(:employee_pacf_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_position' do
            change_request = Fabricate(:employee_position_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_rank' do
            change_request = Fabricate(:employee_rank_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_section' do
            change_request = Fabricate(:employee_section_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_title' do
            change_request = Fabricate(:employee_title_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - firearm_range_score' do
            change_request = Fabricate(:firearm_range_score_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - leave' do
            change_request = Fabricate(:leave_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - life_insurance' do
            change_request = Fabricate(:life_insurance_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - lodi' do
            change_request = Fabricate(:lodi_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - lodi_request_tab' do
            change_request = Fabricate(:lodi_request_tab_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi_request_tab",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - upload' do
            change_request = Fabricate(:upload_update)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "upload",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end
        end

        context "Should update change request with status - rejected" do
          it 'Should update change request with valid params and request_type - employee' do
            change_request = Fabricate(:change_request)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - contact' do
            change_request = Fabricate(:contact_change_request)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "contact",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - award' do
            change_request = Fabricate(:award_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - beneficiary' do
            change_request = Fabricate(:beneficiary_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - benefit_coverage' do
            change_request = Fabricate(:benefit_coverage_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - benefit_disbursement' do
            change_request = Fabricate(:benefit_disbursement_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - delegate_assignment' do
            change_request = Fabricate(:delegate_assignment_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - dependent' do
            change_request = Fabricate(:dependent_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_benefit' do
            change_request = Fabricate(:employee_benefit_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_department' do
            change_request = Fabricate(:employee_department_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_discipline_setting' do
            change_request = Fabricate(:employee_discipline_setting_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_discipline_step' do
            change_request = Fabricate(:employee_discipline_step_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_employment_status' do
            change_request = Fabricate(:employee_employment_status_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_firearm_status' do
            change_request = Fabricate(:employee_firearm_status_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_grievance' do
            change_request = Fabricate(:employee_grievance_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_grievance_step' do
            change_request = Fabricate(:employee_grievance_step_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_meeting_type' do
            change_request = Fabricate(:employee_meeting_type_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_officer_status' do
            change_request = Fabricate(:employee_officer_status_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_office' do
            change_request = Fabricate(:employee_office_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_pacf' do
            change_request = Fabricate(:employee_pacf_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_position' do
            change_request = Fabricate(:employee_position_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_rank' do
            change_request = Fabricate(:employee_rank_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_section' do
            change_request = Fabricate(:employee_section_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_title' do
            change_request = Fabricate(:employee_title_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - firearm_range_score' do
            change_request = Fabricate(:firearm_range_score_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - leave' do
            change_request = Fabricate(:leave_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - life_insurance' do
            change_request = Fabricate(:life_insurance_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - lodi' do
            change_request = Fabricate(:lodi_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - lodi_request_tab' do
            change_request = Fabricate(:lodi_request_tab_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "lodi_request_tab",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - upload' do
            change_request = Fabricate(:upload_update)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "upload",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end
        end
      end

      context "Should update a change request to new records - with valid params" do
        context "Should update change request with status - completed" do
          it 'Should update change request with valid params and request_type - award' do
            change_request = Fabricate(:award_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - beneficiary' do
            change_request = Fabricate(:beneficiary_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - benefit_coverage' do
            change_request = Fabricate(:benefit_coverage_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - benefit_disbursement' do
            change_request = Fabricate(:benefit_disbursement_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - delegate_assignment' do
            change_request = Fabricate(:delegate_assignment_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - dependent' do
            change_request = Fabricate(:dependent_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_benefit' do
            change_request = Fabricate(:employee_benefit_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_department' do
            change_request = Fabricate(:employee_department_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_discipline_setting' do
            change_request = Fabricate(:employee_discipline_setting_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_discipline_step' do
            change_request = Fabricate(:employee_discipline_step_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_employment_status' do
            change_request = Fabricate(:employee_employment_status_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_firearm_status' do
            change_request = Fabricate(:employee_firearm_status_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_grievance' do
            change_request = Fabricate(:employee_grievance_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_grievance_step' do
            change_request = Fabricate(:employee_grievance_step_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_meeting_type' do
            change_request = Fabricate(:employee_meeting_type_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_officer_status' do
            change_request = Fabricate(:employee_officer_status_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_office' do
            change_request = Fabricate(:employee_office_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_pacf' do
            change_request = Fabricate(:employee_pacf_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_position' do
            change_request = Fabricate(:employee_position_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_rank' do
            change_request = Fabricate(:employee_rank_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_section' do
            change_request = Fabricate(:employee_section_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_title' do
            change_request = Fabricate(:employee_title_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - firearm_range_score' do
            change_request = Fabricate(:firearm_range_score_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - leave' do
            change_request = Fabricate(:leave_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - life_insurance' do
            change_request = Fabricate(:life_insurance_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - lodi' do
            change_request = Fabricate(:lodi_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - lodi_request_tab' do
            change_request = Fabricate(:lodi_request_tab_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi_request_tab",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - upload' do
            change_request = Fabricate(:upload_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "upload",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end
        end
        context "Should update change request with status - rejected" do
          it 'Should update change request with valid params and request_type - award' do
            change_request = Fabricate(:award_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - beneficiary' do
            change_request = Fabricate(:beneficiary_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - benefit_coverage' do
            change_request = Fabricate(:benefit_coverage_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - benefit_disbursement' do
            change_request = Fabricate(:benefit_disbursement_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - delegate_assignment' do
            change_request = Fabricate(:delegate_assignment_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - dependent' do
            change_request = Fabricate(:dependent_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_benefit' do
            change_request = Fabricate(:employee_benefit_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_department' do
            change_request = Fabricate(:employee_department_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_discipline_setting' do
            change_request = Fabricate(:employee_discipline_setting_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_discipline_step' do
            change_request = Fabricate(:employee_discipline_step_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_employment_status' do
            change_request = Fabricate(:employee_employment_status_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_firearm_status' do
            change_request = Fabricate(:employee_firearm_status_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_grievance' do
            change_request = Fabricate(:employee_grievance_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_grievance_step' do
            change_request = Fabricate(:employee_grievance_step_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_meeting_type' do
            change_request = Fabricate(:employee_meeting_type_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_officer_status' do
            change_request = Fabricate(:employee_officer_status_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_office' do
            change_request = Fabricate(:employee_office_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_pacf' do
            change_request = Fabricate(:employee_pacf_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_position' do
            change_request = Fabricate(:employee_position_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_rank' do
            change_request = Fabricate(:employee_rank_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_section' do
            change_request = Fabricate(:employee_section_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - employee_title' do
            change_request = Fabricate(:employee_title_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - firearm_range_score' do
            change_request = Fabricate(:firearm_range_score_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - leave' do
            change_request = Fabricate(:leave_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - life_insurance' do
            change_request = Fabricate(:life_insurance_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - lodi' do
            change_request = Fabricate(:lodi_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - lodi_request_tab' do
            change_request = Fabricate(:lodi_request_tab_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "lodi_request_tab",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end

          it 'Should update change request with valid params and request_type - upload' do
            change_request = Fabricate(:upload_create)
            patch :update, params: {status: "rejected", id: change_request.id, request_type: "upload",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            success_response
          end
        end
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      context 'Should not update a change request to existing records - with invalid params' do
        context 'Should not update change request(Invalid cases for request_type - award)' do
          it 'Should not update award without an id' do
            change_request = Fabricate(:award_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => Award.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update award without employee' do
            change_request = Fabricate(:award_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update award without name' do
            change_request = Fabricate(:award_update)
            change_request.requested_changes[0].delete('name')
            change_request.requested_changes[0].merge!('name' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Name can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - beneficiary)' do
          it 'Should not update beneficiary without an id' do
            change_request = Fabricate(:beneficiary_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => Beneficiary.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update beneficiary without employee' do
            change_request = Fabricate(:beneficiary_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update beneficiary without name' do
            change_request = Fabricate(:beneficiary_update)
            change_request.requested_changes[0].delete('name')
            change_request.requested_changes[0].merge!('name' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Name can't be blank"])
          end

          it 'Should not update beneficiary without percentage' do
            change_request = Fabricate(:beneficiary_update)
            change_request.requested_changes[0].delete('percentage')
            change_request.requested_changes[0].merge!('percentage' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response[0]).to eq("Percentage can't be blank")
            expect(error_response[1]).to eq("Percentage is not a number")
          end
        end

        context 'Should not update change request(Invalid cases for request_type - benefit_coverage)' do
          it 'Should not update benefit_coverage without an id' do
            change_request = Fabricate(:benefit_coverage_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => BenefitCoverage.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update benefit_coverage without employee' do
            change_request = Fabricate(:benefit_coverage_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update benefit_coverage without address' do
            change_request = Fabricate(:benefit_coverage_update)
            change_request.requested_changes[0].delete('address')
            change_request.requested_changes[0].merge!('address' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Address can't be blank"])
          end

          it 'Should not update benefit_coverage without employee_benefit' do
            change_request = Fabricate(:benefit_coverage_update)
            change_request.requested_changes[0].delete('employee_benefit_id')
            change_request.requested_changes[0].merge!('employee_benefit_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee benefit can't be blank", "Employee benefit must exist"])
          end

          it 'Should not update benefit_coverage without birthday' do
            change_request = Fabricate(:benefit_coverage_update)
            change_request.requested_changes[0].delete('birthday')
            change_request.requested_changes[0].merge!('birthday' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Birthday can't be blank"])
          end

          it 'Should not update beneficiary without name' do
            change_request = Fabricate(:benefit_coverage_update)
            change_request.requested_changes[0].delete('name')
            change_request.requested_changes[0].merge!('name' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Name can't be blank"])
          end

          it 'Should not update beneficiary without social_security_number' do
            change_request = Fabricate(:benefit_coverage_update)
            change_request.requested_changes[0].delete('social_security_number')
            change_request.requested_changes[0].merge!('social_security_number' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Social security number can't be blank"])
          end

          it 'Should not update beneficiary without relationship' do
            change_request = Fabricate(:benefit_coverage_update)
            change_request.requested_changes[0].delete('relationship')
            change_request.requested_changes[0].merge!('relationship' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_coverage",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Relationship can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - benefit_disbursement)' do
          it 'Should not update benefit_disbursement without an id' do
            change_request = Fabricate(:benefit_disbursement_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => BenefitDisbursement.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update benefit_disbursement without employee' do
            change_request = Fabricate(:benefit_disbursement_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update benefit_disbursement without amount' do
            change_request = Fabricate(:benefit_disbursement_update)
            change_request.requested_changes[0].delete('amount')
            change_request.requested_changes[0].merge!('amount' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response[0]).to eq("Amount can't be blank")
            expect(error_response[1]).to eq("Amount is not a number")
          end

          it 'Should not update benefit_disbursement without employee_benefit' do
            change_request = Fabricate(:benefit_disbursement_update)
            change_request.requested_changes[0].delete('employee_benefit_id')
            change_request.requested_changes[0].merge!('employee_benefit_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee benefit can't be blank", "Employee benefit must exist"])
          end

          it 'Should not update benefit_disbursement without payment_type' do
            change_request = Fabricate(:benefit_disbursement_update)
            change_request.requested_changes[0].delete('payment_type_id')
            change_request.requested_changes[0].merge!('payment_type_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Payment type can't be blank"])
          end

          it 'Should not update benefit_disbursement without date' do
            change_request = Fabricate(:benefit_disbursement_update)
            change_request.requested_changes[0].delete('date')
            change_request.requested_changes[0].merge!('date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Date can't be blank"])
          end

          it 'Should not update benefit_disbursement without reference_number' do
            change_request = Fabricate(:benefit_disbursement_update)
            change_request.requested_changes[0].delete('reference_number')
            change_request.requested_changes[0].merge!('reference_number' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Reference number can't be blank"])
          end

          it 'Should not update benefit_disbursement without year' do
            change_request = Fabricate(:benefit_disbursement_update)
            change_request.requested_changes[0].delete('year')
            change_request.requested_changes[0].merge!('year' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response[0]).to eq("Year can't be blank")
            expect(error_response[1]).to eq("Year is not a number")
          end
        end

        context 'Should not update change request(Invalid cases for request_type - delegate_assignment)' do
          it 'Should not update delegate_assignment without employee' do
            change_request = Fabricate(:delegate_assignment_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => DelegateAssignment.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update delegate_assignment without employee' do
            change_request = Fabricate(:delegate_assignment_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update delegate_assignment without office' do
            change_request = Fabricate(:delegate_assignment_update)
            change_request.requested_changes[0].delete('office_id')
            change_request.requested_changes[0].merge!('office_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Office can't be blank", "Office must exist"])
          end

          it 'Should not update delegate_assignment without delegate_employee' do
            change_request = Fabricate(:delegate_assignment_update)
            change_request.requested_changes[0].delete('delegate_employee_id')
            change_request.requested_changes[0].merge!('delegate_employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Delegate employee can't be blank", "Delegate employee must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - dependent)' do
          it 'Should not update dependent without an id' do
            change_request = Fabricate(:dependent_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => Dependent.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update dependent without employee' do
            change_request = Fabricate(:dependent_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update dependent without life_insurance' do
            change_request = Fabricate(:dependent_update)
            change_request.requested_changes[0].delete('life_insurance_id')
            change_request.requested_changes[0].merge!('life_insurance_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Life insurance can't be blank", "Life insurance must exist"])
          end

          it 'Should not update dependent without name' do
            change_request = Fabricate(:dependent_update)
            change_request.requested_changes[0].delete('name')
            change_request.requested_changes[0].merge!('name' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Name can't be blank"])
          end

          it 'Should not update dependent without relationship' do
            change_request = Fabricate(:dependent_update)
            change_request.requested_changes[0].delete('relationship')
            change_request.requested_changes[0].merge!('relationship' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Relationship can't be blank"])
          end

          it 'Should not update dependent without address' do
            change_request = Fabricate(:dependent_update)
            change_request.requested_changes[0].delete('address')
            change_request.requested_changes[0].merge!('address' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Address can't be blank"])
          end

          it 'Should not update dependent without amount' do
            change_request = Fabricate(:dependent_update)
            change_request.requested_changes[0].delete('amount')
            change_request.requested_changes[0].merge!('amount' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Amount can't be blank"])
          end

          it 'Should not update dependent without date' do
            change_request = Fabricate(:dependent_update)
            change_request.requested_changes[0].delete('date')
            change_request.requested_changes[0].merge!('date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_benefit)' do
          it 'Should not update employee_benefit without an id' do
            change_request = Fabricate(:employee_benefit_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeBenefit.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_benefit without employee' do
            change_request = Fabricate(:employee_benefit_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_benefit without benefit' do
            change_request = Fabricate(:employee_benefit_update)
            change_request.requested_changes[0].delete('benefit_id')
            change_request.requested_changes[0].merge!('benefit_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Benefit can't be blank", "Benefit must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_department)' do
          it 'Should not update employee_department without an id' do
            change_request = Fabricate(:employee_department_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeDepartment.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_department without employee' do
            change_request = Fabricate(:employee_department_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_department without department' do
            change_request = Fabricate(:employee_department_update)
            change_request.requested_changes[0].delete('department_id')
            change_request.requested_changes[0].merge!('department_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Department can't be blank", "Department must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_discipline_setting)' do
          it 'Should not update employee_discipline_setting without an id' do
            change_request = Fabricate(:employee_discipline_setting_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeDisciplineSetting.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_discipline_setting without employee' do
            change_request = Fabricate(:employee_discipline_setting_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_discipline_setting without discipline_setting' do
            change_request = Fabricate(:employee_discipline_setting_update)
            change_request.requested_changes[0].delete('discipline_setting_id')
            change_request.requested_changes[0].merge!('discipline_setting_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Discipline setting can't be blank", "Discipline setting must exist"])
          end

          it 'Should not update employee_discipline_setting without discipline_charge' do
            change_request = Fabricate(:employee_discipline_setting_update)
            change_request.requested_changes[0].delete('discipline_charge_id')
            change_request.requested_changes[0].merge!('discipline_charge_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Discipline charge can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_discipline_step)' do
          it 'Should not update employee_discipline_step without an id' do
            change_request = Fabricate(:employee_discipline_step_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeDisciplineStep.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_discipline_step without employee_discipline_setting' do
            change_request = Fabricate(:employee_discipline_step_update)
            change_request.requested_changes[0].delete('employee_discipline_setting_id')
            change_request.requested_changes[0].merge!('employee_discipline_setting_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee discipline setting can't be blank", "Employee discipline setting must exist"])
          end

          it 'Should not update employee_discipline_step without step' do
            change_request = Fabricate(:employee_discipline_step_update)
            change_request.requested_changes[0].delete('step')
            change_request.requested_changes[0].merge!('step' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Step can't be blank", "Step Step can't be updated"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_employment_status)' do
          it 'Should not update employee_employment_status without an id' do
            change_request = Fabricate(:employee_employment_status_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeEmploymentStatus.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_employment_status without employee' do
            change_request = Fabricate(:employee_employment_status_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_employment_status without employment_status' do
            change_request = Fabricate(:employee_employment_status_update)
            change_request.requested_changes[0].delete('employment_status_id')
            change_request.requested_changes[0].merge!('employment_status_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employment status can't be blank", "Employment status must exist"])
          end

          it 'Should not update employee_employment_status without start_date' do
            change_request = Fabricate(:employee_employment_status_update)
            change_request.requested_changes[0].delete('start_date')
            change_request.requested_changes[0].merge!('start_date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Start date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_firearm_status)' do
          it 'Should not update employee_firearm_status without an id' do
            change_request = Fabricate(:employee_firearm_status_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeFirearmStatus.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_firearm_status without employee' do
            change_request = Fabricate(:employee_firearm_status_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_firearm_status without firearm_status' do
            change_request = Fabricate(:employee_firearm_status_update)
            change_request.requested_changes[0].delete('firearm_status_id')
            change_request.requested_changes[0].merge!('firearm_status_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Firearm status can't be blank", "Firearm status must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_grievance)' do
          it 'Should not update employee_grievance without employee' do
            change_request = Fabricate(:employee_grievance_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeGrievance.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_grievance without employee' do
            change_request = Fabricate(:employee_grievance_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_grievance without grievance' do
            change_request = Fabricate(:employee_grievance_update)
            change_request.requested_changes[0].delete('grievance_id')
            change_request.requested_changes[0].merge!('grievance_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Grievance can't be blank", "Grievance must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_grievance_step)' do
          it 'Should not update employee_grievance without an id' do
            change_request = Fabricate(:employee_grievance_step_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeGrievanceStep.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_grievance without employee_grievance' do
            change_request = Fabricate(:employee_grievance_step_update)
            change_request.requested_changes[0].delete('employee_grievance_id')
            change_request.requested_changes[0].merge!('employee_grievance_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee grievance can't be blank", "Employee grievance must exist"])
          end

          it 'Should not update employee_grievance without step' do
            change_request = Fabricate(:employee_grievance_step_update)
            change_request.requested_changes[0].delete('step')
            change_request.requested_changes[0].merge!('step' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Step can't be blank", "Step Step can't be updated"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_meeting_type)' do
          it 'Should not update employee_meeting_type without an id' do
            change_request = Fabricate(:employee_meeting_type_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeMeetingType.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_meeting_type without employee' do
            change_request = Fabricate(:employee_meeting_type_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_meeting_type without meeting_type' do
            change_request = Fabricate(:employee_meeting_type_update)
            change_request.requested_changes[0].delete('meeting_type_id')
            change_request.requested_changes[0].merge!('meeting_type_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Meeting type can't be blank", "Meeting type must exist"])
          end

          it 'Should not update employee_meeting_type without meeting_date' do
            change_request = Fabricate(:employee_meeting_type_update)
            change_request.requested_changes[0].delete('meeting_date')
            change_request.requested_changes[0].merge!('meeting_date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Meeting date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_officer_status)' do
          it 'Should not update employee_officer_status without an id' do
            change_request = Fabricate(:employee_officer_status_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeOfficerStatus.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_officer_status without employee' do
            change_request = Fabricate(:employee_officer_status_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_officer_status without officer_status' do
            change_request = Fabricate(:employee_officer_status_update)
            change_request.requested_changes[0].delete('officer_status_id')
            change_request.requested_changes[0].merge!('officer_status_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Officer status can't be blank", "Officer status must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_office)' do
          it 'Should not update employee_office without an id' do
            change_request = Fabricate(:employee_office_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeOffice.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_office without employee' do
            change_request = Fabricate(:employee_office_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_office without office' do
            change_request = Fabricate(:employee_office_update)
            change_request.requested_changes[0].delete('office_id')
            change_request.requested_changes[0].merge!('office_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Office must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_pacf)' do
          it 'Should not update employee_pacf without an id' do
            change_request = Fabricate(:employee_pacf_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeePacf.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_pacf without employee' do
            change_request = Fabricate(:employee_pacf_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_pacf without pacf' do
            change_request = Fabricate(:employee_pacf_update)
            change_request.requested_changes[0].delete('pacf_id')
            change_request.requested_changes[0].merge!('pacf_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Pacf can't be blank", "Pacf must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_position)' do
          it 'Should not update employee_position without an id' do
            change_request = Fabricate(:employee_position_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeePosition.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_position without employee' do
            change_request = Fabricate(:employee_position_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_position without position' do
            change_request = Fabricate(:employee_position_update)
            change_request.requested_changes[0].delete('position_id')
            change_request.requested_changes[0].merge!('position_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Position can't be blank", "Position must exist"])
          end

          it 'Should not update employee_position without delegate_series' do
            change_request = Fabricate(:employee_position_update)
            change_request.requested_changes[0].delete('delegate_series_id')
            change_request.requested_changes[0].merge!('delegate_series_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Delegate series can't be blank"])
          end

          it 'Should not update employee_position without start_date' do
            change_request = Fabricate(:employee_position_update)
            change_request.requested_changes[0].delete('start_date')
            change_request.requested_changes[0].merge!('start_date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Start date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_rank)' do
          it 'Should not update employee_rank without an id' do
            change_request = Fabricate(:employee_rank_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeRank.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_rank without employee' do
            change_request = Fabricate(:employee_rank_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_rank without rank' do
            change_request = Fabricate(:employee_rank_update)
            change_request.requested_changes[0].delete('rank_id')
            change_request.requested_changes[0].merge!('rank_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Rank can't be blank", "Rank must exist"])
          end

          it 'Should not update employee_rank without start_date' do
            change_request = Fabricate(:employee_rank_update)
            change_request.requested_changes[0].delete('start_date')
            change_request.requested_changes[0].merge!('start_date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Start date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_section)' do
          it 'Should not update employee_section without an id' do
            change_request = Fabricate(:employee_section_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeSection.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_section without employee' do
            change_request = Fabricate(:employee_section_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_section without section' do
            change_request = Fabricate(:employee_section_update)
            change_request.requested_changes[0].delete('section_id')
            change_request.requested_changes[0].merge!('section_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Section can't be blank", "Section must exist"])
          end

          it 'Should not update employee_section without department' do
            change_request = Fabricate(:employee_section_update)
            change_request.requested_changes[0].delete('department_id')
            change_request.requested_changes[0].merge!('department_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Department can't be blank", "Department must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_title)' do
          it 'Should not update employee_title without an id' do
            change_request = Fabricate(:employee_title_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => EmployeeTitle.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update employee_title without employee' do
            change_request = Fabricate(:employee_title_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update employee_title without department' do
            change_request = Fabricate(:employee_title_update)
            change_request.requested_changes[0].delete('department_id')
            change_request.requested_changes[0].merge!('department_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Department can't be blank", "Department must exist"])
          end

          it 'Should not update employee_title without section' do
            change_request = Fabricate(:employee_title_update)
            change_request.requested_changes[0].delete('section_id')
            change_request.requested_changes[0].merge!('section_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Section can't be blank", "Section must exist"])
          end

          it 'Should not update employee_title without title' do
            change_request = Fabricate(:employee_title_update)
            change_request.requested_changes[0].delete('title_id')
            change_request.requested_changes[0].merge!('title_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Title can't be blank", "Title must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - firearm_range_score)' do
          it 'Should not update firearm_range_score without an id' do
            change_request = Fabricate(:firearm_range_score_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => FirearmRangeScore.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update firearm_range_score without employee' do
            change_request = Fabricate(:firearm_range_score_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update firearm_range_score without test_date' do
            change_request = Fabricate(:firearm_range_score_update)
            change_request.requested_changes[0].delete('test_date')
            change_request.requested_changes[0].merge!('test_date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Test date can't be blank"])
          end

          it 'Should not update firearm_range_score without test_type' do
            change_request = Fabricate(:firearm_range_score_update)
            change_request.requested_changes[0].delete('test_type')
            change_request.requested_changes[0].merge!('test_type' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Test type can't be blank"])
          end

          it 'Should not update firearm_range_score without score' do
            change_request = Fabricate(:firearm_range_score_update)
            change_request.requested_changes[0].delete('score')
            change_request.requested_changes[0].merge!('score' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Score can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - leave)' do
          it 'Should not update leave without an id' do
            change_request = Fabricate(:leave_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => Leave.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update leave without employee' do
            change_request = Fabricate(:leave_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee must exist"])
          end

          it 'Should not update leave without started_at' do
            change_request = Fabricate(:leave_update)
            change_request.requested_changes[0].delete('started_at')
            change_request.requested_changes[0].merge!('started_at' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Started at can't be blank"])
          end

          it 'Should not update leave without leave_type' do
            change_request = Fabricate(:leave_update)
            change_request.requested_changes[0].delete('leave_type')
            change_request.requested_changes[0].merge!('leave_type' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Leave type can't be blank", "Leave type is not included in the list"])
          end
        end


        context 'Should not update change request(Invalid cases for request_type - life_insurance)' do
          it 'Should not update life_insurance without an id' do
            change_request = Fabricate(:life_insurance_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => LifeInsurance.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update life_insurance without employee' do
            change_request = Fabricate(:life_insurance_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update life_insurance without insurance_type' do
            change_request = Fabricate(:life_insurance_update)
            change_request.requested_changes[0].delete('insurance_type')
            change_request.requested_changes[0].merge!('insurance_type' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Insurance type can't be blank"])
          end

          it 'Should not update life_insurance without amount' do
            change_request = Fabricate(:life_insurance_update)
            change_request.requested_changes[0].delete('amount')
            change_request.requested_changes[0].merge!('amount' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Amount can't be blank"])
          end

          it 'Should not update life_insurance without start_date' do
            change_request = Fabricate(:life_insurance_update)
            change_request.requested_changes[0].delete('start_date')
            change_request.requested_changes[0].merge!('start_date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Start date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - lodi)' do
          it 'Should not update lodi without an id' do
            change_request = Fabricate(:lodi_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => Lodi.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Record not found"])
          end

          it 'Should not update lodi without employee' do
            change_request = Fabricate(:lodi_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not update lodi without incident_date' do
            change_request = Fabricate(:lodi_update)
            change_request.requested_changes[0].delete('incident_date')
            change_request.requested_changes[0].merge!('incident_date' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Incident date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - upload)' do
          it 'Should not update upload without an id' do
            change_request = Fabricate(:upload_update)
            change_request.requested_changes[0].delete('id')
            change_request.requested_changes[0].merge!('id' => Upload.last.id + 1)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "upload",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response[0]).to eq("Record not found")
          end

          it 'Should not update upload without employee' do
            change_request = Fabricate(:upload_update)
            change_request.requested_changes[0].delete('employee_id')
            change_request.requested_changes[0].merge!('employee_id' => "")
            patch :update, params: {status: "completed", id: change_request.id, request_type: "upload",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response[0]).to eq("Employee can't be blank")
            expect(error_response[1]).to eq("Employee must exist")
          end
        end
      end

      context 'Should not update a change request to new records - with invalid params' do
        context 'Should not update change request(Invalid cases for request_type - award)' do
          it 'Should not create award without employee' do
            change_request = Fabricate(:award_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create award without name' do
            change_request = Fabricate(:award_create)
            change_request.requested_changes[0].delete('name')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "award",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Name can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - beneficiary)' do
          it 'Should not create beneficiary without employee' do
            change_request = Fabricate(:beneficiary_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create beneficiary without name' do
            change_request = Fabricate(:beneficiary_create)
            change_request.requested_changes[0].delete('name')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Name can't be blank"])
          end

          it 'Should not create beneficiary without percentage' do
            change_request = Fabricate(:beneficiary_create)
            change_request.requested_changes[0].delete('percentage')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "beneficiary",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Percentage can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - benefit_disbursement)' do
          it 'Should not create benefit_disbursement without employee' do
            change_request = Fabricate(:benefit_disbursement_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create benefit_disbursement without amount' do
            change_request = Fabricate(:benefit_disbursement_create)
            change_request.requested_changes[0].delete('amount')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Amount can't be blank"])
          end

          it 'Should not create benefit_disbursement without employee_benefit' do
            change_request = Fabricate(:benefit_disbursement_create)
            change_request.requested_changes[0].delete('employee_benefit_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee benefit can't be blank", "Employee benefit must exist"])
          end

          it 'Should not create benefit_disbursement without payment_type' do
            change_request = Fabricate(:benefit_disbursement_create)
            change_request.requested_changes[0].delete('payment_type_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Payment type can't be blank"])
          end

          it 'Should not create benefit_disbursement without date' do
            change_request = Fabricate(:benefit_disbursement_create)
            change_request.requested_changes[0].delete('date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Date can't be blank"])
          end

          it 'Should not create benefit_disbursement without reference_number' do
            change_request = Fabricate(:benefit_disbursement_create)
            change_request.requested_changes[0].delete('reference_number')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Reference number can't be blank"])
          end

          it 'Should not create benefit_disbursement without year' do
            change_request = Fabricate(:benefit_disbursement_create)
            change_request.requested_changes[0].delete('year')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "benefit_disbursement",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Year can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - delegate_assignment)' do
          it 'Should not create delegate_assignment without employee' do
            change_request = Fabricate(:delegate_assignment_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create delegate_assignment without office' do
            change_request = Fabricate(:delegate_assignment_create)
            change_request.requested_changes[0].delete('office_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Office can't be blank", "Office must exist"])
          end

          it 'Should not create delegate_assignment without delegate_employee' do
            change_request = Fabricate(:delegate_assignment_create)
            change_request.requested_changes[0].delete('delegate_employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "delegate_assignment",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Delegate employee can't be blank", "Delegate employee must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - dependent)' do
          it 'Should not create dependent without employee' do
            change_request = Fabricate(:dependent_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create dependent without life_insurance' do
            change_request = Fabricate(:dependent_create)
            change_request.requested_changes[0].delete('life_insurance_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Life insurance can't be blank", "Life insurance must exist"])
          end

          it 'Should not create dependent without name' do
            change_request = Fabricate(:dependent_create)
            change_request.requested_changes[0].delete('name')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Name can't be blank"])
          end

          it 'Should not create dependent without relationship' do
            change_request = Fabricate(:dependent_create)
            change_request.requested_changes[0].delete('relationship')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Relationship can't be blank"])
          end

          it 'Should not create dependent without address' do
            change_request = Fabricate(:dependent_create)
            change_request.requested_changes[0].delete('address')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Address can't be blank"])
          end

          it 'Should not create dependent without amount' do
            change_request = Fabricate(:dependent_create)
            change_request.requested_changes[0].delete('amount')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Amount can't be blank"])
          end

          it 'Should not create dependent without date' do
            change_request = Fabricate(:dependent_create)
            change_request.requested_changes[0].delete('date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "dependent",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_benefit)' do
          it 'Should not create employee_benefit without employee' do
            change_request = Fabricate(:employee_benefit_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_benefit without benefit' do
            change_request = Fabricate(:employee_benefit_create)
            change_request.requested_changes[0].delete('benefit_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_benefit",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Benefit can't be blank", "Benefit must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_department)' do
          it 'Should not create employee_department without employee' do
            change_request = Fabricate(:employee_department_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_department without department' do
            change_request = Fabricate(:employee_department_create)
            change_request.requested_changes[0].delete('department_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_department",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Department can't be blank", "Department must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_discipline_setting)' do
          it 'Should not create employee_discipline_setting without employee' do
            change_request = Fabricate(:employee_discipline_setting_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_discipline_setting without discipline_setting' do
            change_request = Fabricate(:employee_discipline_setting_create)
            change_request.requested_changes[0].delete('discipline_setting_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Discipline setting can't be blank", "Discipline setting must exist"])
          end

          it 'Should not create employee_discipline_setting without discipline_charge' do
            change_request = Fabricate(:employee_discipline_setting_create)
            change_request.requested_changes[0].delete('discipline_charge_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_setting",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Discipline charge can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_discipline_step)' do
          it 'Should not create employee_discipline_step without employee_discipline_setting' do
            change_request = Fabricate(:employee_discipline_step_create)
            change_request.requested_changes[0].delete('employee_discipline_setting_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee discipline setting can't be blank", "Employee discipline setting must exist"])
          end

          it 'Should not create employee_discipline_step without step' do
            change_request = Fabricate(:employee_discipline_step_create)
            change_request.requested_changes[0].delete('step')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_discipline_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Step can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_employment_status)' do
          it 'Should not create employee_employment_status without employee' do
            change_request = Fabricate(:employee_employment_status_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_employment_status without employment_status' do
            change_request = Fabricate(:employee_employment_status_create)
            change_request.requested_changes[0].delete('employment_status_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employment status can't be blank", "Employment status must exist"])
          end

          it 'Should not create employee_employment_status without start_date' do
            change_request = Fabricate(:employee_employment_status_create)
            change_request.requested_changes[0].delete('start_date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_employment_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Start date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_firearm_status)' do
          it 'Should not create employee_firearm_status without employee' do
            change_request = Fabricate(:employee_firearm_status_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_firearm_status without firearm_status' do
            change_request = Fabricate(:employee_firearm_status_create)
            change_request.requested_changes[0].delete('firearm_status_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_firearm_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Firearm status can't be blank", "Firearm status must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_grievance)' do
          it 'Should not create employee_grievance without employee' do
            change_request = Fabricate(:employee_grievance_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_grievance without grievance' do
            change_request = Fabricate(:employee_grievance_create)
            change_request.requested_changes[0].delete('grievance_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Grievance can't be blank", "Grievance must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_grievance_step)' do
          it 'Should not create employee_grievance without employee_grievance' do
            change_request = Fabricate(:employee_grievance_step_create)
            change_request.requested_changes[0].delete('employee_grievance_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee grievance can't be blank", "Employee grievance must exist"])
          end

          it 'Should not create employee_grievance without step' do
            change_request = Fabricate(:employee_grievance_step_create)
            change_request.requested_changes[0].delete('step')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_grievance_step",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Step can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_meeting_type)' do
          it 'Should not create employee_meeting_type without employee' do
            change_request = Fabricate(:employee_meeting_type_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_meeting_type without meeting_type' do
            change_request = Fabricate(:employee_meeting_type_create)
            change_request.requested_changes[0].delete('meeting_type_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Meeting type can't be blank", "Meeting type must exist"])
          end

          it 'Should not create employee_meeting_type without meeting_date' do
            change_request = Fabricate(:employee_meeting_type_create)
            change_request.requested_changes[0].delete('meeting_date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_meeting_type",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Meeting date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_officer_status)' do
          it 'Should not create employee_officer_status without employee' do
            change_request = Fabricate(:employee_officer_status_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_officer_status without officer_status' do
            change_request = Fabricate(:employee_officer_status_create)
            change_request.requested_changes[0].delete('officer_status_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_officer_status",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Officer status can't be blank", "Officer status must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_office)' do
          it 'Should not create employee_office without employee' do
            change_request = Fabricate(:employee_office_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_office without office' do
            change_request = Fabricate(:employee_office_create)
            change_request.requested_changes[0].delete('office_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_office",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Office must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_pacf)' do
          it 'Should not create employee_pacf without employee' do
            change_request = Fabricate(:employee_pacf_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_pacf without pacf' do
            change_request = Fabricate(:employee_pacf_create)
            change_request.requested_changes[0].delete('pacf_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_pacf",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Pacf can't be blank", "Pacf must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_position)' do
          it 'Should not create employee_position without employee' do
            change_request = Fabricate(:employee_position_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_position without position' do
            change_request = Fabricate(:employee_position_create)
            change_request.requested_changes[0].delete('position_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Position can't be blank", "Position must exist"])
          end

          it 'Should not create employee_position without delegate_series' do
            change_request = Fabricate(:employee_position_create)
            change_request.requested_changes[0].delete('delegate_series_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Delegate series can't be blank"])
          end

          it 'Should not create employee_position without start_date' do
            change_request = Fabricate(:employee_position_create)
            change_request.requested_changes[0].delete('start_date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_position",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Start date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_rank)' do
          it 'Should not create employee_rank without employee' do
            change_request = Fabricate(:employee_rank_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_rank without rank' do
            change_request = Fabricate(:employee_rank_create)
            change_request.requested_changes[0].delete('rank_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Rank can't be blank", "Rank must exist"])
          end

          it 'Should not create employee_rank without start_date' do
            change_request = Fabricate(:employee_rank_create)
            change_request.requested_changes[0].delete('start_date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_rank",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Start date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_section)' do
          it 'Should not create employee_section without employee' do
            change_request = Fabricate(:employee_section_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_section without section' do
            change_request = Fabricate(:employee_section_create)
            change_request.requested_changes[0].delete('section_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Section can't be blank", "Section must exist"])
          end

          it 'Should not create employee_section without department' do
            change_request = Fabricate(:employee_section_create)
            change_request.requested_changes[0].delete('department_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_section",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Department can't be blank", "Department must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - employee_title)' do
          it 'Should not create employee_title without employee' do
            change_request = Fabricate(:employee_title_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create employee_title without department' do
            change_request = Fabricate(:employee_title_create)
            change_request.requested_changes[0].delete('department_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Department can't be blank", "Department must exist"])
          end

          it 'Should not create employee_title without section' do
            change_request = Fabricate(:employee_title_create)
            change_request.requested_changes[0].delete('section_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Section can't be blank", "Section must exist"])
          end

          it 'Should not create employee_title without title' do
            change_request = Fabricate(:employee_title_create)
            change_request.requested_changes[0].delete('title_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "employee_title",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Title can't be blank", "Title must exist"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - firearm_range_score)' do
          it 'Should not create firearm_range_score without employee' do
            change_request = Fabricate(:firearm_range_score_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create firearm_range_score without test_date' do
            change_request = Fabricate(:firearm_range_score_create)
            change_request.requested_changes[0].delete('test_date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Test date can't be blank"])
          end

          it 'Should not create firearm_range_score without test_type' do
            change_request = Fabricate(:firearm_range_score_create)
            change_request.requested_changes[0].delete('test_type')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Test type can't be blank"])
          end

          it 'Should not create firearm_range_score without score' do
            change_request = Fabricate(:firearm_range_score_create)
            change_request.requested_changes[0].delete('score')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "firearm_range_score",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Score can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - leave)' do
          it 'Should not create leave without employee' do
            change_request = Fabricate(:leave_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee must exist"])
          end

          it 'Should not create leave without started_at' do
            change_request = Fabricate(:leave_create)
            change_request.requested_changes[0].delete('started_at')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Started at can't be blank"])
          end

          it 'Should not create leave without leave_type' do
            change_request = Fabricate(:leave_create)
            change_request.requested_changes[0].delete('leave_type')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "leave",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Leave type can't be blank", "Leave type is not included in the list"])
          end
        end


        context 'Should not update change request(Invalid cases for request_type - life_insurance)' do
          it 'Should not create life_insurance without employee' do
            change_request = Fabricate(:life_insurance_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create life_insurance without insurance_type' do
            change_request = Fabricate(:life_insurance_create)
            change_request.requested_changes[0].delete('insurance_type')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Insurance type can't be blank"])
          end

          it 'Should not create life_insurance without amount' do
            change_request = Fabricate(:life_insurance_create)
            change_request.requested_changes[0].delete('amount')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Amount can't be blank"])
          end

          it 'Should not create life_insurance without start_date' do
            change_request = Fabricate(:life_insurance_create)
            change_request.requested_changes[0].delete('start_date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "life_insurance",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Start date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - lodi)' do
          it 'Should not create lodi without employee' do
            change_request = Fabricate(:lodi_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Employee can't be blank", "Employee must exist"])
          end

          it 'Should not create lodi without incident_date' do
            change_request = Fabricate(:lodi_create)
            change_request.requested_changes[0].delete('incident_date')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "lodi",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["Incident date can't be blank"])
          end
        end

        context 'Should not update change request(Invalid cases for request_type - upload)' do
          it 'Should not create upload without employee' do
            change_request = Fabricate(:upload_create)
            change_request.requested_changes[0].delete('employee_id')
            patch :update, params: {status: "completed", id: change_request.id, request_type: "upload",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response[0]).to eq("Employee can't be blank")
            expect(error_response[1]).to eq("Employee must exist")
          end

          it 'Should not create upload without files' do
            change_request = Fabricate(:upload_create)
            patch :update, params: {status: "completed", id: change_request.id, request_type: "upload",
                                    requested_changes: change_request.requested_changes[0], format: :json}
            expect(error_response).to eq(["File attachment is missing"])
          end
        end
      end
    end
  end
end
