# frozen_string_literal: true

RSpec.describe Api::Employees::DelegateAssignmentsController, type: :controller do
  let(:employee) { Fabricate(:employee) }
  let(:delegate_assignment) { Fabricate(:delegate_assignment, delegate_employee: employee) }
  let(:params) { Fabricate.attributes_for(:delegate_assignment) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { rights: 'employee', object_name: 'delegate_assignment',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :delegate_assignment, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Display list of delegate assignment' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of delegate assignment page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Display list of delegate assignment' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of delegate assignment page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { rights: 'employee', object_name: 'delegate_assignment' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create delegate assignment with valid params' do
        expect do
          post :create, params: { delegate_assignment: params.merge(delegate_employee_id: employee.id), format: :json }
        end.to change(DelegateAssignment, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq(params['notes'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create delegate assignment with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        post :create, params: { delegate_assignment: params.merge(delegate_employee_id: employee.id, employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create delegate assignment with blank office' do
        validation_columns = ['office_id']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        post :create, params: { delegate_assignment: params.merge(delegate_employee_id: employee.id, office_id: ''), format: :json }
        expect(error_response[0]).to include({ office: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create delegate assignment with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        post :create, params: { delegate_assignment: params.merge(start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create delegate assignment with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        post :create, params: { delegate_assignment: params.merge(end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create delegate assignment with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        post :create, params: { delegate_assignment: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create delegate assignment with blank delegate_name' do
        validation_columns = ['delegate_name']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        post :create, params: { delegate_assignment: params.merge(delegate_name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check delegate assignment validation if required fields present' do
        validation_columns = %w[notes start_date end_date]
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        post :create, params: { delegate_assignment: params.merge(notes: '', start_date: '', end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check delegate assignment validation if required fields not present' do
        modify_account_schema_validations(model: 'delegate_assignments', action: 'clear')
        expect do
          post :create, params: { delegate_assignment: params.merge(delegate_name: '', notes: '', start_date: '', end_date: ''), format: :json }
        end.to change(DelegateAssignment, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq('')
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create delegate assignment with invalid date range' do
        post :create, params: { delegate_assignment: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create delegate assignment' do
        post :create, params: { delegate_assignment: params.merge(delegate_employee_id: employee.id), format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { rights: 'employee', object_name: 'delegate_assignment' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update delegate assignment with valid params' do
        delegate_assignment1 = Fabricate(:delegate_assignment)
        patch :update, params: { id: delegate_assignment1.id, delegate_assignment: params, format: :json }
        expect(parsed_response['attributes']['delegate_name']).to eq(params['delegate_name'])
        expect(parsed_response['attributes']['notes']).to eq(params['notes'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update delegate assignment with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: { delegate_employee_id: employee.id, employee_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update delegate assignment with blank office' do
        validation_columns = ['office_id']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: { delegate_employee_id: employee.id, office_id: '' }, format: :json }
        expect(error_response[0]).to include({ office: ['must exist'] }.as_json)

        blank_error_responses(validation_columns)
      end

      it 'Should not update delegate assignment with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: { start_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update delegate assignment with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: { end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update delegate assignment with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: { notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update delegate assignment with blank delegate_name' do
        validation_columns = ['delegate_name']
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: { delegate_name: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check delegate assignment validation if required fields present' do
        validation_columns = %w[notes start_date end_date]
        modify_account_schema_validations(model: 'delegate_assignments', fields: validation_columns)
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: { start_date: '', notes: '', end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check delegate assignment validation if required fields not present' do
        modify_account_schema_validations(model: 'delegate_assignments', action: 'clear')
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: { delegate_name: '', notes: '', start_date: '', end_date: '' }, format: :json }
        expect(parsed_response['attributes']['notes']).to eq('')
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update delegate assignment with invalid date range' do
        patch :update, params: { id: delegate_assignment.id, delegate_assignment: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update delegate assignment' do
        delegate_assignment1 = Fabricate(:delegate_assignment)
        patch :update, params: { id: delegate_assignment1.id, delegate_assignment: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { rights: 'employee', object_name: 'delegate_assignment' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete delegate assignment with valid params' do
        delete :destroy, params: { id: delegate_assignment.id, format: :json }
        expect(assigns(:delegate_assignment).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete delegate assignment with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete delegate assignment' do
        delete :destroy, params: { id: delegate_assignment.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
