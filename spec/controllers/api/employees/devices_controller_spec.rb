# frozen_string_literal: true

RSpec.describe Api::Employees::<PERSON>cesController, type: :controller do
  let(:employee) { Fabricate(:employee) }
  let(:device) { Fabricate(:device) }
  let(:params) { Fabricate.attributes_for(:device) }

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: {object_name: 'device'}

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not create device with blank device token' do
        post :create, params: {device: params.merge(device_token: ''), format: :json}
        expect(response).to have_http_status(422)
        expect(error_response).to eq(["Device token can't be blank"])
      end

      it 'Should not create device with blank os_type' do
        post :create, params: {device: params.merge(os_type: ''), format: :json}
        expect(response).to have_http_status(422)
        expect(error_response).to eq(["OS type can't be blank"])
      end

      it 'Should not create device with invalid os_type' do
        post :create, params: {device: params.merge(os_type: 'test'), format: :json}
        expect(response).to have_http_status(422)
        expect(error_response).to eq(["OS type is invalid"])
      end

      it 'Should not create device with blank os_type' do
        post :create, params: {device: params.merge(os_version: ''), format: :json}
        expect(response).to have_http_status(422)
        expect(error_response).to eq(["OS version can't be blank"])
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Should not create device with blank device token' do
        post :create, params: {device: params.merge(device_token: ''), format: :json}
        expect(response).to have_http_status(422)
        expect(error_response).to eq(["Device token can't be blank"])
      end

      it 'Should not create device with blank os_type' do
        post :create, params: {device: params.merge(os_type: ''), format: :json}
        expect(response).to have_http_status(422)
        expect(error_response).to eq(["OS type can't be blank"])
      end

      it 'Should not create device with invalid os_type' do
        post :create, params: {device: params.merge(os_type: 'test'), format: :json}
        expect(response).to have_http_status(422)
        expect(error_response).to eq(["OS type is invalid"])
      end

      it 'Should not create device with blank os_type' do
        post :create, params: {device: params.merge(os_version: ''), format: :json}
        expect(response).to have_http_status(422)
        expect(error_response).to eq(["OS version can't be blank"])
      end
    end
  end
end
