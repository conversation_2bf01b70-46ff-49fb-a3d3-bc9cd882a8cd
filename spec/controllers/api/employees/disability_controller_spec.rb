# frozen_string_literal: true

RSpec.describe Api::Employees::DisabilitiesController do
  let(:employee) { Fabricate(:employee) }
  let(:disability) { Fabricate(:disability) }
  let(:params) { Fabricate.attributes_for(:disability) }

  describe 'GET #index' do

    before(:each) do
      Fabricate.times(30, :disability, employee: employee)
    end

    context 'Valid cases - authenticated User', authenticated: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Respond disabilities list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of disabilities page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end
    end
  end

  describe 'POST #create' do

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create disability with valid params' do
        expect { post :create, params: { disability: params, format: :json } }.to change(Disability, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end
  end

  describe 'PATCH #update' do
    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update disability with valid params' do
        disability1 = Fabricate(:disability)
        patch :update, params: { id: disability1.id, disability: params, format: :json }
        expect(parsed_response['attributes']['from_date']).to eq(params[:from_date].to_s)
        success_response
      end
    end
  end

  describe 'DELETE #destroy' do
    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete disability with valid params' do
        delete :destroy, params: { id: disability.id, format: :json }
        expect(assigns(:disability).discarded_at).not_to eq(nil)
        success_response
      end
    end
    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete beneficiary with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end
  end
end