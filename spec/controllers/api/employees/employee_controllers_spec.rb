# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeesController do
  let(:employee) { Fabricate(:employee) }
  let(:params) { Fabricate.attributes_for(:employee) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'employee' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :employee)
      end

      it 'Respond benefits list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'SaaS compliance(search filter based on SaaS account JSON)', authenticated: true do
      before(:each) do
        Fabricate.times(5, :employee_search)
      end

      it 'Display employee by search first name' do
        employee_search_schema(key: ['first_name'])
        get :index, params: { search_text: employee.first_name }
        expect(parsed_response.count).to eq(1)
        success_response
      end

      it 'Display employee by search middle name' do
        employee_search_schema(key: ['middle_name'])
        get :index, params: { search_text: employee.middle_name }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search last name' do
        employee_search_schema(key: ['last_name'])
        get :index, params: { search_text: employee.last_name }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search shield number' do
        employee_search_schema(key: ['shield_number'])
        get :index, params: { search_text: employee.shield_number }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search placard number' do
        employee_search_schema(key: ['placard_number'])
        get :index, params: { search_text: employee.placard_number }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search street' do
        employee_search_schema(key: ['street'])
        get :index, params: { search_text: employee.street }
        expect(parsed_response.count).to eq(1)
      end

      it 'displays staff members only' do
        get :index, params: { staff_member_only: true }
        expect(response).to have_http_status(:success)
      end

      it 'Display employee by search start date' do
        employee_search_schema(key: ['start_date'])
        get :index, params: { search_text: employee.start_date }
        expect(parsed_response.count).to eq(6)
      end

      it 'Display employee by search birthday' do
        employee_search_schema(key: ['birthday'])
        get :index, params: { search_text: employee.birthday }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search employment statuses' do
        employment_status = Fabricate(:employee_employment_status, employee_id: employee.id)
        get :index, params: { search_text: employment_status.name }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search officer statuses' do
        officer_status = Fabricate(:officer_status,  name: "test_name")
        Fabricate(:employee_officer_status, employee_id: employee.id, officer_status: officer_status)
        officer_status = Fabricate(:employee_officer_status, employee_id: employee.id)
        get :index, params: { search_text: officer_status.name }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search employee title' do
        title = Fabricate(:employee_title, employee_id: employee.id)
        get :index, params: { search_text: title.name }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search contacts email' do
        Fabricate(:contact, employee_id: employee.id, value: '<EMAIL>')
        contacts = Fabricate(:contact, employee_id: employee.id)
        get :index, params: { search_text: contacts.value }
        expect(parsed_response.count).to eq(1)
      end

      it 'Display employee by search contacts phone' do
        contacts = Fabricate(:contact_phone, employee_id: employee.id)
        get :index, params: { search_text: contacts.value }
        expect(parsed_response.count).to eq(7)
      end

      it 'Display employee by search prefix' do
        employee_search_schema(key: ['first_name'])
        get :index, params: { search_text: 'xyz' }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Display employee when the searching against specified columns of employee' do
        employee_search_schema(key: %w[shield_number first_name middle_name last_name birthday street start_date placard_number])
        get :index, params: { search_text: employee.first_name }
        expect(parsed_response.count).to eq(1)
      end

      it 'When search fields specified in account JSON is empty' do
        employee_search_schema(action: 'clear')
        get :index, params: { search_text: 'asd' }
        expect(parsed_response.count).to eq(0)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'employee' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employees with valid params' do
        expect { post :create, params: { employee: params, format: :json } }.to change(Employee, :count).by(1)
        expect(parsed_response['attributes']['city']).to eq(params['city'])
        success_response
      end

      it 'Should create employees with autoload a_number' do
        expect { post :create, params: { employee: params.merge(a_number: ''), format: :json } }.to change(Employee, :count).by(1)
        expect(parsed_response['attributes']['a_number']).to eq('BTO000001')
        success_response
      end

      it 'Should create employees with autoload a_number(add a_number with exiting record value)' do
        employee = Fabricate(:employee, a_number: '')
        expect { post :create, params: { employee: params.merge(a_number: ''), format: :json } }.to change(Employee, :count).by(1)
        expect(employee.a_number).to eq('BTO000001')
        expect(parsed_response['attributes']['a_number']).to eq('BTO000002')
        success_response
      end

      it 'Should create employees with autoload paroll_id' do
        expect { post :create, params: { employee: params.merge(payroll_id: ''), format: :json } }.to change(Employee, :count).by(1)
        expect(parsed_response['attributes']['payroll_id']).to eq('BTO000001')
        success_response
      end

      it 'Should create employees with autoload payroll_id(add payroll_id with exiting record value)' do
        employee = Fabricate(:employee, payroll_id: '')
        expect { post :create, params: { employee: params.merge(payroll_id: ''), format: :json } }.to change(Employee, :count).by(1)
        expect(employee.payroll_id).to eq('BTO000001')
        expect(parsed_response['attributes']['payroll_id']).to eq('BTO000002')
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee with invalid contact email value' do
        employee = Fabricate.to_params(:employee1)
        post :create, params: { employee: employee, format: :json }
        expect(error_response[0]).to include({ 'contacts.value' => [': Email format is invalid'] }.as_json)
      end

      it 'Should not create employee with blank contact phone value' do
        employee = Fabricate.to_params(:employee2)
        post :create, params: { employee: employee, format: :json }
        expect(error_response[0]).to include({ 'contacts.value' => [': Phone number format is invalid'] }.as_json)
      end

      it 'Should not create employee with blank birthday' do
        validation_columns = ['birthday']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(birthday: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank city' do
        validation_columns = ['city']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(city: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank first name' do
        validation_columns = ['first_name']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(first_name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank last name' do
        validation_columns = ['last_name']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(last_name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank marital status' do
        validation_columns = ['marital_status_id']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(marital_status_id: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank social security number' do
        validation_columns = ['social_security_number']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(social_security_number: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank state' do
        validation_columns = ['state']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(state: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank street' do
        validation_columns = ['street']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(street: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank zipcode' do
        validation_columns = ['zipcode']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(zipcode: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with blank shield number' do
        validation_columns = ['shield_number']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(shield_number: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee invalid blank username' do
        validation_columns = ['username']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(username: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee with invalid password confirmation' do
        validation_columns = ['password_confirmation']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(password_confirmation: ''), format: :json }
        expect(error_response).to eq([{ 'password_confirmation' => ["doesn't match Password", "can't be blank"] }])
      end

      it 'Should check employee validation if required fields present' do
        validation_columns = %w[shield_number zipcode city street state last_name first_name
                                placard_number a_number start_date title_code member_since prom_prov prom_perm birthday username]
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        post :create, params: { employee: params.merge(shield_number: '', zipcode: '', city: '', state: '', street: '',
                                                       placard_number: '', title_code: '', member_since: '', start_date: '', prom_perm: '', prom_prov: '', birthday: '',
                                                       a_number: '', last_name: '', first_name: '', username: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee validation if required fields not present' do
        modify_account_schema_validations(model: 'employees', action: 'clear')
        expect do
          post :create, params: { employee: params.merge(shield_number: '', zipcode: '', city: '', state: '', street: '',
                                                         placard_number: '', title_code: '', member_since: '', start_date: '', prom_perm: '', prom_prov: '', birthday: '',
                                                         a_number: '', last_name: '', first_name: ''), format: :json }
        end.to change(Employee, :count).by(1)
        expect(parsed_response['attributes']['state']).to eq('')
        expect(parsed_response['attributes']['city']).to eq('')
        expect(parsed_response['attributes']['street']).to eq('')
        expect(parsed_response['attributes']['zipcode']).to eq('')
        expect(parsed_response['attributes']['birthday']).to eq(nil)
        expect(parsed_response['attributes']['shield_number']).to eq('')
        expect(parsed_response['attributes']['first_name']).to eq('')
        expect(parsed_response['attributes']['last_name']).to eq('')
        expect(parsed_response['attributes']['placard_number']).to eq('')
        expect(parsed_response['attributes']['title_code']).to eq('')
        expect(parsed_response['attributes']['prom_prov']).to eq(nil)
      end
    end

    context 'Invalid cases - static validations(validations defined in model itself)', authenticated: true do
      it 'Should not create employee with zipcode number more than 5' do
        post :create, params: { employee: params.merge(zipcode: '123456'), format: :json }
        expect(error_response).to eq([{ 'zipcode' => ['is too long (maximum is 5 characters)'] }])
        unprocessable_entity
      end

      it 'Should not create user with same username' do
        employee
        post :create, params: { employee: params.merge(username: employee.username), format: :json }
        expect(error_response).to eq([{ 'username' => ['has already been taken'] }])
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee' do
        post :create, params: { employee: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'employee' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employees with valid params' do
        employee1 = Fabricate(:employee)
        patch :update, params: { id: employee1.id, employee: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        expect(parsed_response['attributes']['social_security_number']).to eq(params[:social_security_number])
        expect(parsed_response['attributes']['first_name']).to eq(params[:first_name])
        expect(parsed_response['attributes']['middle_name']).to eq(params[:middle_name])
        expect(parsed_response['attributes']['last_name']).to eq(params[:last_name])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee with blank birthday' do
        validation_columns = ['birthday']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { birthday: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank city' do
        validation_columns = ['city']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { city: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank first name' do
        validation_columns = ['first_name']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { first_name: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank last name' do
        validation_columns = ['last_name']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { last_name: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank marital status' do
        validation_columns = ['marital_status_id']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { marital_status_id: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank social security number' do
        validation_columns = ['social_security_number']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { social_security_number: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank state' do
        validation_columns = ['state']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { state: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank street' do
        validation_columns = ['street']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { street: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank zipcode' do
        validation_columns = ['zipcode']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { zipcode: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with blank shield number' do
        validation_columns = ['shield_number']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { shield_number: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee invalid blank username' do
        validation_columns = ['username']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { username: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee with invalid password confirmation' do
        validation_columns = ['password_confirmation']
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: { password_confirmation: '' }, format: :json }
        expect(error_response).to eq([{"password_confirmation" => ["doesn't match Password"]}])
      end

      it 'Should return 200 - Should not check employee validation if required fields present' do
        validation_columns = %w[shield_number zipcode city street state last_name first_name
                                placard_number a_number start_date title_code member_since prom_prov prom_perm birthday username]
        modify_account_schema_validations(model: 'employees', fields: validation_columns)
        patch :update, params: { id: employee.id, employee: params.merge(shield_number: '', zipcode: '', city: '', state: '', street: '',
                                                                         placard_number: '', title_code: '', member_since: '', start_date: '', prom_perm: '', prom_prov: '', birthday: '',
                                                                         a_number: '', last_name: '', first_name: '', username: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee validation if required fields present' do
        modify_account_schema_validations(model: 'employees', action: 'clear')
        patch :update, params: { id: employee.id, employee: params.merge(shield_number: '', zipcode: '', city: '', state: '', street: '',
                                                                         placard_number: '', title_code: '', member_since: '', start_date: '', prom_perm: '', prom_prov: '', birthday: '',
                                                                         a_number: '', last_name: '', first_name: ''), format: :json }
        expect(parsed_response['attributes']['state']).to eq('')
        expect(parsed_response['attributes']['city']).to eq('')
        expect(parsed_response['attributes']['street']).to eq('')
        expect(parsed_response['attributes']['zipcode']).to eq('')
        expect(parsed_response['attributes']['birthday']).to eq(nil)
        expect(parsed_response['attributes']['shield_number']).to eq('')
        expect(parsed_response['attributes']['first_name']).to eq('')
        expect(parsed_response['attributes']['last_name']).to eq('')
        expect(parsed_response['attributes']['a_number']).to eq('')
        expect(parsed_response['attributes']['placard_number']).to eq('')
        expect(parsed_response['attributes']['title_code']).to eq('')
        expect(parsed_response['attributes']['prom_prov']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update employee with zipcode number more than 5' do
        patch :update, params: { id: employee.id, employee: { zipcode: '123456' }, format: :json }
        expect(error_response).to eq([{ 'zipcode' => ['is too long (maximum is 5 characters)'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee' do
        employee1 = Fabricate(:employee)
        patch :update, params: { id: employee1.id, employee: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'employee' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employees with valid params' do
        delete :destroy, params: { id: employee.id, format: :json }
        expect(assigns(:employee).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should update user_name to nil' do
        employee1 = Fabricate(:employee, username: "<EMAIL>")
        delete :destroy, params: { id: employee1.id, format: :json }
        expect(assigns(:employee).username).to eq(nil)
        success_response
      end

      it 'Should delete employees and its related association' do
        Fabricate(:award, employee: employee)
        Fabricate(:benefit_coverage, employee: employee)
        Fabricate(:benefit_disbursement, employee: employee)
        Fabricate(:contact, employee: employee)
        Fabricate(:delegate_assignment, employee: employee)
        Fabricate(:leave, employee: employee)
        Fabricate(:lodi, employee: employee)
        Fabricate(:firearm_range_score, employee: employee)
        Fabricate(:employee_benefit, employee: employee)
        Fabricate(:employee_employment_status, employee: employee)
        Fabricate(:employee_firearm_status, employee: employee)
        Fabricate(:employee_meeting_type, employee: employee)
        Fabricate(:employee_officer_status, employee: employee)
        Fabricate(:employee_position, employee: employee)
        Fabricate(:employee_rank, employee: employee)
        delete :destroy, params: { id: employee.id, format: :json }
        expect(assigns(:employee).discarded_at).not_to eq(nil)
        expect(employee.awards.first.discarded_at).not_to eq(nil)
        expect(employee.benefit_coverages.first).to eq(nil)
        expect(employee.benefit_disbursements.first).to eq(nil)
        expect(employee.leaves.first).to eq(nil)
        expect(employee.firearm_range_scores.first).to eq(nil)
        expect(employee.lodis.first).to eq(nil)
        expect(employee.employee_benefits.first).to eq(nil)
        expect(employee.employee_firearm_statuses.first).to eq(nil)
        expect(employee.employee_meeting_types.first).to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employees with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee' do
        delete :destroy, params: { id: employee.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'GET #delegate_employees' do
    context 'Valid params - authenticated user', authenticated: true do
      it 'Should display delegate employee with valid params' do
        Fabricate(:employee_position, employee_id: employee.id, position: Fabricate(:position, name: 'Delegate'), end_date: nil)
        get :delegate_employees, format: :json
        expect(parsed_response.count).to eq(1)
      end
    end

    context 'Valid params - authenticated employee', authenticated_employee: true do
      it 'Should display delegate employee with valid params' do
        Fabricate(:employee_position, employee_id: employee.id, position: Fabricate(:position, name: 'Delegate'), end_date: nil)
        get :delegate_employees, format: :json
        expect(parsed_response.count).to eq(1)
      end
    end

    context 'Invalid params', authenticated: true do
      it 'Should display delegate employee without duplicate values' do
        Fabricate.times(4, :employee_position, employee: Fabricate(:employee),
                                               position: Fabricate(:position, name: 'Delegate'), end_date: nil)
        get :delegate_employees, format: :json
        expect(parsed_response.count).to eq(1)
      end
    end
  end

  describe 'POST #title_code_update' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'title_code_update'

    context 'Valid cases', authenticated: true do
      it 'Should update employee with valid title_code' do
        Fabricate(:title, title_code: 'a123')
        post :title_code_update, params: { id: employee.id, title_code: 'a123', format: :json }
        expect(Employee.first.title_code).to eq('a123')
        expect(EmployeeTitle.count).to eq(1)
        expect(EmployeeDepartment.count).to eq(1)
        expect(EmployeeSection.count).to eq(1)
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update employee with invalid title_code' do
        Fabricate(:title, title_code: 'a123')
        post :title_code_update, params: { id: employee.id, title_code: 'a12345', format: :json }
        expect(error_response).to eq(['Title is not found'])
      end

      it 'Should not update employee with invalid title_code' do
        Fabricate(:title, title_code: 'a123')
        post :title_code_update, params: { id: employee.id, title_code: '', format: :json }
        expect(error_response).to eq(['Title is not found'])
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee with valid title_code' do
        Fabricate(:title, title_code: 'a123')
        post :title_code_update, params: { id: employee.id, title_code: 'a123', format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'GET #profile' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'profile', params: { id: 1 }

    context 'Valid params', authenticated_employee: true do
      it 'should display profile for valid params' do
       get :profile, format: :json
        success_response
      end
    end
  end

  describe 'POST #login_credentials' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'login_credentials'

    context 'Valid cases', authenticated: true do
      it 'should send login credential mail with valid params' do
        employee.contacts.create(contact_type: 'email', contact_for: 'personal', value: '<EMAIL>')
        expect { post :login_credentials, params: {employee_id: employee.id, format: :json} }.to change(ActiveJob::Base.queue_adapter.enqueued_jobs, :size).by(1)
        expect(JSON.parse(response.body)['message']).to eq("Mail has been sent successfully")
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'should not send login credential mail with invalid email' do
        post :login_credentials, params: {employee_id: employee.id, format: :json}
        expect(error_response).to eq(["Member’s Personal Email address is missing"])
      end

      it 'should not send login credential mail with invalid employee id' do
        post :login_credentials, params: {employee_id: '', format: :json}
        expect(error_response).to eq(["Employee id is missing or invalid"])
      end
    end
  end

  describe 'GET #staff_member_employee' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'staff_member_employee'

    context 'Valid params', authenticated: true do
      before(:each) do
        Fabricate.times(5, :employee)
      end

      it 'should display profile for valid params' do
        p Employee.all
        get :staff_member_employee, format: :json
        success_response
      end
    end
  end

  describe 'GET #exact_search' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'exact_search'

    context 'Valid params', authenticated: true do
      it 'should filter exact search results' do
        employee1 = Fabricate(:employee, first_name: 'test', last_name: 'employee1')
        employee2 = Fabricate(:employee, first_name: 'test', last_name: 'employee2')
        get :exact_search, params: { search_text: 'employee2', format: :json }

        expect(parsed_response.count).to eq(1)
        expect(parsed_response[0]['id'].to_i).not_to eq(employee1.id)
        expect(parsed_response[0]['id'].to_i).to eq(employee2.id)
        success_response
      end
    end
  end
end

