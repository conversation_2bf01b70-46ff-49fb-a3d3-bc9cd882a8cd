# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeDisciplineSettingsController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_discipline_setting) { Fabricate(:employee_discipline_setting) }
  let(:params) { Fabricate.attributes_for(:employee_discipline_setting) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'employee_discipline_setting',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_discipline_setting, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee discipline_setting page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee discipline_setting page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'employee_discipline_setting' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee discipline_setting with valid params' do
        expect { post :create, params: { employee_discipline_setting: params, format: :json } }.to change(EmployeeDisciplineSetting, :count).by(1)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee discipline_setting with blank discipline_setting' do
        validation_columns = ['discipline_setting_id']
        modify_account_schema_validations(model: 'employee_discipline_settings', fields: validation_columns)
        post :create, params: { employee_discipline_setting: params.merge(discipline_setting_id: ''), format: :json }
        expect(error_response[0]).to include({ discipline_setting: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee discipline_setting with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_discipline_settings', fields: validation_columns)
        post :create, params: { employee_discipline_setting: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee discipline_setting with blank date' do
        validation_columns = ['date']
        modify_account_schema_validations(model: 'employee_discipline_settings', fields: validation_columns)
        post :create, params: { employee_discipline_setting: params.merge(date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee discipline_setting validation if required fields present' do
        validation_columns = %w[date employee_id]
        modify_account_schema_validations(model: 'employee_discipline_settings', fields: validation_columns)
        post :create, params: { employee_discipline_setting: params.merge(employee_id: '', date: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee discipline_setting validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_discipline_settings', action: 'clear')
        expect do
          post :create, params: { employee_discipline_setting: params.merge(date: ''), format: :json }
        end.to change(EmployeeDisciplineSetting, :count).by(1)
        expect(parsed_response['attributes']['date']).to eq(nil)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee discipline_setting' do
        post :create, params: { employee_discipline_setting: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'employee_discipline_setting' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee discipline_setting with valid params' do
        employee_discipline_setting1 = Fabricate(:employee_discipline_setting)
        patch :update, params: { id: employee_discipline_setting1.id, employee_discipline_setting: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee discipline_setting with blank discipline_setting' do
        validation_columns = ['discipline_setting_id']
        modify_account_schema_validations(model: 'employee_discipline_settings', fields: validation_columns)
        patch :update, params: { employee_discipline_setting: { discipline_setting_id: '' }, id: employee_discipline_setting.id, format: :json }
        expect(error_response[0]).to include({ discipline_setting: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee discipline_setting with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_discipline_settings', fields: validation_columns)
        patch :update, params: { employee_discipline_setting: { employee_id: '' }, id: employee_discipline_setting.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee discipline_setting with blank date' do
        validation_columns = ['date']
        modify_account_schema_validations(model: 'employee_discipline_settings', fields: validation_columns)
        patch :update, params: { id: employee_discipline_setting.id, employee_discipline_setting: { date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee discipline_setting validation if required fields present' do
        validation_columns = %w[date employee_id]
        modify_account_schema_validations(model: 'employee_discipline_settings', fields: validation_columns)
        patch :update, params: { id: employee_discipline_setting.id, employee_discipline_setting: { employee_id: '', date: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee discipline_setting validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_discipline_settings', action: 'clear')
        patch :update, params: { id: employee_discipline_setting.id, employee_discipline_setting: { date: '' }, format: :json }
        expect(parsed_response['attributes']['date']).to eq(nil)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee discipline_setting' do
        employee_discipline_setting1 = Fabricate(:employee_discipline_setting)
        patch :update, params: { id: employee_discipline_setting1.id, employee_discipline_setting: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'employee_discipline_setting' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee discipline_setting with valid params' do
        delete :destroy, params: { id: employee_discipline_setting.id, format: :json }
        expect(assigns(:employee_discipline_setting).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee discipline_setting with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee discipline_setting' do
        delete :destroy, params: { id: employee_discipline_setting.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
