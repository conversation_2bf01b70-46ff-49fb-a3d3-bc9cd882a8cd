# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeFacilitiesController do
  let(:employee) { Fabricate(:employee) }
  let(:facility) { Fabricate(:facility) }
  let(:employee_facility) { Fabricate(:employee_facility) }
  let(:params) { Fabricate.attributes_for(:employee_facility, end_date: nil) }

  describe 'GET #index' do

    before(:each) do
      Fabricate.times(30, :employee_facility, employee: employee, facility: facility, discarded_at: nil)
    end

    context 'Valid cases - authenticated user', authenticated: true do

      it 'Respond facility list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee facility page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee facility with valid params' do
        expect { post :create, params: { employee_facility: params, format: :json } }.to change(EmployeeFacility, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end
  end

  describe 'PATCH #update' do
    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee facility with valid params' do
        emp_facility = Fabricate(:employee_facility)
        patch :update, params: { id: emp_facility.id, employee_facility: params, format: :json }
        expect(parsed_response['attributes']['from_date']).to eq(params[:from_date])
        success_response
      end
    end
  end

  describe 'DELETE #destroy' do
    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee facility with valid params' do
        delete :destroy, params: { id: employee_facility.id, format: :json }
        expect(assigns(:employee_facility).discarded_at).not_to eq(nil)
        success_response
      end
    end
    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee facility with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end
  end
end
