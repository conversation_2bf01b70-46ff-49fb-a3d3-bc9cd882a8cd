# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeFirearmStatusesController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_firearm_status) { Fabricate(:employee_firearm_status) }
  let(:params) { Fabricate.attributes_for(:employee_firearm_status) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'employee_firearm_status',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_firearm_status, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee firearm status page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee firearm status page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'employee_firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee firearm status with valid params' do
        expect { post :create, params: { employee_firearm_status: params, format: :json } }.to change(EmployeeFirearmStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee firearm status with blank firearm status' do
        validation_columns = ['firearm_status_id']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        post :create, params: { employee_firearm_status: params.merge(firearm_status_id: ''), format: :json }
        expect(error_response[0]).to include({ firearm_status: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
        unprocessable_entity
      end

      it 'Should not create employee firearm status with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        post :create, params: { employee_firearm_status: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
        unprocessable_entity
      end

      it 'Should not create employee firearm status with blank status_date' do
        validation_columns = ['status_date']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        post :create, params: { employee_firearm_status: params.merge(status_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee firearm status with blank firearm_type' do
        validation_columns = ['firearm_type']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        post :create, params: { employee_firearm_status: params.merge(firearm_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee firearm status with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        post :create, params: { employee_firearm_status: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee firearm status validation if required fields present' do
        validation_columns = %w[notes status_date firearm_type]
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        post :create, params: { employee_firearm_status: params.merge(notes: '', status_date: '', firearm_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee firearm status validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_firearm_statuses', action: 'clear')
        expect do
          post :create, params: { employee_firearm_status: params.merge(notes: '', status_date: '', firearm_type: ''), format: :json }
        end.to change(EmployeeFirearmStatus, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq('')
        expect(parsed_response['attributes']['status_date']).to eq(nil)
        expect(parsed_response['attributes']['firearm_type']).to eq('')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee firearm status' do
        post :create, params: { employee_firearm_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'employee_firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee firearm status with valid params' do
        employee_firearm_status1 = Fabricate(:employee_firearm_status)
        patch :update, params: { id: employee_firearm_status1.id, employee_firearm_status: params, format: :json }
        expect(parsed_response['attributes']['firearm_type']).to eq(params[:firearm_type])
        expect(parsed_response['attributes']['firearm_status_id']).to eq(params[:firearm_status_id])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee firearm status with blank firearm status' do
        validation_columns = ['firearm_status_id']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        patch :update, params: { employee_firearm_status: { firearm_status_id: '' }, id: employee_firearm_status.id, format: :json }
        expect(error_response[0]).to include({ firearm_status: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
        unprocessable_entity
      end

      it 'Should not update employee firearm status with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        patch :update, params: { employee_firearm_status: { employee_id: '' }, id: employee_firearm_status.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
        unprocessable_entity
      end

      it 'Should not create employee firearm status with blank status_date' do
        validation_columns = ['status_date']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        patch :update, params: { id: employee_firearm_status.id, employee_firearm_status: { status_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee firearm status with blank firearm_type' do
        validation_columns = ['firearm_type']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        patch :update, params: { id: employee_firearm_status.id, employee_firearm_status: { firearm_type: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee firearm status with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        patch :update, params: { id: employee_firearm_status.id, employee_firearm_status: { notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee firearm status validation if required fields present' do
        validation_columns = %w[notes status_date firearm_type]
        modify_account_schema_validations(model: 'employee_firearm_statuses', fields: validation_columns, action: 'add')
        patch :update, params: { id: employee_firearm_status.id, employee_firearm_status: { status_date: '', notes: '', firearm_type: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee firearm status validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_firearm_statuses', action: 'clear')
        patch :update, params: { id: employee_firearm_status.id, employee_firearm_status: { notes: '', status_date: '', firearm_type: '' }, format: :json }
        expect(parsed_response['attributes']['notes']).to eq('')
        expect(parsed_response['attributes']['status_date']).to eq(nil)
        expect(parsed_response['attributes']['firearm_type']).to eq('')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee firearm status' do
        employee_firearm_status1 = Fabricate(:employee_firearm_status)
        patch :update, params: { id: employee_firearm_status1.id, employee_firearm_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'employee_firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee firearm status with valid params' do
        delete :destroy, params: { id: employee_firearm_status.id, format: :json }
        expect(assigns(:employee_firearm_status).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee firearm status with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee firearm status' do
        delete :destroy, params: { id: employee_firearm_status.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
