# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeMeetingTypesController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_meeting_type) { Fabricate(:employee_meeting_type) }
  let(:params) { Fabricate.attributes_for(:employee_meeting_type) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'employee_meeting_type',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_meeting_type, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee meeting type page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee meeting type page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'employee_meeting_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee meeting type with valid params' do
        expect { post :create, params: { employee_meeting_type: params, format: :json } }.to change(EmployeeMeetingType, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq(params['notes'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee meeting type with blank meeting type' do
        validation_columns = ['meeting_type_id']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        post :create, params: { employee_meeting_type: params.merge(meeting_type_id: ''), format: :json }
        expect(error_response[0]).to include({ meeting_type: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee meeting type with blank meeting date' do
        validation_columns = ['meeting_date']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        post :create, params: { employee_meeting_type: params.merge(meeting_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee meeting type with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        post :create, params: { employee_meeting_type: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee meeting type with blank meeting_date' do
        validation_columns = ['meeting_date']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        post :create, params: { employee_meeting_type: params.merge(meeting_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee meeting type with blank attended' do
        validation_columns = ['attended']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        post :create, params: { employee_meeting_type: params.merge(attended: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee meeting type with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        post :create, params: { employee_meeting_type: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee meeting type validation if required fields present' do
        validation_columns = %w[notes meeting_date attended]
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        post :create, params: { employee_meeting_type: params.merge(notes: '', meeting_date: '', attended: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee meeting type validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_meeting_types', action: 'clear')
        expect do
          post :create, params: { employee_meeting_type: params.merge(notes: '', meeting_date: '', attended: ''), format: :json }
        end.to change(EmployeeMeetingType, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq('')
        expect(parsed_response['attributes']['meeting_date']).to eq(nil)
        expect(parsed_response['attributes']['attended']).to eq(nil)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee meeting type' do
        post :create, params: { employee_meeting_type: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'employee_meeting_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee meeting type with valid params' do
        employee_meeting_type1 = Fabricate(:employee_meeting_type)
        patch :update, params: { id: employee_meeting_type1.id, employee_meeting_type: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        expect(parsed_response['attributes']['meeting_type_id']).to eq(params[:meeting_type_id])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee meeting type with blank Meeting type' do
        validation_columns = ['meeting_type_id']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        patch :update, params: { employee_meeting_type: { meeting_type_id: '' }, id: employee_meeting_type.id, format: :json }
        expect(error_response[0]).to include({ meeting_type: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
        unprocessable_entity
      end

      it 'Should not update employee meeting type with blank Meeting date' do
        validation_columns = ['meeting_date']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        patch :update, params: { employee_meeting_type: { meeting_date: '' }, id: employee_meeting_type.id, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee meeting type with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        patch :update, params: { employee_meeting_type: { employee_id: '' }, id: employee_meeting_type.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee meeting type with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        patch :update, params: { id: employee_meeting_type.id, employee_meeting_type: { notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee meeting type validation if required fields present' do
        validation_columns = %w[notes meeting_date]
        modify_account_schema_validations(model: 'employee_meeting_types', fields: validation_columns)
        patch :update, params: { id: employee_meeting_type.id, employee_meeting_type: { meeting_date: '', notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee meeting type validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_meeting_types', action: 'clear')
        patch :update, params: { id: employee_meeting_type.id, employee_meeting_type: { notes: '', meeting_date: '', attended: '' }, format: :json }
        expect(parsed_response['attributes']['notes']).to eq('')
        expect(parsed_response['attributes']['meeting_date']).to eq(nil)
        expect(parsed_response['attributes']['attended']).to eq(nil)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee meeting type' do
        employee_meeting_type1 = Fabricate(:employee_meeting_type)
        patch :update, params: { id: employee_meeting_type1.id, employee_meeting_type: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'employee_meeting_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee meeting type with valid params' do
        delete :destroy, params: { id: employee_meeting_type.id, format: :json }
        expect(assigns(:employee_meeting_type).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee meeting type with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee meeting type' do
        delete :destroy, params: { id: employee_meeting_type.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
