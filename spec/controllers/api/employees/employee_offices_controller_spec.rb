# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeOfficesController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_office) { Fabricate(:employee_office) }
  let(:params) { Fabricate.attributes_for(:employee_office) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { rights: 'employee',
                                                                                   object_name: 'employee_office',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_office, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee office page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee office page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { rights: 'employee', object_name: 'employee_office' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee office with valid params' do
        expect { post :create, params: { employee_office: params, format: :json } }.to change(EmployeeOffice, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not create employee office with blank office' do
        validation_columns = ['office_id']
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        post :create, params: { employee_office: params.merge(office_id: ''), format: :json }
        expect(error_response[0]).to include({ office: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee office with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        post :create, params: { employee_office: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee office with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        post :create, params: { employee_office: params.merge(start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee office with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        post :create, params: { employee_office: params.merge(end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee office validation if required fields present' do
        validation_columns = %w[start_date end_date]
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        post :create, params: { employee_office: params.merge(start_date: '', end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee office validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_officer_statuses', action: 'clear')
        expect do
          post :create, params: { employee_office: params.merge(start_date: '', end_date: ''), format: :json }
        end.to change(EmployeeOffice, :count).by(1)
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create employee officer with invalid date range' do
        post :create, params: { employee_office: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee office' do
        post :create, params: { employee_office: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { rights: 'employee', object_name: 'employee_office' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee office with valid params' do
        employee_office1 = Fabricate(:employee_office)
        patch :update, params: { id: employee_office1.id, employee_office: params, format: :json }
        expect(parsed_response['attributes']['office_id']).to eq(params[:office_id])
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not update employee office with blank office' do
        validation_columns = ['office_id']
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        patch :update, params: { employee_office: { office_id: '' }, id: employee_office.id, format: :json }
        expect(error_response[0]).to include({ office: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee office with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        patch :update, params: { employee_office: { employee_id: '' }, id: employee_office.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee office with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        patch :update, params: { id: employee_office.id, employee_office: { start_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee office with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        patch :update, params: { id: employee_office.id, employee_office: { end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee office validation if required fields present' do
        validation_columns = %w[start_date end_date]
        modify_account_schema_validations(model: 'employee_offices', fields: validation_columns)
        patch :update, params: { id: employee_office.id, employee_office: { start_date: '', end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee office validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_offices', action: 'clear')
        patch :update, params: { id: employee_office.id, employee_office: { start_date: '', end_date: '' }, format: :json }
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update employee office with invalid date range' do
        patch :update, params: { id: employee_office.id, employee_office: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee office' do
        employee_office1 = Fabricate(:employee_office)
        patch :update, params: { id: employee_office1.id, employee_office: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { rights: 'employee', object_name: 'employee_office' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee office with valid params' do
        delete :destroy, params: { id: employee_office.id, format: :json }
        expect(assigns(:employee_office).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee office with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee office' do
        delete :destroy, params: { id: employee_office.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
