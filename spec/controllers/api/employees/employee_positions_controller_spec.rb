# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeePositionsController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_position) { Fabricate(:employee_position) }
  let(:params) { Fabricate.attributes_for(:employee_position) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { rights: 'employee',
                                                                                   object_name: 'employee_position',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_position, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Display list of employee position' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee position page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Display list of employee position' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee position page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
        success_response
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { rights: 'employee', object_name: 'employee_position' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee position with valid params' do
        expect { post :create, params: { employee_position: params, format: :json } }.to change(EmployeePosition, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee position with blank position' do
        validation_columns = ['position_id']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        post :create, params: { employee_position: params.merge(position_id: ''), format: :json }
        expect(error_response[0]).to include({ position: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee position with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        post :create, params: { employee_position: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee position with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        post :create, params: { employee_position: params.merge(start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee position with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        post :create, params: { employee_position: params.merge(end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee position with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        post :create, params: { employee_position: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee position validation if required fields present' do
        validation_columns = %w[notes start_date end_date]
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        post :create, params: { employee_position: params.merge(notes: '', start_date: '', end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee position validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_positions', action: 'clear')
        expect do
          post :create, params: { employee_position: params.merge(notes: '', start_date: '', end_date: ''), format: :json }
        end.to change(EmployeePosition, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq('')
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create employee position with invalid date range' do
        post :create, params: { employee_position: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee position' do
        post :create, params: { employee_position: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { rights: 'employee', object_name: 'employee_position' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee position with valid params' do
        employee_position1 = Fabricate(:employee_position)
        patch :update, params: { id: employee_position1.id, employee_position: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        expect(parsed_response['attributes']['position_id']).to eq(params[:position_id])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee position with blank position' do
        validation_columns = ['position_id']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        patch :update, params: { employee_position: { position_id: '' }, id: employee_position.id, format: :json }
        expect(error_response[0]).to include({ position: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee position with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        patch :update, params: { employee_position: { employee_id: '' }, id: employee_position.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee position with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        patch :update, params: { id: employee_position.id, employee_position: { start_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee position with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        patch :update, params: { id: employee_position.id, employee_position: { end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee position with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        patch :update, params: { id: employee_position.id, employee_position: { notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee position validation if required fields present' do
        validation_columns = %w[notes start_date end_date]
        modify_account_schema_validations(model: 'employee_positions', fields: validation_columns)
        patch :update, params: { id: employee_position.id, employee_position: { start_date: '', notes: '', end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee position validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_positions', action: 'clear')
        patch :update, params: { id: employee_position.id, employee_position: { notes: '', start_date: '', end_date: '' }, format: :json }
        expect(parsed_response['attributes']['notes']).to eq('')
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update employee position with invalid date range' do
        patch :update, params: { id: employee_position.id, employee_position: params.merge(end_date: Date.today, start_date: Date.tomorrow), format: :json }
        expect(error_response).to eq([{ 'Date range' => [' is invalid - End date is greater than Start date'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee position' do
        employee_position1 = Fabricate(:employee_position)
        patch :update, params: { id: employee_position1.id, employee_position: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { rights: 'employee', object_name: 'employee_position' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee position with valid params' do
        delete :destroy, params: { id: employee_position.id, format: :json }
        expect(assigns(:employee_position).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee position with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee position' do
        delete :destroy, params: { id: employee_position.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
