# frozen_string_literal: true

RSpec.describe Api::Employees::EmployeeRanksController do
  let(:employee) { Fabricate(:employee) }
  let(:employee_rank) { Fabricate(:employee_rank) }
  let(:params) { Fabricate.attributes_for(:employee_rank) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { rights: 'employee',
                                                                                   object_name: 'employee_rank',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :employee_rank, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee rank page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Respond benefits list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employee rank page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { rights: 'employee', object_name: 'employee_rank' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee rank with valid params' do
        expect { post :create, params: { employee_rank: params, format: :json } }.to change(EmployeeRank, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee rank with blank rank' do
        validation_columns = ['rank_id']
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        post :create, params: { employee_rank: params.merge(rank_id: ''), format: :json }
        expect(error_response[0]).to include({ rank: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee rank with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        post :create, params: { employee_rank: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee rank with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        post :create, params: { employee_rank: params.merge(start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employee rank with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        post :create, params: { employee_rank: params.merge(end_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee rank validation if required fields present' do
        validation_columns = %w[start_date end_date]
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        post :create, params: { employee_rank: params.merge(end_date: '', start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee rank validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_ranks', action: 'clear')
        expect do
          post :create, params: { employee_rank: params.merge(end_date: '', start_date: ''), format: :json }
        end.to change(EmployeeRank, :count).by(1)
        expect(parsed_response['attributes']['end_date']).to eq(nil)
        expect(parsed_response['attributes']['start_date']).to eq(nil)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employee rank' do
        post :create, params: { employee_rank: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { rights: 'employee', object_name: 'employee_rank' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee rank with valid params' do
        employee_rank1 = Fabricate(:employee_rank)
        patch :update, params: { id: employee_rank1.id, employee_rank: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        expect(parsed_response['attributes']['rank_id']).to eq(params[:rank_id])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employee rank with blank rank' do
        validation_columns = ['rank_id']
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        patch :update, params: { employee_rank: { rank_id: '' }, id: employee_rank.id, format: :json }
        expect(error_response[0]).to include({ rank: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
        unprocessable_entity
      end

      it 'Should not update employee rank with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        patch :update, params: { employee_rank: { employee_id: '' }, id: employee_rank.id, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee rank with blank start_date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        patch :update, params: { id: employee_rank.id, employee_rank: { start_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employee rank with blank end_date' do
        validation_columns = ['end_date']
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        patch :update, params: { id: employee_rank.id, employee_rank: { end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employee rank validation if required fields present' do
        validation_columns = %w[start_date end_date]
        modify_account_schema_validations(model: 'employee_ranks', fields: validation_columns)
        patch :update, params: { id: employee_rank.id, employee_rank: { start_date: '', end_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employee rank validation if required fields not present' do
        modify_account_schema_validations(model: 'employee_ranks', action: 'clear')
        patch :update, params: { id: employee_rank.id, employee_rank: { start_date: '', end_date: '' }, format: :json }
        expect(parsed_response['attributes']['end_date']).to eq(nil)
        expect(parsed_response['attributes']['start_date']).to eq(nil)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employee rank' do
        employee_rank1 = Fabricate(:employee_rank)
        patch :update, params: { id: employee_rank1.id, employee_rank: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { rights: 'employee', object_name: 'employee_rank' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employee rank with valid params' do
        delete :destroy, params: { id: employee_rank.id, format: :json }
        expect(assigns(:employee_rank).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employee rank with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employee rank' do
        delete :destroy, params: { id: employee_rank.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
