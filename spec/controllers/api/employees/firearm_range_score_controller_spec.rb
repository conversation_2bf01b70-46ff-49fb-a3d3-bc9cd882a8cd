# frozen_string_literal: true

RSpec.describe Api::Employees::FirearmRangeScoresController do
  let(:employee) { Fabricate(:employee) }
  let(:firearm_range_score) { Fabricate(:firearm_range_score) }
  let(:params) { Fabricate.attributes_for(:firearm_range_score) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'firearm_range_score',
                                                                                   rights: 'employee_firearm_status',
                                                                                   params: { employee_id: 1 } }

    before(:each) do
      Fabricate.times(30, :firearm_range_score, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Display list of firearm_range_score' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of firearm_range_score page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Display list of firearm_range_score' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of firearm_range_score page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'firearm_range_score', rights: 'employee_firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create firearm_range_score with valid params' do
        expect { post :create, params: { firearm_range_score: params, format: :json } }.to change(FirearmRangeScore, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create firearm_range_score with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        post :create, params: { firearm_range_score: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create firearm_range_score with blank test date' do
        validation_columns = ['test_date']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        post :create, params: { firearm_range_score: params.merge(test_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create firearm_range_score with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        post :create, params: { firearm_range_score: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create firearm_range_score with blank test type' do
        validation_columns = ['test_type']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        post :create, params: { firearm_range_score: params.merge(test_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create firearm_range_score with blank score' do
        validation_columns = ['score']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        post :create, params: { firearm_range_score: params.merge(score: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check firearm range score validation if required fields not present' do
        modify_account_schema_validations(model: 'firearm_range_scores', action: 'clear')
        expect do
          post :create, params: { firearm_range_score: params.merge(test_date: '', notes: '', test_type: '', score: ''), format: :json }
        end.to change(FirearmRangeScore, :count).by(1)
        expect(parsed_response['attributes']['test_date']).to eq(nil)
        expect(parsed_response['attributes']['test_type']).to eq('')
        expect(parsed_response['attributes']['score']).to eq('')
        expect(parsed_response['attributes']['notes']).to eq('')
        success_response
      end

      it 'Should check firearm range score validation if required fields present' do
        validation_columns = %w[notes test_date test_type score]
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        post :create, params: { firearm_range_score: params.merge(score: '', notes: '', test_date: '', test_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create firearm_range_score' do
        post :create, params: { firearm_range_score: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'firearm_range_score', rights: 'employee_firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update firearm_range_score with valid params' do
        firearm_range_score1 = Fabricate(:firearm_range_score)
        patch :update, params: { id: firearm_range_score1.id, firearm_range_score: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        expect(parsed_response['attributes']['score']).to eq(params[:score])
        expect(parsed_response['attributes']['test_type']).to eq(params[:test_type])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update firearm_range_score with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        patch :update, params: { id: firearm_range_score.id, firearm_range_score: { employee_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update firearm_range_score with blank test date' do
        validation_columns = ['test_date']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        patch :update, params: { id: firearm_range_score.id, firearm_range_score: { test_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update firearm_range_score with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        patch :update, params: { id: firearm_range_score.id, firearm_range_score: { notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update firearm_range_score with blank test type' do
        validation_columns = ['test_type']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        patch :update, params: { id: firearm_range_score.id, firearm_range_score: { test_type: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update firearm_range_score with blank score' do
        validation_columns = ['score']
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        patch :update, params: { id: firearm_range_score.id, firearm_range_score: { score: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check firearm range score validation if required fields not present' do
        modify_account_schema_validations(model: 'firearm_range_scores', action: 'clear')
        patch :update, params: { id: firearm_range_score.id, firearm_range_score: { test_date: '', notes: '', score: '', test_type: '' }, format: :json }
        expect(parsed_response['attributes']['test_date']).to eq(nil)
        expect(parsed_response['attributes']['test_type']).to eq('')
        expect(parsed_response['attributes']['score']).to eq('')
        expect(parsed_response['attributes']['notes']).to eq('')
        success_response
      end

      it 'Should check firearm range score validation if required fields present' do
        validation_columns = %w[notes test_date test_type score]
        modify_account_schema_validations(model: 'firearm_range_scores', fields: validation_columns)
        patch :update, params: { id: firearm_range_score.id, firearm_range_score: { test_date: '', score: '', test_type: '',
                                                                                    notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update firearm_range_score' do
        firearm_range_score1 = Fabricate(:firearm_range_score)
        patch :update, params: { id: firearm_range_score1.id, firearm_range_score: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'firearm_range_score', rights: 'employee_firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete firearm_range_score with valid params' do
        delete :destroy, params: { id: firearm_range_score.id, format: :json }
        expect(assigns(:firearm_range_score).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete firearm_range_score with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete firearm_range_score' do
        delete :destroy, params: { id: firearm_range_score.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
