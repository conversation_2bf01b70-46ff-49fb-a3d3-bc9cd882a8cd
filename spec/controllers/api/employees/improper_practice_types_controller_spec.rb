require 'rails_helper'

RSpec.describe Api::Settings::ImproperPracticeTypesController, type: :controller do
  let(:improper_practice_type) { Fabricate(:improper_practice_type) }
  let(:params) { Fabricate.attributes_for(:improper_practice_type) }

  before(:all) do
    %w[read_improper_practice_type write_improper_practice_type].each do |right|
      Right.where(name: right).first_or_create!(rights_type: Right.generate_right_type(right))
    end
  end

  describe 'GET #index' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'improper_practice_type' }

    before(:each) do
      Fabricate.times(30, :improper_practice_type)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Respond improper practice types list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of improper practice type page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of improper practice type page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond improper practice type list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of improper practice types ordered by name ascending" do
        sort_improper_practice_types = ImproperPracticeType.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_improper_practice_types.ids)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'improper_practice_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      before do
        role = Fabricate(:custom_role)
        right = Right.where(name: 'write_improper_practice_type').first_or_create!(rights_type: Right.generate_right_type('write_improper_practice_type'))
        role.rights << right
        @user.update(role_id: role.id)
      end

      it 'Should create improper practice type with valid params' do
        expect { post :create, params: { improper_practice_type: params, format: :json } }.to change(ImproperPracticeType, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end
  end

  describe 'PUT #update' do
    it_behaves_like 'invalid auth credentials', method: 'put', action: 'update', params: { id: 1 }
    it_behaves_like 'Authorization specs', method: 'put', action: 'update', args: { object_name: 'improper_practice_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      before do
        role = Fabricate(:custom_role)
        right = Right.where(name: 'write_improper_practice_type').first_or_create!(rights_type: Right.generate_right_type('write_improper_practice_type'))
        role.rights << right
        @user.update(role_id: role.id)
      end

      it 'updates the improper practice type successfully' do
        put :update, params: { id: improper_practice_type.slug, improper_practice_type: { name: 'Updated Name' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('Updated Name')
        success_response
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }
    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'improper_practice_type' }

    context 'Valid cases - authenticated user', authenticated: true do
      before do
        role = Fabricate(:custom_role)
        right = Right.where(name: 'write_improper_practice_type').first_or_create!(rights_type: Right.generate_right_type('write_improper_practice_type'))
        role.rights << right
        @user.update(role_id: role.id)
      end

      it 'discards the improper practice type' do
        delete :destroy, params: { id: improper_practice_type.slug, format: :json }
        expect(improper_practice_type.reload.discarded_at).not_to be_nil
        success_response
      end
    end
  end

end