# frozen_string_literal: true

RSpec.describe Api::Employees::LeavesController do
  let(:employee) { Fabricate(:employee) }
  let(:leave) { Fabricate(:leave) }
  let(:params) { Fabricate.attributes_for(:leave) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'leave',
                                                                                   rights: 'employee_analytics',
                                                                                   params: { employee_id: 1 } }
    before(:each) do
      Fabricate.times(30, :leave, leave_type: Leave::LEAVE_TYPES.first, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Display list of leave' do
        get :index, params: { employee_id: employee.id, leave_type: Leave::LEAVE_TYPES.first, duration_from: Date.today - 1.year, duration_to: Date.today }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of leave page 2' do
        get :index, params: { employee_id: employee.id, leave_type: Leave::LEAVE_TYPES.first, duration_from: Date.today - 1.year, duration_to: Date.today, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Should not display with blank leave_type and duration_from' do
        get :index, params: { employee_id: employee.id , duration_to: Date.today }
        expect(error_response).to eq(['Duration From or Leave Type is missing'])
        unprocessable_entity
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Display list of leave' do
        get :index, params: { employee_id: employee.id, leave_type: Leave::LEAVE_TYPES.first, duration_from: Date.today - 1.year, duration_to: Date.today }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of leave page 2' do
        get :index, params: { employee_id: employee.id, leave_type: Leave::LEAVE_TYPES.first, duration_from: Date.today - 1.year, duration_to: Date.today, page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Employee id is missing'])
        unprocessable_entity
      end

      it 'Should not display with blank leave_type and duration_from' do
        get :index, params: { employee_id: employee.id , duration_to: Date.today }
        expect(error_response).to eq(['Duration From or Leave Type is missing'])
        unprocessable_entity
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'leave', rights: 'employee_analytics' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create leave with valid params' do
        expect { post :create, params: { leave: params, format: :json } }.to change(Leave, :count).by(1)
        expect(parsed_response['attributes']['notes']).to eq(params['notes'])
        expect(parsed_response['attributes']['cashed_out']).to eq(params['cashed_out'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create leave with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'leaves', fields: validation_columns)
        post :create, params: { leave: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
        unprocessable_entity
      end

      it 'Should not create leave with blank leave_type' do
        validation_columns = ['leave_type']
        modify_account_schema_validations(model: 'leaves', fields: validation_columns)
        post :create, params: { leave: params.merge(leave_type: ''), format: :json }
        blank_error_responses(validation_columns - ['leave_type'])
        expect(error_response[0]).to include({ leave_type: ["can't be blank", 'is not included in the list'] }.as_json)
      end

      it 'Should not create leave with blank Start date' do
        validation_columns = ['started_at']
        modify_account_schema_validations(model: 'leaves', fields: validation_columns)
        post :create, params: { leave: params.merge(started_at: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check leave validation leave type if required fields present' do
        validation_columns = %w[started_at leave_type]
        modify_account_schema_validations(model: 'leaves', fields: validation_columns)
        post :create, params: { leave: params.merge(started_at: '', leave_type: ''), format: :json }
        blank_error_responses(validation_columns - ['leave_type'])
        expect(error_response[0]).to include({ leave_type: ["can't be blank", 'is not included in the list'] }.as_json)
      end

      it 'Should return 200 - Should not check leave validation leave type if required fields not present' do
        modify_account_schema_validations(model: 'leaves', action: 'clear')
        expect do
          post :create, params: { leave: params.merge(leave_type: 'personal', started_at: ''), format: :json }
        end.to change(Leave, :count).by(1)
        expect(parsed_response['attributes']['started_at']).to eq(nil)
        expect(parsed_response['attributes']['leave_type']).to eq('personal')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create leave' do
        post :create, params: { leave: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'leave', rights: 'employee_analytics' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update leave with valid params' do
        patch :update, params: { id: Fabricate(:leave).id, leave: params, format: :json }
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        expect(parsed_response['attributes']['leave_type']).to eq(params[:leave_type])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update leave with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'leaves', fields: validation_columns)
        patch :update, params: { id: leave.id, leave: { employee_id: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create leave with blank leave_type' do
        validation_columns = ['leave_type']
        modify_account_schema_validations(model: 'leaves', fields: validation_columns)
        patch :update, params: { id: leave.id, leave: { leave_type: '' }, format: :json }
        blank_error_responses(validation_columns - ['leave_type'])
        expect(error_response[0]).to include({ leave_type: ["can't be blank", 'is not included in the list'] }.as_json)
      end

      it 'Should not create leave with blank Start date' do
        validation_columns = ['started_at']
        modify_account_schema_validations(model: 'leaves', fields: validation_columns)
        patch :update, params: { id: leave.id, leave: { started_at: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create leave with blank Start date and leave type' do
        validation_columns = %w[started_at leave_type]
        modify_account_schema_validations(model: 'leaves', fields: validation_columns)
        patch :update, params: { id: leave.id, leave: { started_at: '', leave_type: '' }, format: :json }
        blank_error_responses(validation_columns - ['leave_type'])
        expect(error_response[0]).to include({ leave_type: ["can't be blank", 'is not included in the list'] }.as_json)
      end

      it 'Should not create leave with blank leave_type' do
        modify_account_schema_validations(model: 'leaves', action: 'clear')
        patch :update, params: { id: leave.id, leave: { started_at: '', leave_type: 'personal' }, format: :json }
        expect(parsed_response['attributes']['started_at']).to eq(nil)
        expect(parsed_response['attributes']['leave_type']).to eq('personal')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update leave' do
        patch :update, params: { id: Fabricate(:leave).id, leave: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'leave', rights: 'employee_analytics' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete leave with valid params' do
        delete :destroy, params: { id: leave.id, format: :json }
        expect(assigns(:leave).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete leave with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete leave' do
        delete :destroy, params: { id: leave.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'GET #analytics' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'analytics'

    before(:each) do
      Fabricate.times(2, :leave, leave_type: Leave::LEAVE_TYPES.first, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'analytics configurations with specific analytics type' do
        get :analytics, params: { employee_id: employee.id, analytics_type: AnalyticsConfiguration::ANALYTICS_TYPES.first }
        success_response
      end
    end
  end
end
