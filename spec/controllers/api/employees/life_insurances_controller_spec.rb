# frozen_string_literal: true

RSpec.describe Api::Employees::LifeInsurancesController, type: :controller do
  let(:employee) { Fabricate(:employee) }
  let(:life_insurance) { Fabricate(:life_insurance) }
  let(:params) { Fabricate.attributes_for(:life_insurance) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'life_insurance',
                                                                                   params: { employee_id: 1} }

    before(:each) do
      Fabricate.times(30, :life_insurance, employee: employee)
    end

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Required id is missing'])
        unprocessable_entity
      end

      it 'Respond life insurance list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of life insurance page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      it 'Should not display with blank employee id' do
        get :index
        expect(error_response).to eq(['Required id is missing'])
        unprocessable_entity
      end

      it 'Respond life insurance list with default pagination count' do
        get :index, params: { employee_id: employee.id }
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of life insurance page 2' do
        get :index, params: { employee_id: employee.id, page: 2 }
        expect(parsed_response.count).to eq(5)
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'life_insurance' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create life insurance with valid params' do
        post :create, params: { life_insurance: params, format: :json }
        expect(parsed_response['attributes']['amount']).to eq(params['amount'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create life insurance with blank amount' do
        validation_columns = ['amount']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        post :create, params: { life_insurance: params.merge(amount: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create life insurance with blank start sate' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        post :create, params: { life_insurance: params.merge(start_date: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create life insurance with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        post :create, params: { life_insurance: params.merge(notes: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create life insurance with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        post :create, params: { life_insurance: params.merge(employee_id: ''), format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not create life insurance with blank insurance type' do
        validation_columns = ['insurance_type']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        post :create, params: { life_insurance: params.merge(insurance_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check life insurance validation if required fields not present' do
        modify_account_schema_validations(model: 'life_insurances', action: 'clear')
        expect do
          post :create, params: { life_insurance: params.merge(amount: '', start_date: '', notes: ''), format: :json }
        end.to change(LifeInsurance, :count).by(1)
        expect(parsed_response['attributes']['amount']).to eq(nil)
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['notes']).to eq('')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create life_insurance' do
        post :create, params: { life_insurance: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'life_insurance' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update life insurance with valid params' do
        life_insurance1 = Fabricate(:life_insurance)
        patch :update, params: { id: life_insurance1.id, life_insurance: params, format: :json }
        expect(parsed_response['attributes']['amount']).to eq(params[:amount])
        expect(parsed_response['attributes']['notes']).to eq(params[:notes])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update life insurance with blank amount' do
        validation_columns = ['amount']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        patch :update, params: { id: life_insurance.id, life_insurance: { amount: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update life insurance with blank employee' do
        validation_columns = ['employee_id']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        patch :update, params: { id: life_insurance.id, life_insurance: { employee_id: '' }, format: :json }
        expect(error_response[0]).to include({ employee: ['must exist'] }.as_json)
        blank_error_responses(validation_columns)
      end

      it 'Should not update life insurance with blank insurance type' do
        validation_columns = ['insurance_type']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        patch :update, params: { id: life_insurance.id, life_insurance: { insurance_type: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update life insurance with blank notes' do
        validation_columns = ['notes']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        patch :update, params: { id: life_insurance.id, life_insurance: { notes: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update life insurance with blank start date' do
        validation_columns = ['start_date']
        modify_account_schema_validations(model: 'life_insurances', fields: validation_columns)
        patch :update, params: { id: life_insurance.id, life_insurance: { start_date: '' }, format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check life insurance validation if required fields not present' do
        modify_account_schema_validations(model: 'life_insurances', action: 'clear')
        patch :update, params: { id: life_insurance.id, life_insurance: params.merge(amount: '', start_date: '', notes: ''), format: :json }
        expect(parsed_response['attributes']['amount']).to eq(nil)
        expect(parsed_response['attributes']['start_date']).to eq(nil)
        expect(parsed_response['attributes']['notes']).to eq('')
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update life_insurance' do
        life_insurance1 = Fabricate(:life_insurance)
        patch :update, params: { id: life_insurance1.id, life_insurance: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'life_insurance' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete life insurance with valid params' do
        delete :destroy, params: { id: life_insurance.id, format: :json }
        expect(assigns(:life_insurance).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete life insurance with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete life_insurance' do
        delete :destroy, params: { id: life_insurance.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end

