# frozen_string_literal: true

RSpec.describe Api::Employees::LodiRequestTabsController do
  let(:employee) { Fabricate(:employee) }
  let(:lodi_request_tab) { Fabricate(:lodi_request_tab) }
  let(:params) { Fabricate.attributes_for(:lodi_request_tab) }

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'lodi_request_tab',
                                                                                     rights: 'employee_analytics' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employee lodi request with valid params' do
        expect do
          post :create, params: { lodi_request_tab: params, format: :json }
        end.to change(LodiRequestTab, :count).by(1)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee lodi_request with blank lodi' do
        validation_columns = ['lodi_id']
        modify_account_schema_validations(model: 'lodi_request_tab', fields: validation_columns)
        post :create, params: { lodi_request_tab: params.merge(lodi_id: ''), format: :json }
        expect(error_response[0]).to include({ lodi: ['must exist'] }.as_json)
      end

      it 'Should not create two lodi request for the same lodi and employee' do
        lodi = Fabricate(:lodi)
        # Create another step for same employee and discipline
        Fabricate(:lodi_request_tab, lodi: lodi)

        post :create, params: {
            lodi_request_tab: params.merge(lodi_id: lodi.id),
            format: :json }

        expect(error_response[0]).to include({ request_type: ["Same request already exists"] }.as_json)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create lodi request tab' do
        post :create, params: { lodi_request_tab: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'lodi_request_tab',
                                                                                      rights: 'employee_analytics' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employee lodi request with valid params' do
        patch :update, params: { id: lodi_request_tab.id, lodi_request_tab: params, format: :json }
        expect(parsed_response['attributes']['date']).to eq(params[:date])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employee lodi request with blank lodi' do
        validation_columns = ['lodi_id']
        modify_account_schema_validations(model: 'lodi_request_tab', fields: validation_columns)
        patch :update, params: { id: lodi_request_tab.id, lodi_request_tab: params.merge(lodi_id: ''), format: :json }
        expect(error_response[0]).to include({ lodi: ['must exist'] }.as_json)
      end

      it 'Should not create two lodi request for the same lodi and employee' do
        lodi = Fabricate(:lodi)
        # Create another step for same employee and discipline
        Fabricate(:lodi_request_tab, lodi: lodi)

        post :create, params: {
            lodi_request_tab: params.merge(lodi_id: lodi.id),
            format: :json }

        expect(error_response[0]).to include({ request_type: ["Same request already exists"] }.as_json)
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update lodi request tab' do
        patch :update, params: { id: lodi_request_tab.id, lodi_request_tab: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
