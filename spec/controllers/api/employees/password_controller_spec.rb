# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::Employees::PasswordsController, type: :controller do
  let(:contact) { Fabricate(:personal_contact) }
  let(:params) { {email: contact.value} }

  describe 'POST #forgot_password' do
    context 'Valid cases' do
      it 'Checks the message and response status' do
        expect { post :forgot_password, params: {employee: params} }.to change(ActiveJob::Base.queue_adapter.enqueued_jobs, :size).by(1)
        expect(JSON.parse(response.body)['message']).to eq('You will receive an email with instructions on how to reset your password in a few minutes')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not send forgot password instructions mail with invalid email' do
        post :forgot_password, params: {employee: {email: "<EMAIL>"}}
        expect(error_response).to eq(["Your Email address is missing. Please contact your Admin"])
        unprocessable_entity
      end

      it 'Should not send forgot password instructions mail with empty email' do
        post :forgot_password, params: {employee: {email: ""}}
        expect(error_response).to eq(["Email can't be blank"])
        unprocessable_entity
      end
    end
  end
end