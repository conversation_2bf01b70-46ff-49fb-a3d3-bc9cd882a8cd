# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::PasswordsController, type: :controller do
  let(:user) { Fabricate(:user) }
  let(:params) { {email: user.email} }

  describe 'POST #forgot_password' do

    context 'Valid cases', authenticated: true do

      it 'Checks the message and response status' do
        expect{ post :forgot_password, params: {user: params} }.to change(ActiveJob::Base.queue_adapter.enqueued_jobs, :size).by(1)
        expect(JSON.parse(response.body)['message']).to eq('You will receive an email with instructions on how to reset your password in a few minutes')
        success_response
      end
    end

    context 'InValid cases', authenticated: true do

      it 'Checks the response status' do
        post :forgot_password, params: {user: {email: "<EMAIL>"}}
        unprocessable_entity
      end
    end
  end
end