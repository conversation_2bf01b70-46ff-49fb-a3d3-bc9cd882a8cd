# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::RemindersController do
  include ActiveSupport::Testing::TimeHelpers

  let(:params) { Fabricate.attributes_for(:reminder) }

  describe 'GET #index' do
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'reminder' }

    context 'Reminder Index Action', authenticated: true do
      let(:current_time) { Time.current.in_time_zone('Eastern Time (US & Canada)') }
      before(:each) do
        @reminders = Fabricate.times(30, :reminder)
      end


      it 'Respond reminder list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of reminder page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of reminder page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Filters reminders by status pending' do
        pending_count = Reminder.kept.where(status: 'pending').order('updated_at DESC').limit(25).count
        get :index, params: { status: 'pending' }, format: :json
        num = 0
        parsed_response.each do |reminder|
          expect(reminder['attributes']['status']).to eq('pending')
          num+=1
        end
        expect(pending_count).to eq(num)
        success_response
      end


      it 'Filters reminders by status completed' do
        Fabricate(:reminder, status: 'completed')

        completed_count = Reminder.kept.where(status: 'completed').order('updated_at DESC').limit(25).count
        get :index, params: { status: 'completed' }, format: :json
        num = 0
        parsed_response.each do |reminder|
          expect(reminder['attributes']['status']).to eq('completed')
          num+=1
        end
        expect(completed_count).to eq(num)
        success_response
      end

      it 'Filters reminders by status scheduled' do
          Fabricate.build(:reminder, reminder_start_date: Date.today, reminder_end_date: Date.today + 3.days, time: (current_time + 1.hour).strftime('%H:%M'))
          scheduled_count = Reminder.kept.where(status: 'scheduled').order('updated_at DESC').limit(25).count
          get :index, params: { status: 'scheduled' }, format: :json
          num = 0
          parsed_response.each do |reminder|
            expect(reminder['attributes']['status']).to eq('scheduled')
            num+=1
          end
          expect(scheduled_count).to eq(num)
          success_response
      end
    end
  end

  describe 'GET #show' do
    let(:reminder) { Fabricate(:reminder) }
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'reminder' }

    context 'Reminder Show Action', authenticated: true do
      it 'Responds with the requested reminder' do
        get :show, params: { id: reminder.id }, format: :json
        expect(response).to have_http_status(:success)
        expect(parsed_response['id']).to eq(reminder.id.to_s)
        success_response
      end

      it 'Returns not found for invalid reminder id' do
        get :show, params: { id: -1 }, format: :json
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'POST #create' do
    let(:other_user) { Fabricate(:user) }
    let(:valid_params) do
      {
        reminder: {
          title: 'Test Reminder',
          description: 'This is a test',
          reminder_start_date: Date.today,
          time: '14:00',
          repeat: { type: 'daily', daily: { every: 1 } },
          user_ids: other_user.id.to_s
        }
      }
    end
    let(:invalid_params) do
      {
        reminder: {
          user_ids: other_user.id.to_s,
          title: '',
          description: '',
          reminder_start_date: Date.today,
          time: '14:00',
          repeat: { type: 'daily', daily: { every: 1 } },
        }
      }
    end
    let(:invalid_user_params) do
      {
        reminder: {
          title: 'Test Reminder',
          description: 'This is a test',
          reminder_start_date: Date.today,
          time: '14:00',
          repeat: { type: 'daily', daily: { every: 1 } },
          user_ids: ''
        }
      }
    end

    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'reminder' }

    context 'Reminder Create Action', authenticated: true do
      it 'Creates a new reminder with valid params' do
        expect {
          post :create, params: valid_params, format: :json
        }.to change(Reminder, :count).by(1)

        parsed_response = JSON.parse(response.body)
        attributes = parsed_response.dig('data', 'attributes')

        expect(attributes['title']).to eq('Test Reminder')
        expect(attributes['status']).to eq('scheduled')
        expect(attributes['creator_name']).to be_present
        expect(attributes['user_ids']).to include(other_user.id)
        expect(parsed_response['message']).to eq('Reminder created')
      end

      it 'does not create a reminder with missing required fields' do
        expect {
          post :create, params: invalid_params, format: :json
        }.not_to change(Reminder, :count)
        parsed_response = JSON.parse(response.body)

        expect(parsed_response['errors']).to include("Title can't be blank")
        expect(parsed_response['errors']).to include("Description can't be blank")
      end

      it 'does not create a reminder with missing user fields' do
        expect {
          post :create, params: invalid_user_params, format: :json
        }.not_to change(Reminder, :count)

        parsed_response = JSON.parse(response.body)
        expect(parsed_response['errors']).to include("At least one user must be present")
      end

      it 'Creates a daily schedule with IceCube' do
        post :create, params: valid_params, format: :json
        reminder = Reminder.last
        expect(reminder.schedule).to be_a(IceCube::Schedule)
        expect(reminder.schedule.recurrence_rules.first).to be_a(IceCube::DailyRule)
        expect(reminder.schedule.start_time.strftime('%H:%M')).to eq('14:00')
      end
    end
  end

  describe 'PATCH #update' do
    let(:other_user) { Fabricate(:user) }
    let(:reminder) { Fabricate(:reminder) }
    let(:update_params) do
      {
        id: reminder.id,
        reminder: {
          title: 'Updated Reminder',
          description: 'Updated description',
          reminder_start_date: Date.today + 1.day,
          time: '15:00:00',
          repeat: { type: 'daily', daily: { every: 2 } },
          user_ids: other_user.id.to_s
        }
      }
    end
    let(:invalid_params) do
      {
        id: reminder.id,
        reminder: {
          title: '',
          description: ''
        }
      }
    end

    context 'Reminder Update', authenticated: true do
      it 'Updates an existing reminder with valid params' do
        patch :update, params: update_params, format: :json

        reminder.reload
        expect(reminder.title).to eq('Updated Reminder')
        expect(reminder.description).to eq('Updated description')
        expect(reminder.schedule.start_time.strftime('%H:%M:%S')).to eq('15:00:00')
        expect(reminder.schedule.recurrence_rules.first).to be_a(IceCube::DailyRule)

        parsed_response = JSON.parse(response.body)
        expect(parsed_response['message']).to eq('Reminder updated')
        expect(parsed_response['data']['attributes']['title']).to eq('Updated Reminder')
        expect(parsed_response['data']['attributes']['status']).to eq('pending')
      end


      it 'Returns error for invalid params' do
        patch :update, params: invalid_params, format: :json

        parsed_response = JSON.parse(response.body)
        expect(parsed_response['errors']).to include("At least one user must be present")
      end

      it 'Returns not found for invalid reminder id' do
        patch :update, params: { id: -1, reminder: { title: 'Test' } }, format: :json
        not_found
      end
    end
  end

  describe 'DELETE #destroy' do
    let(:reminder) { Fabricate(:reminder) }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Soft deletes an existing reminder' do
        delete :destroy, params: { id: reminder.id }, format: :json

        reminder.reload
        expect(reminder.discarded?).to be true

        parsed_response = JSON.parse(response.body)
        expect(parsed_response['message']).to eq('Reminder deleted')
        expect(parsed_response['data']['id']).to eq(reminder.id.to_s)
      end

      it 'Returns not found for invalid reminder id' do
        delete :destroy, params: { id: -1 }, format: :json
        not_found
      end
    end
  end
end