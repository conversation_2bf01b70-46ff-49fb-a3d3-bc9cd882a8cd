# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::ReportsController do
  let(:employee) { Fabricate(:employee) }
  let(:params) { Fabricate.attributes_for(:report) }

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: {object_name: 'report'}

    context 'Single employee report' do

      context 'Valid cases - authenticated user', authenticated: true do
        it 'Should create report with valid params' do
          post :create, params: {report: { report_type: Report::ReportTypes::SINGLE_EMPLOYEE, report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end

    context 'Benifits report' do

      context 'Valid cases - authenticated user', authenticated: true do
        it 'Should create report with valid params' do
          post :create, params: {report: { report_type: Report::ReportTypes::BENEFITS, report_format: 'pdf', columns: 0, employee_ids: employee.id, start_date_from: Date.current, start_date_to: Date.current + 2 }}
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end

    context 'Sick report' do

      context 'Valid cases - authenticated user', authenticated: true do
        it 'Should create report with valid params' do
          post :create, params: {report: { report_type: Report::ReportTypes::SICK_BANK, report_format: 'pdf', columns: 0, employee_ids: employee.id, home_phone: '**********', cellphone: '**********' }}
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end

    context 'Discipline report' do

      context 'Valid cases - authenticated user', authenticated: true do

        it 'Should create report with valid params with date range filter' do
          post :create, params: {report: { report_type: Report::ReportTypes::DISCIPLINES, report_format: 'pdf', columns: 0, employee_ids: employee.id,  start_date: Date.current, end_date: Date.current + 2}}
          expect(parsed_response).to eq(nil)
          success_response
        end

        it 'Should create report with valid params with was_employee_pds filter' do
          post :create, params: {report: { report_type: Report::ReportTypes::DISCIPLINES, report_format: 'pdf', columns: 0, employee_ids: employee.id,  was_employee_pds: true}}
          expect(parsed_response).to eq(nil)
          success_response
        end

        it 'Should create report with valid params with case_abeyance filter' do
          post :create, params: {report: { report_type: Report::ReportTypes::DISCIPLINES, report_format: 'pdf', columns: 0, employee_ids: employee.id,  case_abeyance: true}}
          expect(parsed_response).to eq(nil)
          success_response
        end

        it 'Should create report with valid params with ta_implemented filter' do
          post :create, params: {report: { report_type: Report::ReportTypes::DISCIPLINES, report_format: 'pdf', columns: 0, employee_ids: employee.id,  ta_implemented: true}}
          expect(parsed_response).to eq(nil)
          success_response
        end

        it 'Should create report with valid params with do_not_mail filter' do
          post :create, params: {report: { report_type: Report::ReportTypes::DISCIPLINES, report_format: 'pdf', columns: 0, employee_ids: employee.id,  do_not_mail: true}}
          expect(parsed_response).to eq(nil)
          success_response
        end

        it 'Should create report with valid params discipline_setting filter' do
          post :create, params: {report: { report_type: Report::ReportTypes::DISCIPLINES, report_format: 'pdf', columns: 0, employee_ids: employee.id,  discipline_ids: [1]}}
          expect(parsed_response).to eq(nil)
          success_response
        end

        it 'Should create report with valid params olr_date filter' do
          post :create, params: {report: { report_type: Report::ReportTypes::DISCIPLINES, report_format: 'pdf', columns: 0, employee_ids: employee.id,  filed_olr_from_date: Date.current, filed_olr_to_date: Date.current + 4} }
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end

    context 'grieverance report' do

      context 'Valid cases - authenticated user', authenticated: true do
        it 'Should create report with valid params' do
          post :create, params: {report: { report_type: Report::ReportTypes::GRIEVANCES, report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(parsed_response).to eq(nil)
          success_response
        end

        it 'Should create report with valid params and date filter' do
          post :create, params: {report: { report_type: Report::ReportTypes::GRIEVANCES, report_format: 'pdf', columns: 0, employee_ids: employee.id, start_date: Date.current, end_date: Date.current + 2 }}
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end

    context 'Janus report' do

      context 'Valid cases - authenticated user', authenticated: true do
        it 'Should create report with valid params' do
          post :create, params: {report: { report_type: Report::ReportTypes::JANUS, report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end

    context 'Lodi report' do

      context 'Valid cases - authenticated user', authenticated: true do
        it 'Should create report with valid params' do
          post :create, params: {report: { report_type: Report::ReportTypes::LODI, report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end

    context 'Union meetings report' do

      context 'Valid cases - authenticated user', authenticated: true do
        it 'Should create report with valid params' do
          post :create, params: {report: { report_type: Report::ReportTypes::UNION_MEETINGS, report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end

    context 'Employee delegate assignment report' do

      context 'Valid cases - authenticated user', authenticated: true do
        it 'Should create report with valid params' do
          post :create, params: {report: { report_type: Report::ReportTypes::EMPLOYEE_DELEGATE_ASSIGNMENT, report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(parsed_response).to eq(nil)
          success_response
        end
      end

      context 'InValid cases - authenticated user', authenticated: true do
        it 'Should throw an error response' do
          post :create, params: {report: {  report_format: 'pdf', columns: 0, employee_ids: employee.id }}
          expect(error_response).to eq(['Required param is missing'])
          unprocessable_entity
        end
      end
    end
  end
end
