# frozen_string_literal: true

RSpec.describe Api::Settings::BenefitsController do
  let(:benefit) { Fabricate(:benefit) }
  let(:params) { Fabricate.attributes_for(:benefit) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :benefit)
      end

      it 'Respond benefits list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of benefit page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of benefit page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond benefits list with custom pagination count specified(per_page count) page 1' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond benefits list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of benefits ordered by name ascending" do
        sort_benefits = Benefit.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_benefits.ids)
      end
    end

    context 'Benefits list - Search by name with authenticated user', authenticated: true do
      it 'Filters the benefits by name' do
        Fabricate.times(5, :benefit)
        get :index, params: { search_text: benefit.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated user', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :benefit)
      end

      it 'Respond benefits list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of benefit page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of benefit page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond benefits list with custom pagination count specified(per_page count) page 1' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond benefits list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of benefits ordered by name ascending" do
        sort_benefits = Benefit.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_benefits.ids)
      end
    end

    context 'Benefits list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the benefits by name' do
        Fabricate.times(5, :benefit)
        get :index, params: { search_text: benefit.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create benefit with valid params' do
        expect { post :create, params: { benefit: params, format: :json } }.to change(Benefit, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create benefit with empty name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'benefits', fields: validation_columns)
        post :create, params: { benefit: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create benefit with empty description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'benefits', fields: validation_columns)
        post :create, params: { benefit: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check benefits validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'benefits', fields: validation_columns)
        post :create, params: { benefit: params.merge(description: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check benefit validation if required fields not present' do
        modify_account_schema_validations(model: 'benefits', action: 'clear')
        expect do
          post :create, params: { benefit: params.merge(name: '', description: ''), format: :json }
        end.to change(Benefit, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create benefit with same name' do
        post :create, params: { benefit: params.merge(name: benefit.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create benefit' do
        post :create, params: { benefit: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update benefit with valid params' do
        patch :update, params: { id: benefit.id, benefit: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
        success_response
      end

      it 'Should update benefit with valid params slug' do
        patch :update, params: { id: benefit.slug, benefit: { name: 'testing' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('testing')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update benefit with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'benefits', fields: validation_columns)
        patch :update, params: { id: benefit.id, benefit: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update benefit with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'benefits', fields: validation_columns)
        patch :update, params: { id: benefit.id, benefit: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check benefit validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'benefits', fields: validation_columns)
        patch :update, params: { id: benefit.id, benefit: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check benefit validation if required fields not present' do
        modify_account_schema_validations(model: 'benefits', action: 'clear')
        patch :update, params: { id: benefit.id, benefit: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update benefit with blank id' do
        patch :update, params: { id: '', benefit: params.merge(name: ''), format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'Should not update benefit with same name' do
        patch :update, params: { id: Fabricate(:benefit).id, benefit: { name: benefit.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update benefit' do
        patch :update, params: { id: benefit.id, benefit: { name: 'sample' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'benefit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete benefit with valid params slug' do
        delete :destroy, params: { id: benefit.slug, format: :json }
        expect(assigns(:benefit).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete benefit and update discarded_at for its associations' do
        Fabricate(:employee_benefit, benefit: benefit)
        delete :destroy, params: { id: benefit.slug, format: :json }
        expect(assigns(:benefit).discarded_at).not_to eq(nil)
        expect(benefit.employee_benefits.first.reload.discarded_at).not_to eq(nil)
      end

      it 'Should delete benefit with valid params id' do
        delete :destroy, params: { id: benefit.id, format: :json }
        expect(assigns(:benefit).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete benefit  with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete benefit' do
        delete :destroy, params: { id: benefit.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
