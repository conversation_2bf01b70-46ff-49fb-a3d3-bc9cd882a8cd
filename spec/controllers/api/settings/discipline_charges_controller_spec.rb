# frozen_string_literal: true

RSpec.describe Api::Settings::DisciplineChargesController do
  let(:discipline_charge) { Fabricate(:discipline_charge) }
  let(:params) { Fabricate.attributes_for(:discipline_charge) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'discipline_charge' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :discipline_charge)
      end

      it 'Respond discipline settings list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of discipline setting page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of discipline setting page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond discipline setting list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond discipline setting list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of discipline charges ordered by name ascending" do
        sort_discipline_charges = DisciplineCharge.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_discipline_charges.ids)
      end
    end

    context 'Discipline charges list - Search by name authenticated user', authenticated: true do
      it 'Filters the discipline charges by name' do
        Fabricate.times(5, :discipline_charge)
        get :index, params: { search_text: discipline_charge.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :discipline_charge)
      end

      it 'Respond discipline settings list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of discipline setting page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of discipline setting page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond discipline setting list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond discipline setting list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of discipline charges ordered by name ascending" do
        sort_discipline_charges = DisciplineCharge.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_discipline_charges.ids)
      end
    end

    context 'Discipline charges list - Search by name authenticated employee', authenticated_employee: true do
      it 'Filters the discipline charges by name' do
        Fabricate.times(5, :discipline_charge)
        get :index, params: { search_text: discipline_charge.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'discipline_charge' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create discipline setting with valid params' do
        expect { post :create, params: { discipline_charge: params, format: :json } }.to change(DisciplineCharge, :count).by(1)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create discipline setting with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'discipline_charges', fields: validation_columns)
        post :create, params: { discipline_charge: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create discipline setting with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'discipline_charges', fields: validation_columns)
        post :create, params: { discipline_charge: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check discipline setting validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'discipline_charges', fields: validation_columns)
        post :create, params: { discipline_charge: params.merge(description: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check discipline setting validation if required fields not present' do
        modify_account_schema_validations(model: 'discipline_charges', action: 'clear')
        expect do
          post :create, params: { discipline_charge: params.merge(name: '', description: ''), format: :json }
        end.to change(DisciplineCharge, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create discipline setting with invalid same name' do
        post :create, params: { discipline_charge: { name: discipline_charge.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create discipline charge' do
        post :create, params: { discipline_charge: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'discipline_charge' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update discipline setting with valid params id' do
        patch :update, params: { id: discipline_charge.id, discipline_charge: { name: 'discipline' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('discipline')
        success_response
      end

      it 'Should update discipline setting with valid params slug' do
        patch :update, params: { id: discipline_charge.slug, discipline_charge: { name: 'discipline' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('discipline')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update discipline setting with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'discipline_charges', fields: validation_columns)
        patch :update, params: { id: discipline_charge.id, discipline_charge: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update discipline setting with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'discipline_charges', fields: validation_columns)
        patch :update, params: { id: discipline_charge.id, discipline_charge: params.merge(description: ''),
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check discipline_charges validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'discipline_charges', fields: validation_columns)
        patch :update, params: { id: discipline_charge.id, discipline_charge: params.merge(name: '', description: ''),
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check discipline_charges validation if required fields not present' do
        modify_account_schema_validations(model: 'discipline_charges', action: 'clear')
        patch :update, params: { id: discipline_charge.id, discipline_charge: params.merge(name: '', description: ''),
                                 format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update discipline setting with same name' do
        patch :update, params: { id: Fabricate(:discipline_charge).id, discipline_charge: { name: discipline_charge.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update discipline setting with blank id' do
        patch :update, params: { id: '', discipline_charge: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update discipline charge' do
        patch :update, params: { id: discipline_charge.id, discipline_charge: { name: 'discipline' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'discipline_charge' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete discipline setting with valid params slug' do
        delete :destroy, params: { id: discipline_charge.slug, format: :json }
        expect(assigns(:discipline_charge).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete discipline setting with valid params id' do
        delete :destroy, params: { id: discipline_charge.id, format: :json }
        expect(assigns(:discipline_charge).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete discipline setting with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete discipline setting  with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete discipline charge' do
        delete :destroy, params: { id: discipline_charge.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
