# frozen_string_literal: true

RSpec.describe Api::Settings::DisciplineStatusesController do
  let(:discipline_status) { Fabricate(:discipline_status) }
  let(:params) { Fabricate.attributes_for(:discipline_status) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'discipline_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :discipline_status)
      end

      it 'Respond discipline settings list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of discipline setting page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of discipline setting page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond discipline setting list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond discipline setting list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of discipline status ordered by name ascending" do
        sort_discipline_status = DisciplineStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_discipline_status.ids)
      end
    end

    context 'Discipline status list - Search by name with authenticated user', authenticated: true do
      it 'Filters the discipline statuses by name' do
        Fabricate.times(5, :discipline_status)
        get :index, params: { search_text: discipline_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :discipline_status)
      end

      it 'Respond discipline settings list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of discipline setting page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of discipline setting page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond discipline setting list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond discipline setting list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of discipline status ordered by name ascending" do
        sort_discipline_status = DisciplineStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_discipline_status.ids)
      end
    end

    context 'Discipline status list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the discipline statuses by name' do
        Fabricate.times(5, :discipline_status)
        get :index, params: { search_text: discipline_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'discipline_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create discipline setting with valid params' do
        expect { post :create, params: { discipline_status: params, format: :json } }.to change(DisciplineStatus, :count).by(1)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create discipline setting with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'discipline_statuses', fields: validation_columns)
        post :create, params: { discipline_status: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create discipline setting with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'discipline_statuses', fields: validation_columns)
        post :create, params: { discipline_status: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check discipline setting validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'discipline_statuses', fields: validation_columns)
        post :create, params: { discipline_status: params.merge(description: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check discipline setting validation if required fields not present' do
        modify_account_schema_validations(model: 'discipline_statuses', action: 'clear')
        expect do
          post :create, params: { discipline_status: params.merge(name: '', description: ''), format: :json }
        end.to change(DisciplineStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create discipline setting with invalid same name' do
        post :create, params: { discipline_status: { name: discipline_status.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create discipline status' do
        post :create, params: { discipline_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'discipline_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update discipline setting with valid params id' do
        patch :update, params: { id: discipline_status.id, discipline_status: { name: 'discipline' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('discipline')
        success_response
      end

      it 'Should update discipline setting with valid params slug' do
        patch :update, params: { id: discipline_status.slug, discipline_status: { name: 'discipline' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('discipline')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update discipline setting with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'discipline_statuses', fields: validation_columns)
        patch :update, params: { id: discipline_status.id, discipline_status: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update discipline setting with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'discipline_statuses', fields: validation_columns)
        patch :update, params: { id: discipline_status.id, discipline_status: params.merge(description: ''),
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check discipline_statuses validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'discipline_statuses', fields: validation_columns)
        patch :update, params: { id: discipline_status.id, discipline_status: params.merge(name: '', description: ''),
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check discipline_statuses validation if required fields not present' do
        modify_account_schema_validations(model: 'discipline_statuses', action: 'clear')
        patch :update, params: { id: discipline_status.id, discipline_status: params.merge(name: '', description: ''),
                                 format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update discipline setting with same name' do
        patch :update, params: { id: Fabricate(:discipline_status).id, discipline_status: { name: discipline_status.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update discipline setting with blank id' do
        patch :update, params: { id: '', discipline_status: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update discipline status' do
        patch :update, params: { id: discipline_status.id, discipline_status: { name: 'discipline' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'discipline_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete discipline setting with valid params slug' do
        delete :destroy, params: { id: discipline_status.slug, format: :json }
        expect(assigns(:discipline_status).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete discipline setting with valid params id' do
        delete :destroy, params: { id: discipline_status.id, format: :json }
        expect(assigns(:discipline_status).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete discipline setting with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete discipline setting  with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete discipline status' do
        delete :destroy, params: { id: discipline_status.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
