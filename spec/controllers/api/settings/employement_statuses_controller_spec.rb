# frozen_string_literal: true

RSpec.describe Api::Settings::EmploymentStatusesController do
  let(:employment_status) { Fabricate(:employment_status) }
  let(:params) { Fabricate.attributes_for(:employment_status) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'employment_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :employment_status)
      end

      it 'Respond employment statuses list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employment statuses page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of employment statuses page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond employment statuses list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond employment statuses list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of employment statuses ordered by name ascending" do
        sort_employment_statuses = EmploymentStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_employment_statuses.ids)
      end
    end

    context 'Employment status list - Search by name with authenticated user', authenticated: true do
      it 'Filters the employment statuses by name' do
        Fabricate.times(5, :employment_status)
        get :index, params: { search_text: employment_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :employment_status)
      end

      it 'Respond employment statuses list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of employment statuses page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of employment statuses page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond employment statuses list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond employment statuses list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of employment statuses ordered by name ascending" do
        sort_employment_statuses = EmploymentStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_employment_statuses.ids)
      end
    end

    context 'Employment status list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the employment statuses by name' do
        Fabricate.times(5, :employment_status)
        get :index, params: { search_text: employment_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'employment_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create employment statuses with valid params' do
        expect { post :create, params: { employment_status: params, format: :json } }.to change(EmploymentStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create employment statues with with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'employment_statuses', fields: validation_columns)
        post :create, params: { employment_status: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create employment statues with with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'employment_statuses', fields: validation_columns)
        post :create, params: { employment_status: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employment statues validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'employment_statuses', fields: validation_columns)
        post :create, params: { employment_status: params.merge(description: '', name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employment statues validation if required fields not present' do
        modify_account_schema_validations(model: 'employment_statuses', action: 'clear')
        expect do
          post :create, params: { employment_status: params.merge(name: '', description: ''), format: :json }
        end.to change(EmploymentStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create employment statues with same name' do
        post :create, params: { employment_status: params.merge(name: employment_status.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create employment status' do
        post :create, params: { employment_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'employment_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update employment statuses with valid params' do
        patch :update, params: { id: employment_status.id, employment_status: { name: 'employment_status' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('employment_status')
        success_response
      end

      it 'Should update employment statuses with valid params slug' do
        patch :update, params: { id: employment_status.slug, employment_status: { name: 'employment_status' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('employment_status')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update employment statues with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'employment_statuses', fields: validation_columns)
        patch :update, params: { id: employment_status.id, employment_status: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update employment statues with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'employment_statuses', fields: validation_columns)
        patch :update, params: { id: employment_status.id, employment_status: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check employment statues validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'employment_statuses', fields: validation_columns)
        patch :update, params: { id: employment_status.id, employment_status: params.merge(name: '', description: ''),
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check employment statues validation if required fields not present' do
        modify_account_schema_validations(model: 'employment_statuses', action: 'clear')
        patch :update, params: { id: employment_status.id, employment_status: params.merge(name: '', description: ''),
                                 format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update employment statues with invalid same name' do
        patch :update, params: { id: Fabricate(:employment_status).id, employment_status: { name: employment_status.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update employment statues with blank id' do
        patch :update, params: { id: '', employment_status: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update employment status' do
        patch :update, params: { id: employment_status.id, employment_status: { name: 'employment_status' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'employment_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete employment statuses with valid params slug' do
        delete :destroy, params: { id: employment_status.slug, format: :json }
        expect(assigns(:employment_status).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete employment statuses with valid params id' do
        delete :destroy, params: { id: employment_status.id, format: :json }
        expect(assigns(:employment_status).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete employment statuses with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete employment statuses with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete employment status' do
        delete :destroy, params: { id: employment_status.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
