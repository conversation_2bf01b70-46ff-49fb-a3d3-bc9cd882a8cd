# frozen_string_literal: true

RSpec.describe Api::Settings::FacilitiesController do
  let(:facility) { Fabricate(:facility) }
  let(:params) { Fabricate.attributes_for(:facility) }

  describe 'GET #index' do

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :facility)
      end

      it 'Respond facility list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of facility page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of facility page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end
    end
  end

  describe 'POST #create' do

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create facility with valid params' do
        p params
        expect { post :create, params: { facility: params }, format: :json }.to change(Facility, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end


    context 'Invalid cases', authenticated: true do
      it 'Should not create facility with invalid same name' do
        params
        post :create, params: { facility: params.merge(name: facility.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    describe 'PATCH #update' do

      context 'Valid cases - authenticated user', authenticated: true do

        it 'Should update facility valid params' do
          patch :update, params: { id: facility.id, facility: { name: 'test' }, format: :json }
          expect(parsed_response['attributes']['name']).to eq('test')
          success_response
        end

        it 'Should update facility valid params slug' do
          patch :update, params: { id: facility.slug, facility: { name: 'test' }, format: :json }
          expect(parsed_response['attributes']['name']).to eq('test')
        end
      end

      context 'Invalid cases', authenticated: true do
        it 'Should not update facility with invalid same name' do
          patch :update, params: { id: Fabricate(:facility).id, facility: params.merge(name: facility.name), format: :json }
          expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
          unprocessable_entity
        end

        it 'Should not update facility with blank id' do
          patch :update, params: { id: '', facility: params, format: :json }
          expect(error_response).to eq(['Record not found'])
          not_found
        end
      end
    end
  end

  describe 'DELETE #destroy' do

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete facility with valid params slug' do
        delete :destroy, params: { id: facility.slug, format: :json }
        expect(assigns(:facility).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete facility with valid params id' do
        delete :destroy, params: { id: facility.id, format: :json }
        expect(assigns(:facility).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete facility with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete facility with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete facility' do
        delete :destroy, params: { id: facility.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
