# frozen_string_literal: true

RSpec.describe Api::Settings::FirearmStatusesController do
  let(:firearm_status) { Fabricate(:firearm_status) }
  let(:params) { Fabricate.attributes_for(:firearm_status) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :firearm_status)
      end

      it 'Respond firearm statuses list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of firearm_status page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of firearm_status page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond firearm_status list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond firearm_status list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of firearm status ordered by name ascending" do
        sort_firearm_statuses = FirearmStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_firearm_statuses.ids)
      end
    end

    context 'Firearm status list - Search by name with authenticated user', authenticated: true do
      it 'Filters the firearm statuses by name' do
        Fabricate.times(5, :firearm_status)
        get :index, params: { search_text: firearm_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :firearm_status)
      end

      it 'Respond firearm statuses list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of firearm_status page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of firearm_status page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond firearm_status list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond firearm_status list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of firearm status ordered by name ascending" do
        sort_firearm_statuses = FirearmStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_firearm_statuses.ids)
      end
    end

    context 'Firearm status list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the firearm statuses by name' do
        Fabricate.times(5, :firearm_status)
        get :index, params: { search_text: firearm_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create firearm status with valid params' do
        expect { post :create, params: { firearm_status: params, format: :json } }.to change(FirearmStatus, :count).by(1)
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create firearm status with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'firearm_statuses', fields: validation_columns)
        post :create, params: { firearm_status: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create firearm status with blank firearm_type' do
        validation_columns = ['firearm_type']
        modify_account_schema_validations(model: 'firearm_statuses', fields: validation_columns)
        post :create, params: { firearm_status: params.merge(firearm_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create firearm status with blank firearm_test_type' do
        validation_columns = ['firearm_test_type']
        modify_account_schema_validations(model: 'firearm_statuses', fields: validation_columns)
        post :create, params: { firearm_status: params.merge(firearm_test_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check firearm status validation if required fields present' do
        validation_columns = %w[firearm_type firearm_test_type name]
        modify_account_schema_validations(model: 'firearm_statuses', fields: validation_columns)
        post :create, params: { firearm_status: params.merge(name: '', firearm_type: '', firearm_test_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check firearm status validation if required fields not present' do
        modify_account_schema_validations(model: 'firearm_statuses', action: 'clear')
        expect do
          post :create, params: { firearm_status: params.merge(name: '', firearm_type: '', firearm_test_type: ''), format: :json }
        end.to change(FirearmStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['firearm_type']).to eq('')
        expect(parsed_response['attributes']['firearm_test_type']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create firearm status with invalid same name' do
        firearm_status
        post :create, params: { firearm_status: params.merge(name: firearm_status.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create firearm status' do
        post :create, params: { firearm_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update firearm statue with valid params' do
        patch :update, params: { id: firearm_status.id, firearm_status: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
        success_response
      end

      it 'Should update firearm statue with valid params slug' do
        patch :update, params: { id: firearm_status.slug, firearm_status: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update firearm status with empty name ' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'firearm_statuses', fields: validation_columns)
        patch :update, params: { id: firearm_status.id, firearm_status: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update firearm status with empty firearm_type' do
        validation_columns = ['firearm_type']
        modify_account_schema_validations(model: 'firearm_statuses', fields: validation_columns)
        patch :update, params: { id: firearm_status.id, firearm_status: params.merge(firearm_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update firearm status with empty firearm_test_type' do
        validation_columns = ['firearm_test_type']
        modify_account_schema_validations(model: 'firearm_statuses', fields: validation_columns)
        patch :update, params: { id: firearm_status.id, firearm_status: params.merge(firearm_test_type: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check firearm status validation if required fields present' do
        validation_columns = %w[firearm_type firearm_test_type name]
        modify_account_schema_validations(model: 'firearm_statuses', fields: validation_columns)
        patch :update, params: { id: firearm_status.id, firearm_status: params.merge(name: '', firearm_type: '', firearm_test_type: ''),
                                 format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check firearm status validation if required fields not present' do
        modify_account_schema_validations(model: 'firearm_statuses', action: 'clear')
        patch :update, params: { id: firearm_status.id, firearm_status: params.merge(name: '', firearm_type: '',
                                                                                     firearm_test_type: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['firearm_type']).to eq('')
        expect(parsed_response['attributes']['firearm_test_type']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update firearm status with invalid same name ' do
        patch :update, params: { id: Fabricate(:firearm_status).id, firearm_status: { name: firearm_status.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update firearm status with blank id ' do
        patch :update, params: { id: '', firearm_status: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update firearm status' do
        patch :update, params: { id: firearm_status.id, firearm_status: { name: 'sample' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'firearm_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete firearm status with valid params slug' do
        delete :destroy, params: { id: firearm_status.slug, format: :json }
        expect(assigns(:firearm_status).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete firearm status with valid params id' do
        delete :destroy, params: { id: firearm_status.id, format: :json }
        expect(assigns(:firearm_status).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete firearm status with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete firearm status with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete firearm status' do
        delete :destroy, params: { id: firearm_status.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
