# frozen_string_literal: true

RSpec.describe Api::Settings::GendersController do
  let(:gender) { Fabricate(:gender) }
  let(:params) { Fabricate.attributes_for(:gender) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'gender' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :gender)
      end

      it 'Respond genders list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of gender page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of gender page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond genders list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond genders list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of genders ordered by name ascending" do
        sort_genders = Gender.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_genders.ids)
      end
    end

    context 'gender list - Search by name with authenticated user', authenticated: true do
      it 'Filters the genders by name' do
        Fabricate.times(5, :gender)
        get :index, params: { search_text: gender.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :gender)
        Gender.last.destroy
      end

      it 'Respond genders list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of gender page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of gender page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond genders list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond genders list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of genders ordered by name ascending" do
        sort_genders = Gender.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_genders.ids)
      end
    end

    context 'gender list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the genders by name' do
        Fabricate.times(5, :gender)
        get :index, params: { search_text: gender.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'gender' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create gender with valid params' do
        expect { post :create, params: { gender: params, format: :json } }.to change(Gender, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create gender with empty name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'genders', fields: validation_columns)
        post :create, params: { gender: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create gender with empty description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'genders', fields: validation_columns)
        post :create, params: { gender: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check gender validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'genders', fields: validation_columns)
        post :create, params: { gender: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check gender validation if required fields not present' do
        modify_account_schema_validations(model: 'genders', action: 'clear')
        expect do
          post :create, params: { gender: params.merge(name: '', description: ''), format: :json }
        end.to change(Gender, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create gender with invalid same name' do
        gender
        post :create, params: { gender: params.merge(name: gender.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create gender' do
        post :create, params: { gender: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'gender' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update gender with valid params' do
        patch :update, params: { id: gender.id, gender: { name: 'gender' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('gender')
        success_response
      end

      it 'Should update gender with valid params slug' do
        patch :update, params: { id: gender.slug, gender: { name: 'gender' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('gender')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update gender with empty name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'genders', fields: validation_columns)
        patch :update, params: { id: gender.id, gender: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update gender with empty description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'genders', fields: validation_columns)
        patch :update, params: { id: gender.id, gender: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check gender validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'genders', fields: validation_columns)
        patch :update, params: { id: gender.id, gender: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check gender validation if required fields not present' do
        modify_account_schema_validations(model: 'genders', action: 'clear')
        patch :update, params: { id: gender.id, gender: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update gender with invalid same name' do
        patch :update, params: { id: Fabricate(:gender).id, gender: { name: gender.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update gender with blank id' do
        patch :update, params: { id: '', gender: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update gender' do
        patch :update, params: { id: gender.id, gender: { name: 'gender' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'gender' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete gender with valid params slug' do
        delete :destroy, params: { id: gender.slug, format: :json }
        expect(assigns(:gender).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete gender with valid params id' do
        delete :destroy, params: { id: gender.id, format: :json }
        expect(assigns(:gender).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete gender with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete gender with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete gender' do
        post :destroy, params: { id: gender.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
