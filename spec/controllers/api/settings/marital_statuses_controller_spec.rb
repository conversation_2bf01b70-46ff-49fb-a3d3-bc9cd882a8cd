# frozen_string_literal: true

RSpec.describe Api::Settings::<PERSON><PERSON>StatusesController do
  let(:marital_status) { Fabricate(:marital_status) }
  let(:params) { Fabricate.attributes_for(:marital_status) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'marital_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :marital_status)
      end

      it 'Respond marital statuses list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of marital_status page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of marital_status page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond marital status list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond marital status list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of marital statuses ordered by name ascending" do
        sort_marital_statues = MaritalStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_marital_statues.ids)
      end
    end

    context 'Marital status list - Search by name with authenticated user', authenticated: true do
      it 'Filters the marital statuses by name' do
        Fabricate.times(5, :marital_status)
        get :index, params: { search_text: marital_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :marital_status)
      end

      it 'Respond marital statuses list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of marital_status page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of marital_status page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond marital status list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond marital status list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of marital statuses ordered by name ascending" do
        sort_marital_statues = MaritalStatus.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_marital_statues.ids)
      end
    end

    context 'Marital status list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the marital statuses by name' do
        Fabricate.times(5, :marital_status)
        get :index, params: { search_text: marital_status.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'marital_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create marital status with valid params' do
        expect { post :create, params: { marital_status: params, format: :json } }.to change(MaritalStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create marital status with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'marital_statuses', fields: ['name'])
        post :create, params: { marital_status: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create marital status with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'marital_statuses', fields: ['description'])
        post :create, params: { marital_status: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check marital status validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'marital_statuses', fields: %w[name description])
        post :create, params: { marital_status: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check marital status validation if required fields not present' do
        modify_account_schema_validations(model: 'marital_statuses', action: 'clear')
        expect do
          post :create, params: { marital_status: params.merge(name: '', description: ''), format: :json }
        end.to change(MaritalStatus, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create marital status with invalid same name' do
        marital_status
        post :create, params: { marital_status: params.merge(name: marital_status.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create marital status' do
        post :create, params: { marital_status: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'marital_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update marital status with valid params slug' do
        patch :update, params: { id: marital_status.slug, marital_status: { name: 'marital_status' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('marital_status')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update marital status with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'marital_statuses', fields: validation_columns)
        patch :update, params: { id: marital_status.id, marital_status: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update marital status with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'marital_statuses', fields: validation_columns)
        patch :update, params: { id: marital_status.id, marital_status: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check marital status validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'marital_statuses', fields: validation_columns)
        patch :update, params: { id: marital_status.id, marital_status: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check marital status validation if required fields not present' do
        modify_account_schema_validations(model: 'marital_statuses', action: 'clear')
        patch :update, params: { id: marital_status.id, marital_status: { name: '', description: '' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update marital status with invalid same name' do
        patch :update, params: { id: Fabricate(:marital_status).id, marital_status: { name: marital_status.name }, format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update marital status with blank id' do
        patch :update, params: { id: '', marital_status: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update marital status' do
        patch :update, params: { id: marital_status.id, marital_status: { name: 'marital_status' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'marital_status' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete marital status with valid params slug' do
        delete :destroy, params: { id: marital_status.slug, format: :json }
        expect(assigns(:marital_status).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete marital status with valid params id' do
        delete :destroy, params: { id: marital_status.id, format: :json }
        expect(assigns(:marital_status).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete marital status with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete marital status with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete marital status' do
        delete :destroy, params: { id: marital_status.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
