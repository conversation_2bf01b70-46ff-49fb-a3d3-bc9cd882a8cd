# frozen_string_literal: true

RSpec.describe Api::Settings::OfficesController do
  let(:office) { Fabricate(:office) }
  let(:params) { Fabricate.attributes_for(:office) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'office' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :office)
      end

      it 'Respond offices list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of office page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of office page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond office list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond office list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of offices ordered by name ascending" do
        sort_offices = Office.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_offices.ids)
      end
    end

    context 'Office list - Search by name with authenticated user', authenticated: true do
      it 'Filters the offices by name' do
        Fabricate.times(5, :office)
        get :index, params: { search_text: office.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :office)
      end

      it 'Respond offices list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of office page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of office page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond office list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond office list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of offices ordered by name ascending" do
        sort_offices = Office.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_offices.ids)
      end
    end

    context 'Office list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the offices by name' do
        Fabricate.times(5, :office)
        get :index, params: { search_text: office.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'office' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create office with valid params' do
        expect { post :create, params: { office: params, format: :json } }.to change(Office, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create office with empty params name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        post :create, params: { office: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create office with empty params address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        post :create, params: { office: params.merge(address: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create office with empty params phone' do
        validation_columns = ['phone']
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        post :create, params: { office: params.merge(phone: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create office with empty params fax' do
        validation_columns = ['fax']
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        post :create, params: { office: params.merge(fax: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check office validation if required fields present' do
        validation_columns = %w[name address phone fax]
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        post :create, params: { office: params.merge(name: '', address: '', phone: '', fax: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check office validation if required fields not present' do
        modify_account_schema_validations(model: 'offices', action: 'clear')
        expect do
          post :create, params: { office: params.merge(name: '', phone: '', address: '', fax: ''), format: :json }
        end.to change(Office, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['address']).to eq('')
        expect(parsed_response['attributes']['phone']).to eq('')
        expect(parsed_response['attributes']['fax']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not create office with invalid same name' do
        office
        post :create, params: { office: params.merge(name: office.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create office' do
        post :create, params: { office: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'office' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update office valid params' do
        patch :update, params: { id: office.id, office: { name: 'office' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('office')
        success_response
      end

      it 'Should update office valid params slug' do
        patch :update, params: { id: office.slug, office: { name: 'office' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('office')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update office with empty name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        patch :update, params: { id: office.id, office: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update office with empty address' do
        validation_columns = ['address']
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        patch :update, params: { id: office.id, office: params.merge(address: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update office with empty phone' do
        validation_columns = ['phone']
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        patch :update, params: { id: office.id, office: params.merge(phone: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update office with empty fax' do
        validation_columns = ['fax']
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        patch :update, params: { id: office.id, office: params.merge(fax: ''), format: :json }
        expect(error_response).to eq([{ 'fax' => ["can't be blank"] }])
        unprocessable_entity
      end

      it 'Should check office validation if required fields present' do
        validation_columns = %w[name address phone fax]
        modify_account_schema_validations(model: 'offices', fields: validation_columns)
        patch :update, params: { id: office.id, office: params.merge(name: '', address: '', phone: '', fax: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check office validation if required fields not present' do
        modify_account_schema_validations(model: 'offices', action: 'clear')
        patch :update, params: { id: office.id, office: params.merge(name: '', address: '', phone: '', fax: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['address']).to eq('')
        expect(parsed_response['attributes']['phone']).to eq('')
        expect(parsed_response['attributes']['fax']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update office with invalid same name' do
        patch :update, params: { id: Fabricate(:office).id, office: params.merge(name: office.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'Should not update office with blank id' do
        patch :update, params: { id: '', office: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update office' do
        patch :update, params: { id: office.id, office: { name: 'office' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'office' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete office with valid params slug' do
        delete :destroy, params: { id: office.slug, format: :json }
        expect(assigns(:office).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete office with valid params id' do
        delete :destroy, params: { id: office.id, format: :json }
        expect(assigns(:office).discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete office with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete office  with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete office' do
        delete :destroy, params: { id: office.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
