# frozen_string_literal: true

RSpec.describe Api::Settings::<PERSON>fsController do
  let(:pacf) { Fabricate(:pacf) }
  let(:params) { Fabricate.attributes_for(:pacf) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'pacf', rights: 'paf' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :pacf)
      end

      it 'Respond pacfs list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of pacf page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of pacf page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond pacf list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond pacf list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of pacfs ordered by name ascending" do
        sort_pacfs = Pacf.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_pacfs.ids)
      end
    end

    context 'Pacfs list - Search by name with authenticated user', authenticated: true do
      it 'Filters the pacfs by name' do
        Fabricate.times(5, :pacf)
        get :index, params: { search_text: pacf.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :pacf)
      end

      it 'Respond pacfs list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of pacf page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of pacf page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond pacf list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond pacf list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of pacfs ordered by name ascending" do
        sort_pacfs = Pacf.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_pacfs.ids)
      end
    end

    context 'Pacfs list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the pacfs by name' do
        Fabricate.times(5, :pacf)
        get :index, params: { search_text: pacf.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'pacf', rights: 'paf' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should create pacf with valid params' do
        expect { post :create, params: { pacf: params, format: :json } }.to change(Pacf, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'Invalid cases - static required fields', authenticated: true do
      it 'Should not create pacf with empty pacf name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'pacfs', fields: validation_columns)
        post :create, params: { pacf: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create pacf with empty pacf description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'pacfs', fields: validation_columns)
        post :create, params: { pacf: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check pacf validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'pacfs', fields: validation_columns)
        post :create, params: { pacf: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check pacf validation if required fields not present' do
        modify_account_schema_validations(model: 'pacfs', action: 'clear')
        expect do
          post :create, params: { pacf: params.merge(name: '', description: ''), format: :json }
        end.to change(Pacf, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases - dynamic required fields', authenticated: true do
      it 'Should not create pacf with blank pacf same name' do
        pacf
        post :create, params: { pacf: params.merge(name: pacf.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create pacf' do
        post :create, params: { pacf: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'pacf', rights: 'paf' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update pacf with valid params' do
        patch :update, params: { id: pacf.id, pacf: { name: 'pacf' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('pacf')
        success_response
      end

      it 'Should update pacf with valid params slug' do
        patch :update, params: { id: pacf.slug, pacf: { name: 'pacf' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('pacf')
      end
    end

    context 'Invalid cases - static required fields', authenticated: true do
      it 'should not update pacf with empty params' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'pacfs', fields: validation_columns)
        patch :update, params: { id: pacf.id, pacf: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'should not update pacf with empty params description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'pacfs', fields: validation_columns)
        patch :update, params: { id: pacf.id, pacf: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'should not update pacf with empty params' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'pacfs', fields: validation_columns)
        patch :update, params: { id: pacf.id, pacf: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check pacf validation if required fields not present' do
        modify_account_schema_validations(model: 'pacfs', action: 'clear')
        patch :update, params: { id: pacf.id, pacf: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases - dynamic required fields', authenticated: true do
      it 'should not update pacf with invalid same name' do
        pacf1 = Fabricate(:pacf)
        patch :update, params: { id: pacf1.id, pacf: params.merge(name: pacf.name), format: :json }
        expect(error_response).to eq([{ 'name' => ['has already been taken'] }])
        unprocessable_entity
      end

      it 'should not update pacf with blank id' do
        patch :update, params: { id: '', pacf: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update pacf' do
        patch :update, params: { id: pacf.id, pacf: { name: 'pacf' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'pacf', rights: 'paf' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete pacf with valid params slug' do
        delete :destroy, params: { id: pacf.slug, format: :json }
        expect(assigns(:pacf).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete pacf with valid params id' do
        delete :destroy, params: { id: pacf.id, format: :json }
      end

      it 'Should delete pacf with valid params' do
        Fabricate(:employee_pacf, pacf: pacf)
        delete :destroy, params: { id: pacf.slug, format: :json }
        expect(assigns(:pacf).discarded_at).not_to eq(nil)
        expect(pacf.employee_pacfs.first.reload.discarded_at).not_to eq(nil)
        success_response
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete pacf with invalid params' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end

      it 'delete pacf with with blank id' do
        delete :destroy, params: { id: '', format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete pacf' do
        delete :destroy, params: { id: pacf.slug, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
