# frozen_string_literal: true

RSpec.describe Api::Settings::UnitsController do
  let(:unit) { Fabricate(:unit) }
  let(:params) { Fabricate.attributes_for(:unit) }

  describe 'GET #index' do
    # This contains 'invalid' JWT token cases.
    it_behaves_like 'invalid auth credentials', method: 'get', action: 'index'

    it_behaves_like 'Authorization specs', method: 'get', action: 'index', args: { object_name: 'unit' }

    context 'Valid cases - authenticated user', authenticated: true do
      before(:each) do
        Fabricate.times(30, :unit)
      end

      it 'Respond units list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of unit page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of unit page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond units list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond units list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of units ordered by name ascending" do
        sort_units = Unit.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_units.ids)
      end
    end

    context 'unit list - Search by name with authenticated user', authenticated: true do
      it 'Filters the units by name' do
        Fabricate.times(5, :unit)
        get :index, params: { search_text: unit.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end

    context 'Valid cases - authenticated employee', authenticated_employee: true do
      before(:each) do
        Fabricate.times(30, :unit)
        Unit.last.destroy
      end

      it 'Respond units list with default pagination count' do
        get :index
        expect(parsed_response.count).to eq(25)
        success_response
      end

      it 'Display list of unit page 2' do
        get :index, params: { page: 2 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Display list of unit page limit' do
        get :index, params: { page: 3 }
        expect(parsed_response.count).to eq(5)
      end

      it 'Respond units list with custom pagination count specified(per_page count)' do
        get :index, params: { per_page: 28, page: 1 }
        expect(parsed_response.count).to eq(28)
      end

      it 'Respond units list with custom pagination count specified(per_page count) with page 2' do
        get :index, params: { per_page: 28, page: 2 }
        expect(parsed_response.count).to eq(2)
      end

      it "Display list of units ordered by name ascending" do
        sort_units = Unit.order('name asc').limit(25)
        get :index
        expect(parsed_response.map { |x| x['id'].to_i }).to eq(sort_units.ids)
      end
    end

    context 'unit list - Search by name with authenticated employee', authenticated_employee: true do
      it 'Filters the units by name' do
        Fabricate.times(5, :unit)
        get :index, params: { search_text: unit.name }, format: :json
        expect(parsed_response.count).to eq(1)
        success_response
      end
    end
  end

  describe 'POST #create' do
    it_behaves_like 'invalid auth credentials', method: 'post', action: 'create'

    it_behaves_like 'Authorization specs', method: 'post', action: 'create', args: { object_name: 'unit' }

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should create unit with valid params' do
        expect { post :create, params: { unit: params, format: :json } }.to change(Unit, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq(params['name'])
        success_response
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not create unit with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'units', fields: validation_columns)
        post :create, params: { unit: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not create unit with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'units', fields: validation_columns)
        post :create, params: { unit: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should check unit validation if required fields present' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'units', fields: validation_columns)
        post :create, params: { unit: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check unit validation if required fields not present' do
        modify_account_schema_validations(model: 'units', action: 'clear')
        expect do
          post :create, params: { unit: params.merge(name: '', description: ''), format: :json }
        end.to change(Unit, :count).by(1)
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not create unit' do
        post :create, params: { unit: params, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'PATCH #update' do
    it_behaves_like 'invalid auth credentials', method: 'patch', action: 'update', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'patch', action: 'update', args: { object_name: 'unit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should update unit with valid params' do
        patch :update, params: { id: unit.id, unit: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
        success_response
      end

      it 'Should update unit with valid params slug' do
        patch :update, params: { id: unit.slug, unit: { name: 'sample' }, format: :json }
        expect(parsed_response['attributes']['name']).to eq('sample')
      end
    end

    context 'SaaS compliance(dynamic validations based on SaaS account JSON)', authenticated: true do
      it 'Should not update unit with blank name' do
        validation_columns = ['name']
        modify_account_schema_validations(model: 'units', fields: validation_columns)
        patch :update, params: { id: unit.id, unit: params.merge(name: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update unit with blank description' do
        validation_columns = ['description']
        modify_account_schema_validations(model: 'units', fields: validation_columns)
        patch :update, params: { id: unit.id, unit: params.merge(description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should not update unit with blank name' do
        validation_columns = %w[name description]
        modify_account_schema_validations(model: 'units', fields: validation_columns)
        patch :update, params: { id: unit.id, unit: params.merge(name: '', description: ''), format: :json }
        blank_error_responses(validation_columns)
      end

      it 'Should return 200 - Should not check unit validation if required fields not present' do
        modify_account_schema_validations(model: 'units', action: 'clear')
        patch :update, params: { id: unit.id, unit: params.merge(name: '', description: ''), format: :json }
        expect(parsed_response['attributes']['name']).to eq('')
        expect(parsed_response['attributes']['description']).to eq('')
        success_response
      end
    end

    context 'Invalid cases', authenticated: true do
      it 'Should not update unit with blank id' do
        patch :update, params: { id: '', unit: params, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not update unit' do
        patch :update, params: { id: unit.id, unit: { name: 'sample' }, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end

  describe 'DELETE #destroy' do
    it_behaves_like 'invalid auth credentials', method: 'delete', action: 'destroy', params: { id: 1 }

    it_behaves_like 'Authorization specs', method: 'delete', action: 'destroy', args: { object_name: 'unit' }

    context 'Valid cases - authenticated user', authenticated: true do
      it 'Should delete unit with valid params slug' do
        delete :destroy, params: { id: unit.slug, format: :json }
        expect(assigns(:unit).discarded_at).not_to eq(nil)
        success_response
      end

      it 'Should delete unit with valid params id' do
        delete :destroy, params: { id: unit.id, format: :json }
        expect(assigns(:unit).discarded_at).not_to eq(nil)
      end
    end

    context 'Invalid cases - authenticated user', authenticated: true do
      it 'Should not delete unit with invalid id' do
        delete :destroy, params: { id: 2, format: :json }
        expect(error_response).to eq(['Record not found'])
        not_found
      end
    end

    context 'Invalid cases - authenticated employee', authenticated_employee: true do
      it 'Access Denied - should not delete unit' do
        delete :destroy, params: { id: unit.id, format: :json }
        expect(error_response).to eq(["Access Denied: you are not authorized to perform this action"])
        forbidden
      end
    end
  end
end
