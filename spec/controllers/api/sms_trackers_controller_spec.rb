# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::SmsTrackersController, type: :controller do
  let(:notification_tracker) { Fabricate(:notification_tracker, message_id: '4514a190-aaec-11ea-b6ed-0242ac110003') }
  let(:params) { Fabricate.attributes_for(:notification_tracker) }

  describe 'POST #create' do
    context 'Valid cases - authenticated user', authenticated: true do
      it 'Update notification tracker for sent sms message' do
        notification_tracker
        post :create, params: { MessageUUID: '4514a190-aaec-11ea-b6ed-0242ac110003', Status: 'sent' }
        expect(NotificationTracker.find_by(message_id: '4514a190-aaec-11ea-b6ed-0242ac110003').sms_sent).to eq(true)
        success_response
      end

      it 'Update notification tracker for sent sms delivered' do
        notification_tracker
        post :create, params: { MessageUUID: '4514a190-aaec-11ea-b6ed-0242ac110003', Status: 'delivered' }
        expect(NotificationTracker.find_by(message_id: '4514a190-aaec-11ea-b6ed-0242ac110003').sms_delivered).to eq(true)
        success_response
      end

      it 'Update notification tracker for sent sms failed' do
        notification_tracker
        post :create, params: { MessageUUID: '4514a190-aaec-11ea-b6ed-0242ac110003', Status: 'failed', ErrorCode: 200 }
        expect(NotificationTracker.find_by(message_id: '4514a190-aaec-11ea-b6ed-0242ac110003').sms_failed).to eq(true)
        success_response
      end

      it 'Update notification tracker for sent sms undelivered' do
        notification_tracker
        post :create, params: { MessageUUID: '4514a190-aaec-11ea-b6ed-0242ac110003', Status: 'undelivered', ErrorCode: 200 }
        expect(NotificationTracker.find_by(message_id: '4514a190-aaec-11ea-b6ed-0242ac110003').sms_undelivered).to eq(true)
        success_response
      end
    end

    context 'Invalid params', authenticated: true do
      it 'Should not update notification tracker for sent sms message status invalid' do
        notification_tracker
        post :create, params: { MessageUUID: '4514a190-aaec-11ea-b6ed-0242ac110003' }
        expect(NotificationTracker.find_by(message_id: '4514a190-aaec-11ea-b6ed-0242ac110003').sms_failed).to eq(false)
        success_response
      end

      it 'Should not update notification tracker for sent sms message status invalid' do
        notification_tracker
        post :create, params: { MessageUUID: '4514a190-aaec-11ea-b6ed-0242ac110003' }
        expect(NotificationTracker.find_by(message_id: '4514a190-aaec-11ea-b6ed-0242ac110003').sms_sent).to eq(false)
        success_response
      end

      it 'Should not update notification tracker for sent sms message status invalid' do
        notification_tracker
        post :create, params: { MessageUUID: '4514a190-aaec-11ea-b6ed-0242ac110003' }
        expect(NotificationTracker.find_by(message_id: '4514a190-aaec-11ea-b6ed-0242ac110003').sms_delivered).to eq(false)
        success_response
      end
    end
  end
end
