Fabricator(:assault) do
  location        { sequence { |i| "#{Faker::Lorem.word}_#{i}" } }
  date            { Date.today }
  time            {  Time.current.strftime("%H:%M:%S") }
  physical        false
  verbal          false
  description     { Faker::Lorem.paragraph }
  incident_reported_to  { Faker::Lorem.paragraph }
  incident_report false
  lodi_pack       false
  delegate        false

  employee
end