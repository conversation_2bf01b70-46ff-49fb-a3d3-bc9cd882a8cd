Fabricator(:change_request) do
  request_type { "employee" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         status: "pending",
         first_name: "test",
         id: Fabricate(:employee).id
     },
     {
         status: "pending",
         street: "street name",
         id: Fabricate(:employee).id
     }]
  end
end

Fabricator(:contact_change_request, from: 'change_request') do
  request_type { "contact" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "value": "test@mallow_tech.com",
         "id": Fabricate(:contact).id,
         "employee_id": Fabricate(:employee).id
     },
     {
         "status": "pending",
         "value": "(121) 221 - 2122",
         "id": Fabricate(:contact).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:award_update, from: 'change_request') do
  request_type { "award" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "awarded_on": "2021-05-30",
         "name": "Award 2",
         "description": "Notes",
         "id": Fabricate(:award).id
     }]
  end
end

Fabricator(:award_create, from: 'change_request') do
  request_type { "award" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "awarded_on": "2021-05-30",
         "name": "Award 2",
         "description": "Notes",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:beneficiary_update, from: 'change_request') do
  request_type { "beneficiary" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "name": "Sample Name",
         "percentage": 50.00,
         "relationship": "Child",
         "beneficiary_type": "Secondary",
         "address": "AG street, Karur",
         "phone": "(121) 221 - 2122",
         "timestamp": Time.now.to_i,
         "id": Fabricate(:beneficiary).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:beneficiary_create, from: 'change_request') do
  request_type { "beneficiary" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "name": "Sample Name",
         "percentage": 50.00,
         "relationship": "Child",
         "beneficiary_type": "Secondary",
         "address": "AG street, Karur",
         "phone": "(121) 221 - 2122",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:benefit_coverage_update, from: 'change_request') do
  request_type { "benefit_coverage" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "name": "Test name 1",
         "relationship": "child",
         "social_security_number": "***********",
         "address": "AG Street, Karur",
         "birthday": "2021-06-30",
         "expires_at": "2021-06-30",
         "phone": "(121) 221 - 2122",
         "age": 0,
         "employee_benefit_id": Fabricate(:employee_benefit).id,
         "id": Fabricate(:benefit_coverage).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:benefit_coverage_create, from: 'change_request') do
  request_type { "benefit_coverage" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "name": "Test name 1",
         "relationship": "child",
         "social_security_number": "***********",
         "address": "AG Street, Karur",
         "birthday": "2021-06-30",
         "expires_at": "2021-06-30",
         "phone": "(121) 221 - 2122",
         "age": 0,
         "employee_benefit_id": Fabricate(:employee_benefit).id,
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:benefit_disbursement_update, from: 'change_request') do
  request_type { "benefit_disbursement" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "year": "2015",
         "date": "2021-05-12",
         "payment_type_id": Fabricate(:payment_type).id,
         "reference_number": "12341",
         "amount": "123",
         "notes": "Notes",
         "employee_benefit_id": Fabricate(:employee_benefit).id,
         "id": Fabricate(:benefit_disbursement).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:benefit_disbursement_create, from: 'change_request') do
  request_type { "benefit_disbursement" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "year": "2015",
         "date": "2021-05-12",
         "payment_type_id": Fabricate(:payment_type).id,
         "reference_number": "12341",
         "amount": "123",
         "notes": "Notes",
         "employee_benefit_id": Fabricate(:employee_benefit).id,
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:delegate_assignment_update, from: 'change_request') do
  request_type { "delegate_assignment" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "delegate_employee_id": Fabricate(:employee).id,
         "office_id": Fabricate(:office).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "notes": "Notes",
         "id": Fabricate(:delegate_assignment).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:delegate_assignment_create, from: 'change_request') do
  request_type { "delegate_assignment" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "delegate_employee_id": Fabricate(:employee).id,
         "office_id": Fabricate(:office).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "notes": "Notes",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:dependent_update, from: 'change_request') do
  request_type { "dependent" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "life_insurance_id": Fabricate(:life_insurance).id,
         "name": "Test Name",
         "relationship": "Spouse",
         "amount": 50000.00,
         "address": "KARUR",
         "date": "1995-05-29",
         "age": 26,
         "spouse_contribution": "1.92",
         "id": Fabricate(:dependent).id
     }]
  end
end

Fabricator(:dependent_create, from: 'change_request') do
  request_type { "dependent" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "life_insurance_id": Fabricate(:life_insurance).id,
         "name": "Test Name",
         "relationship": "Spouse",
         "amount": 50000.00,
         "address": "KARUR",
         "date": "1995-05-29",
         "age": 26,
         "spouse_contribution": "1.92",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:employee_benefit_update, from: 'change_request') do
  request_type { "employee_benefit" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "benefit_id": Fabricate(:benefit).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "description": "Notes",
         "id": Fabricate(:employee_benefit).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_benefit_create, from: 'change_request') do
  request_type { "employee_benefit" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "benefit_id": Fabricate(:benefit).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "description": "Notes",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_department_update, from: 'change_request') do
  request_type { "employee_department" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "department_id": Fabricate(:department).id,
         "start_date": "2021-04-20",
         "end_date": "2021-05-30",
         "id": Fabricate(:employee_department).id
     }]
  end
end

Fabricator(:employee_department_create, from: 'change_request') do
  request_type { "employee_department" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "department_id": Fabricate(:department).id,
         "start_date": "2021-04-20",
         "end_date": "2021-05-30",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:employee_discipline_setting_update, from: 'change_request') do
  request_type { 'employee_discipline_setting' }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "discipline_setting_id": Fabricate(:discipline_setting).id,
         "description": "Description",
         "date": "2021-06-10",
         "discipline_charge_id": Fabricate(:discipline_charge).id,
         "dan_number": "dan_number 1",
         "recommended_penalty": "recommended_penalty",
         "was_employee_pds": "true",
         "case_and_abeyance": "false",
         "ta_implemented": "true",
         "abandonment_hearing": "true",
         "filed_olr": "2021-04-05",
         "charge": "charge",
         "id": Fabricate(:employee_discipline_setting).id
     }]
  end
end

Fabricator(:employee_discipline_setting_create, from: 'change_request') do
  request_type { 'employee_discipline_setting' }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "discipline_setting_id": Fabricate(:discipline_setting).id,
         "description": "Description",
         "date": "2021-06-10",
         "discipline_charge_id": Fabricate(:discipline_charge).id,
         "dan_number": "dan_number 1",
         "recommended_penalty": "recommended_penalty",
         "was_employee_pds": "true",
         "case_and_abeyance": "false",
         "ta_implemented": "true",
         "abandonment_hearing": "true",
         "filed_olr": "2021-04-05",
         "charge": "charge",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:employee_discipline_step_update, from: 'change_request') do
  request_type { 'employee_discipline_step' }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "date": "2021-04-12",
         "recommended_notes": "No remarks",
         "is_settled": "true",
         "is_pending": "false",
         "win": "false",
         "loss": "false",
         "step": "step_1",
         "employee_discipline_setting_id": Fabricate(:employee_discipline_setting).id,
         "discipline_status_id": Fabricate(:discipline_status).id,
         "id": Fabricate(:employee_discipline_step).id
     }]
  end
end

Fabricator(:employee_discipline_step_create, from: 'change_request') do
  request_type { 'employee_discipline_step' }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "date": "2021-04-12",
         "recommended_notes": "No remarks",
         "is_settled": "true",
         "is_pending": "false",
         "win": "false",
         "loss": "false",
         "step": "step_1",
         "employee_discipline_setting_id": Fabricate(:employee_discipline_setting).id,
         "discipline_status_id": Fabricate(:discipline_status).id,
         "timestamp": Time.now.to_i
     }]
  end
end


Fabricator(:employee_employment_status_update, from: 'change_request') do
  request_type { "employee_employment_status" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employment_status_id": Fabricate(:employment_status).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "id": Fabricate(:employee_employment_status).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_employment_status_create, from: 'change_request') do
  request_type { "employee_employment_status" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employment_status_id": Fabricate(:employment_status).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_firearm_status_update, from: 'change_request') do
  request_type { "employee_firearm_status" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "id": Fabricate(:employee_firearm_status).id,
         "firearm_status_id": Fabricate(:firearm_status).id,
         "status_date": "2021-03-30",
         "firearm_type": "firearm type 1",
         "notes": "Sample text"

     }]
  end
end

Fabricator(:employee_firearm_status_create, from: 'change_request') do
  request_type { "employee_firearm_status" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "timestamp": Time.now.to_i,
         "firearm_status_id": Fabricate(:firearm_status).id,
         "status_date": "2021-03-30",
         "firearm_type": "firearm type 1",
         "notes": "Sample text"

     }]
  end
end

Fabricator(:employee_grievance_update, from: 'change_request') do
  request_type { "employee_grievance" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "date": "2021-04-12",
         "description": "No remarks",
         "number": "NO1",
         "violation_alleged": "Violation Alleged",
         "filed_olr": "2021-04-13",
         "charge": " charge 1",
         "grievance_id": Fabricate(:grievance).id,
         "employee_id": Fabricate(:employee).id,
         "id": Fabricate(:employee_grievance).id
     }]
  end
end

Fabricator(:employee_grievance_create, from: 'change_request') do
  request_type { "employee_grievance" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "date": "2021-04-12",
         "description": "No remarks",
         "number": "NO1",
         "violation_alleged": "Violation Alleged",
         "filed_olr": "2021-04-13",
         "charge": " charge 1",
         "grievance_id": Fabricate(:grievance).id,
         "employee_id": Fabricate(:employee).id,
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:employee_grievance_step_update, from: 'change_request') do
  request_type { "employee_grievance_step" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "date": "2021-04-12",
         "recommended_notes": "No remarks",
         "is_settled": "true",
         "is_pending": "false",
         "win": "false",
         "loss": "false",
         "step": "step_1",
         "employee_grievance_id": Fabricate(:employee_grievance).id,
         "grievance_status_id": Fabricate(:grievance_status).id,
         "id": Fabricate(:employee_grievance_step).id
     }]
  end
end

Fabricator(:employee_grievance_step_create, from: 'change_request') do
  request_type { "employee_grievance_step" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "date": "2021-04-12",
         "recommended_notes": "No remarks",
         "is_settled": "true",
         "is_pending": "false",
         "win": "false",
         "loss": "false",
         "step": "step_1",
         "employee_grievance_id": Fabricate(:employee_grievance).id,
         "grievance_status_id": Fabricate(:grievance_status).id,
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:employee_meeting_type_update, from: 'change_request') do
  request_type { "employee_meeting_type" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "meeting_type_id": Fabricate(:meeting_type).id,
         "attended": "true",
         "meeting_date": "2021-07-20",
         "notes": "Notes",
         "id": Fabricate(:employee_meeting_type).id
     }]
  end
end

Fabricator(:employee_meeting_type_create, from: 'change_request') do
  request_type { "employee_meeting_type" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "meeting_type_id": Fabricate(:meeting_type).id,
         "attended": "true",
         "meeting_date": "2021-07-20",
         "notes": "Notes",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:employee_officer_status_update, from: 'change_request') do
  request_type { "employee_officer_status" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "officer_status_id": Fabricate(:officer_status).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "id": Fabricate(:employee_officer_status).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_officer_status_create, from: 'change_request') do
  request_type { "employee_officer_status" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "officer_status_id": Fabricate(:officer_status).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_office_update, from: 'change_request') do
  request_type { "employee_office" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "office_id": Fabricate(:office).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "id": Fabricate(:employee_office).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_office_create, from: 'change_request') do
  request_type { "employee_office" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "office_id": Fabricate(:office).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_pacf_update, from: 'change_request') do
  request_type { "employee_pacf" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "pacf_id": Fabricate(:pacf).id,
         "date": "2021-06-02",
         "notes": "Notes",
         "amount": 100.00,
         "id": Fabricate(:employee_pacf).id
     }]
  end
end

Fabricator(:employee_pacf_create, from: 'change_request') do
  request_type { "employee_pacf" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "pacf_id": Fabricate(:pacf).id,
         "date": "2021-06-02",
         "notes": "Notes",
         "amount": 100.00,
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:employee_position_update, from: 'change_request') do
  request_type { "employee_position" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "position_id": Fabricate(:position).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "notes": "Notes",
         "id": Fabricate(:employee_position).id,
         "employee_id": Fabricate(:employee).id,
         "delegate_series_id": Fabricate(:delegate_series).id
     }]
  end
end

Fabricator(:employee_position_create, from: 'change_request') do
  request_type { "employee_position" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "position_id": Fabricate(:position).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "notes": "Notes",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id,
         "delegate_series_id": Fabricate(:delegate_series).id
     }]
  end
end

Fabricator(:employee_rank_update, from: 'change_request') do
  request_type { "employee_rank" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "rank_id": Fabricate(:rank).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "id": Fabricate(:employee_rank).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_rank_create, from: 'change_request') do
  request_type { "employee_rank" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "rank_id": Fabricate(:rank).id,
         "start_date": "2021-05-15",
         "end_date": "2021-05-30",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:employee_section_update, from: 'change_request') do
  request_type { "employee_section" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "section_id": Fabricate(:section).id,
         "department_id": Fabricate(:department).id,
         "start_date": "2021-04-12",
         "end_date": "2021-05-18",
         "id": Fabricate(:employee_section).id
     }]
  end
end

Fabricator(:employee_section_create, from: 'change_request') do
  request_type { "employee_section" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "section_id": Fabricate(:section).id,
         "department_id": Fabricate(:department).id,
         "start_date": "2021-04-12",
         "end_date": "2021-05-18",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:employee_title_update, from: 'change_request') do
  request_type { "employee_title" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "section_id": Fabricate(:section).id,
         "department_id": Fabricate(:department).id,
         "title_id": Fabricate(:title).id,
         "start_date": "2021-05-12",
         "end_date": "2021-05-18",
         "notes": "Notes",
         "id": Fabricate(:employee_title).id
     }]
  end
end

Fabricator(:employee_title_create, from: 'change_request') do
  request_type { "employee_title" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "section_id": Fabricate(:section).id,
         "department_id": Fabricate(:department).id,
         "title_id": Fabricate(:title).id,
         "start_date": "2021-05-12",
         "end_date": "2021-05-18",
         "notes": "Notes",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:firearm_range_score_update, from: 'change_request') do
  request_type { "firearm_range_score" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "test_date": "2021-03-12",
         "score": "1",
         "test_type": "test type",
         "notes": "Notes",
         "id": Fabricate(:firearm_range_score).id
     }]
  end
end

Fabricator(:firearm_range_score_create, from: 'change_request') do
  request_type { "firearm_range_score" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "test_date": "2021-03-12",
         "score": "1",
         "test_type": "test type",
         "notes": "Notes",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:leave_update, from: 'change_request') do
  request_type { "leave" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "started_at": "2021-03-30",
         "ended_at": "2021-04-01",
         "hours_used": 4,
         "leave_type": "sick",
         "notes": "Notes",
         "id": Fabricate(:leave).id
     }]
  end
end

Fabricator(:leave_create, from: 'change_request') do
  request_type { "leave" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "started_at": "2021-03-30",
         "ended_at": "2021-04-01",
         "hours_used": 4,
         "leave_type": "sick",
         "notes": "Notes",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:life_insurance_update, from: 'change_request') do
  request_type { "life_insurance" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "notes": "Notes",
         "amount": "15000",
         "status": "pending",
         "start_date": "2021-03-30",
         "employee_id": Fabricate(:employee).id,
         "insurance_type": "basic",
         "id": Fabricate(:life_insurance).id
     }]
  end
end

Fabricator(:life_insurance_create, from: 'change_request') do
  request_type { "life_insurance" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "notes": "Notes",
         "amount": "15000",
         "status": "pending",
         "start_date": "2021-03-30",
         "employee_id": Fabricate(:employee).id,
         "insurance_type": "basic",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:lodi_update, from: 'change_request') do
  request_type { "lodi" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "incident_date": "2021-03-30",
         "return_date": "2021-04-01",
         "office_id": Fabricate(:office).id,
         "injury": "Injury",
         "wcb": "WCB",
         "carrier_case": "Carrier Case",
         "notes": "Notes",
         "approved": "true",
         "denied": "false",
         "id": Fabricate(:lodi).id
     }]
  end
end

Fabricator(:lodi_create, from: 'change_request') do
  request_type { "lodi" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "employee_id": Fabricate(:employee).id,
         "incident_date": "2021-03-30",
         "return_date": "2021-04-01",
         "office_id": Fabricate(:office).id,
         "injury": "Injury",
         "wcb": "WCB",
         "carrier_case": "Carrier Case",
         "notes": "Notes",
         "approved": "true",
         "denied": "false",
         "timestamp": Time.now.to_i
     }]
  end
end

Fabricator(:lodi_request_tab_update, from: 'change_request') do
  request_type { "lodi_request_tab" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "date": "2021-04-23",
         "remarks": "No remarks",
         "request_type": "medscope",
         "lodi_status": "Granted",
         "reason": "Causal Connection",
         "lodi_id": Fabricate(:lodi).id,
         "id": Fabricate(:lodi_request_tab).id
     }]
  end
end

Fabricator(:lodi_request_tab_create, from: 'change_request') do
  request_type { "lodi_request_tab" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "date": "2021-04-23",
         "remarks": "No remarks",
         "request_type": "medscope",
         "lodi_status": "Granted",
         "reason": "Causal Connection",
         "lodi_id": Fabricate(:lodi).id,
         "timestamp": Time.now.to_i
     }]
  end
end
Fabricator(:upload_update, from: 'change_request') do
  request_type { "upload" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "notes": "Notes",
         "id": Fabricate(:upload).id,
         "employee_id": Fabricate(:employee).id
     }]
  end
end

Fabricator(:upload_create, from: 'change_request') do
  request_type { "upload" }
  employee_id {Fabricate(:employee).id}
  requested_changes do
    [{
         "status": "pending",
         "notes": "Notes",
         "timestamp": Time.now.to_i,
         "employee_id": Fabricate(:employee).id
     }]
  end
end