# frozen_string_literal: true

Fabricator(:contact) do
  contact_for { 'work' }
  contact_type { 'email' }
  value { '<EMAIL>' }

  employee
end

Fabricator(:contact_phone, from: 'contact') do
  contact_for { 'work' }
  contact_type { 'phone' }
  value { '(123) 123 - 1111' }

  employee
end

Fabricator(:contact2, from: 'contact') do
  contact_for { 'work' }
  contact_type { 'email' }
  value { 'abcgmail.com' }

  employee
end

Fabricator(:contact1, from: 'contact') do
  contact_for { 'work' }
  contact_type { 'phone' }
  value { '(123) 123 - 11' }

  employee
end

Fabricator(:personal_contact, from: 'contact') do
  contact_for { 'personal' }
  contact_type { 'email' }
  value { '<EMAIL>' }

  employee
end