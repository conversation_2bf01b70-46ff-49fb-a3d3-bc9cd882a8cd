# frozen_string_literal: true

Fabricator(:notification) do
  subject { Faker::Lorem.word }
  sms_message { Faker::Lorem.paragraph }
  email_message { Faker::Lorem.paragraph }
  push_message { Faker::Lorem.paragraph }
  email_to { 'work' }
  sms { [true, false].sample }
  email { [true, false].sample }
  push { [true, false].sample }
  filters do
    { "employee_ids": [
      ''
    ],
      "department_ids": [
        ''
      ],
      "section_ids": [
        ''
      ],

      "title_ids": [
        ''
      ],
      "employment_status_ids": [
        ''
      ],
      "marital_status_ids": [
        ''
      ],
      "email": '',
      "position_ids": [
        ''
      ],
      "social_security_number": '',
      "a_number": '',
      "shield_number": '',
      "birthday_from": '',
      "birthday_to": '',
      "member_since_from_date": '',
      "member_since_to_date": '',
      "start_date_from": '',
      "start_date_to": '',
      "home_phone": '',
      "cellphone": '',
      "work_phone_1": '',
      "work_phone_2": '',
      "city": [
        ''
      ],
      "state": [
        ''
      ],
      "zipcode": [],
      "unit_ids": [
        ''
      ] }
  end
end
