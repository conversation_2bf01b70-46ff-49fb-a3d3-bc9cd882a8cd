# frozen_string_literal: true

Fabricator(:upload) do
  file do
    Rack::Test::UploadedFile.new(
      './spec/assets/test.pdf',
      'application/pdf'
    )
  end
  notes { Faker::Lorem.paragraph }

  employee
end

Fabricator(:upload_sample, from: 'upload') do
  file do
    Rack::Test::UploadedFile.new(
      './spec/assets/sample.pdf',
      'application/pdf'
    )
  end
  notes { Faker::Lorem.paragraph }

  employee
end
