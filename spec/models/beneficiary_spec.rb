# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Beneficiary, type: :model do
  describe 'validation' do
    subject { Fabricate(:beneficiary) }

    describe 'valid beneficiary' do
      it { expect(subject).to validate_presence_of(:name) }
      it { expect(subject).to validate_numericality_of(:percentage) }
      it { expect(subject).to belong_to(:employee) }
      it { expect(subject).to belong_to(:gender).optional }
    end
  end
end
