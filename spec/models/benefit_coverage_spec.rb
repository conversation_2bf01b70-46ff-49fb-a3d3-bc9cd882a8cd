# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BenefitCoverage, type: :model do
  describe 'validation' do
    subject { Fabricate(:benefit_coverage) }

    describe 'valid benefit_coverage' do
      it { expect(subject).to validate_presence_of(:address) }
      it { expect(subject).to validate_presence_of(:birthday) }
      it { expect(subject).to validate_presence_of(:relationship) }
      it { expect(subject).to validate_presence_of(:social_security_number) }
      it { expect(subject).to belong_to(:employee) }
      it { expect(subject).to belong_to(:employee_benefit) }
      it { expect(subject).to belong_to(:gender).optional }
    end
  end
end
