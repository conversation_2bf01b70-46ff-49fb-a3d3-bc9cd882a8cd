# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BenefitDisbursement, type: :model do
  describe 'validation' do
    subject { Fabricate(:benefit_disbursement) }

    describe 'valid benefit_disbursement' do
      it { expect(subject).to validate_numericality_of(:amount) }
      it { expect(subject).to validate_presence_of(:date) }
      it { expect(subject).to validate_presence_of(:reference_number) }
      it { expect(subject).to validate_numericality_of(:year) }
      it { expect(subject).to belong_to(:employee) }
      it { expect(subject).to belong_to(:employee_benefit) }
      it { expect(subject).to belong_to(:payment_type).optional }
    end
  end
end
