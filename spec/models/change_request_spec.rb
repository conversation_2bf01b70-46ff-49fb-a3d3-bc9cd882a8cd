require 'rails_helper'

RSpec.describe ChangeRequest, type: :model do
  it { should belong_to(:employee) }
  it { should have_many(:change_request_uploads) }
  it { should validate_presence_of(:request_type) }
  it { should define_enum_for(:status).with_values([:pending, :completed]) }
  it { should define_enum_for(:request_type).with_values(%w[employee contact award beneficiary benefit_coverage benefit_disbursement delegate_assignment
                        dependent employee_benefit employee_department employee_discipline_setting employee_discipline_step
                        employee_employment_status employee_firearm_status employee_grievance_step employee_grievance
                        employee_meeting_type employee_officer_status employee_office employee_pacf employee_position
                        employee_rank employee_section employee_title firearm_range_score leave life_insurance lodi
                        lodi_request_tab upload]) }
end
