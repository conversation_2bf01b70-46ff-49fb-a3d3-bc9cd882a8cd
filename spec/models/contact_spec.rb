# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Contact, type: :model do
  describe 'validation' do
    subject { Fabricate(:contact) }

    describe 'valid contact' do
      it { expect(subject).to validate_inclusion_of(:contact_for).in_array(Contact::CONTACT_FOR) }
      it { expect(subject).to validate_inclusion_of(:contact_type).in_array(Contact::CONTACT_TYPES) }
      it { expect(subject).to validate_presence_of(:employee) }
      it { expect(subject).to belong_to(:employee) }
    end
  end
end
