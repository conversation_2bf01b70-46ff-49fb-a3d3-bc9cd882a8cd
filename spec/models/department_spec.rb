# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Department, type: :model do
  describe 'validation' do
    subject { Fabricate(:department, name: 'testing') }

    describe 'valid department' do
      it { expect(subject).to validate_presence_of(:name) }
      it { expect(subject).to have_many(:titles) }
      it { expect(subject).to have_many(:sections) }
      it { expect(subject).to have_many(:employee_departments) }
    end
  end
end
