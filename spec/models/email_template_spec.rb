# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::EmailTemplatesController, type: :controller do
  let(:valid_template_id) { 'valid-template-id' }
  let(:invalid_template_id) { 'invalid-template-id' }
  let(:template_html) { '<html><body><h1>{{header}}</h1><p>{{message}}</p></body></html>' }
  let(:template_html_1) { '<html><body><h1>{{header}}</h1></body></html>' }
  let(:sendgrid_service_mock) { instance_double(SendgridService) }

  before do
    # Mock the SendgridService
    allow(SendgridService).to receive(:new).and_return(sendgrid_service_mock)
  end

  describe 'GET #index' do
    it 'returns a list of email templates' do
      allow(sendgrid_service_mock).to receive(:fetch_template).and_return(template_html)

      EmailTemplate.create!(template_id: valid_template_id, template_html: template_html)

      get :index

      expect(response).to have_http_status(:ok)

      body = JSON.parse(response.body)

      expect(body.length).to eq(1)
      expect(body.first['sendgrid_template_id']).to eq(valid_template_id)
      expect(body.first['html_content']).to eq(template_html)
    end
  end

  describe 'GET #show' do
    it 'returns a single email template by ID' do
      allow(sendgrid_service_mock).to receive(:fetch_template).and_return(template_html)

      email_template = EmailTemplate.create!(template_id: valid_template_id, template_html: template_html)

      get :show, params: { id: email_template.id }

      expect(response).to have_http_status(:ok)

      body = JSON.parse(response.body)

      expect(body['id']).to eq(email_template.id)
      expect(body['sendgrid_template_id']).to eq(valid_template_id)
      expect(body['html_content']).to eq(template_html)
    end

    it 'returns a 404 when the template is not found' do
      get :show, params: { id: 999999 }

      expect(response).to have_http_status(:not_found)
    end
  end
end
