# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Employee, type: :model do
  describe 'validation' do
    subject { Fabricate(:employee) }

    describe 'valid employee' do
      it { expect(subject).to validate_presence_of(:birthday) }
      it { expect(subject).to validate_presence_of(:city) }
      it { expect(subject).to validate_presence_of(:first_name) }
      it { expect(subject).to validate_presence_of(:last_name) }
      it { expect(subject).to validate_presence_of(:shield_number) }
      it { expect(subject).to validate_presence_of(:social_security_number) }
      it { expect(subject).to validate_presence_of(:state) }
      it { expect(subject).to validate_presence_of(:street) }
      it { expect(subject).to validate_presence_of(:zipcode) }
      it { expect(subject).to belong_to(:marital_status).optional }
      it { expect(subject).to belong_to(:gender).optional }
      it { expect(subject).to belong_to(:unit).optional }
      it { expect(subject).to belong_to(:affiliation).optional }
      it { expect(subject).to belong_to(:tour_of_duty).optional }
      it { expect(subject).to belong_to(:legislative_detail).optional }
      it { expect(subject).to have_many(:awards) }
      it { expect(subject).to have_many(:beneficiaries) }
      it { expect(subject).to have_many(:benefit_coverages) }
      it { expect(subject).to have_many(:benefit_disbursements) }
      it { expect(subject).to have_many(:change_requests) }
      it { expect(subject).to have_many(:comp_leaves) }
      it { expect(subject).to have_many(:contacts) }
      it { expect(subject).to have_many(:delegate_assignments) }
      it { expect(subject).to have_many(:disciplines) }
      it { expect(subject).to have_many(:employee_benefits) }
      it { expect(subject).to have_many(:employee_discipline_settings) }
      it { expect(subject).to have_many(:employee_grievances) }
      it { expect(subject).to have_many(:employee_employment_statuses) }
      it { expect(subject).to have_many(:employee_firearm_statuses) }
      it { expect(subject).to have_many(:employee_pacfs) }
      it { expect(subject).to have_many(:employee_meeting_types) }
      it { expect(subject).to have_many(:employee_officer_statuses) }
      it { expect(subject).to have_many(:employee_offices) }
      it { expect(subject).to have_many(:employee_positions) }
      it { expect(subject).to have_many(:employee_ranks) }
      it { expect(subject).to have_many(:employee_departments) }
      it { expect(subject).to have_many(:employee_sections) }
      it { expect(subject).to have_many(:employee_titles) }
      it { expect(subject).to have_many(:firearm_range_scores) }
      it { expect(subject).to have_many(:leaves) }
      it { expect(subject).to have_many(:lodis) }
      it { expect(subject).to have_many(:officer_statuses) }
      it { expect(subject).to have_many(:offices) }
      it { expect(subject).to have_many(:ranks) }
      it { expect(subject).to have_many(:titles) }
      it { expect(subject).to have_many(:overtimes) }
      it { expect(subject).to have_many(:personal_leaves) }
      it { expect(subject).to have_many(:sick_leaves) }
      it { expect(subject).to have_many(:totalities) }
      it { expect(subject).to have_many(:peshes) }
      it { expect(subject).to have_many(:vacation_leaves) }
      it { expect(subject).to have_many(:uploads) }
      it { expect(subject).to have_many(:employee_employment_statuses) }
      it { expect(subject).to have_many(:employment_statuses) }
      it { expect(subject).to have_many(:dependents) }
      it { expect(subject).to have_many(:life_insurances) }
      it { expect(subject).to have_one(:mailing_address) }
      it { expect(subject).to have_many(:assaults) }
    end
  end
end
