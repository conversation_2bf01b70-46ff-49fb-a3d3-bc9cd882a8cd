# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Gender, type: :model do
  describe 'validation' do
    subject { Fabricate(:gender) }

    describe 'valid gender' do
      it { expect(subject).to validate_presence_of(:name) }
      it { expect(subject).to have_many(:employees) }
      it { expect(subject).to have_many(:beneficiaries) }
      it { expect(subject).to have_many(:benefit_coverages) }
    end
  end
end
