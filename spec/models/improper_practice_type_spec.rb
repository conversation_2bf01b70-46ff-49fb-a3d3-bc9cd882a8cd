# spec/models/improper_practice_type_spec.rb
require 'rails_helper'

RSpec.describe ImproperPracticeType, type: :model do
  describe 'associations' do
    it { is_expected.to have_many(:improper_practices) }
  end

  describe 'validations' do
    subject { Fabricate(:improper_practice_type) }

    it { is_expected.to validate_length_of(:name).is_at_most(100) }
  end

  describe 'friendly_id' do
    let(:improper_practice_type) { Fabricate(:improper_practice_type, name: 'Test Name') }

    it 'generates a slug from the name' do
      expect(improper_practice_type.slug).to eq('test-name')
    end
  end

  describe 'soft delete' do
    let(:improper_practice_type) { Fabricate(:improper_practice_type) }

    it 'sets discarded_at when discarded' do
      improper_practice_type.discard
      expect(improper_practice_type.discarded_at).not_to be_nil
    end
  end

end