# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Lodi, type: :model do
  describe 'validation' do
    subject { Fabricate(:lodi) }

    describe 'valid lodi' do
      it { expect(subject).to validate_presence_of(:incident_date) }
      it { expect(subject).to belong_to(:employee) }
      it { expect(subject).to belong_to(:office).optional }
      it { expect(subject).to have_many(:lodi_request_tabs) }
      it { expect(subject).to have_and_belong_to_many(:denied_reasons) }
      it { expect(subject).to validate_inclusion_of(:lodi_type).in_array(Lodi::LODI_TYPES) }
    end
  end
end
