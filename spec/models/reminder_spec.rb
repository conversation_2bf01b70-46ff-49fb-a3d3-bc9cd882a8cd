# frozen_string_literal: true

require 'rails_helper'

RSpec.describe <PERSON>minder, type: :model do
  let(:reminder) { Fabricate(:reminder) }

  describe 'validations' do
    subject { reminder }

    it { is_expected.to validate_presence_of(:description) }
    it { is_expected.to validate_presence_of(:title) }
    it { is_expected.to validate_presence_of(:reminder_start_date) }
    it { is_expected.to validate_presence_of(:time) }

    context 'when reminder_end_date is present' do
      let(:reminder) { Fabricate.build(:reminder, reminder_end_date: Date.today + 10.days) }

      it 'is valid if end date is after start date' do
        expect(reminder).to be_valid
      end

      it 'is invalid if end date is before start date' do
        reminder.reminder_end_date = reminder.reminder_start_date - 1.day
        expect(reminder).not_to be_valid
        expect(reminder.errors[:reminder_end_date]).to include('must be greater than the start date')
      end
    end

    context 'when reminder_end_date is not present' do
      let(:reminder) { Fabricate.build(:reminder, reminder_end_date: nil) }

      it 'is valid without an end date' do
        expect(reminder).to be_valid
      end
    end
  end

  describe 'associations' do
    subject { reminder }

    it { is_expected.to belong_to(:creator).class_name('User').with_foreign_key('user_id').required(true) }
    it { is_expected.to have_and_belong_to_many(:users).join_table('reminders_users') }
    it { is_expected.to have_many(:reminder_trackers) }
  end

  describe 'callbacks' do
    let(:current_time) { Time.current.in_time_zone('Eastern Time (US & Canada)') }
    let(:reminder) do
      Fabricate.build(
        :reminder,
        reminder_start_date: Date.today + 2.days,
        reminder_end_date: Date.today + 9.days,
        time: (current_time + 1.hour).strftime('%H:%M'),
      )
    end

    it 'does not schedule a job if start time is more than 24 hours away' do
      expect(ReminderJob).not_to receive(:set)
      expect(reminder.job_id).to eq(nil)
    end

    it 'status should be pending if start time is more than 24 hours away' do
      expect(reminder.status).to eq('pending')
    end

  end

end