# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Section, type: :model do
  describe 'validation' do
    subject { Fabricate(:section) }

    describe 'valid section' do
      it { expect(subject).to validate_presence_of(:name) }
      it { expect(subject).to belong_to(:department) }
      it { expect(subject).to have_many(:employee_sections) }
      it { expect(subject).to have_many(:employee_titles) }
    end
  end
end
