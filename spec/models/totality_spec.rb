# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Totality, type: :model do
  describe 'validation' do
    subject { Fabricate(:totality) }

    describe 'valid totality' do
      it { expect(subject).to validate_presence_of(:employee) }
      it { expect(subject).to validate_presence_of(:totalable_type) }
      it { expect(subject).to validate_numericality_of(:value) }
      it { expect(subject).to belong_to(:employee) }
    end
  end
end
