# frozen_string_literal: true

# Shared context with for testing unauthenticated requests
RSpec.shared_context 'invalid auth credentials' do |method:, action:, params: {}|
  after(:each) do
    expect(response).to have_http_status(401)
    expect(error_response).to eq(['Your session expired. Please sign in again to continue.'])
  end

  before(:each) do
    allow(controller).to receive(:set_paper_trail_whodunnit).and_return(true)
  end

  it 'should return 401 - JWT auth cookie missing' do
    process action, method: method, params: params
  end

  it 'Should return 401 - JWT auth cookie invalid' do
    request.cookies['fusesystems_session'] = 'eyJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoyLCJleHAiOjE1ODQ'

    process action, method: method, params: params
  end

  it 'should return 401 - JWT auth cookie missing but cookies present' do
    request.cookies['fusesystems_session'] = ''

    process action, method: method, params: params
  end

  it 'should return 401 - expired token', expired_token: true do
    process action, method: method, params: params
  end
end
