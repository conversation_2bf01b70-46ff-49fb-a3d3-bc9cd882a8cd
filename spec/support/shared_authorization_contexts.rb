# frozen_string_literal: true

RSpec.shared_context 'Authorization specs', authenticated: true do |method:, action:, args: {}|
  current_rights = args[:rights].presence || args[:object_name]
  let(:role) { Fabricate(:custom_role) }

  let(:permission) do
    lambda { |right|
      role.rights << Right.where(name: right)
      @user.update(role_id: role.id)
    }
  end

  let(:request_params) do
    return args[:params] if args[:params].present?

    if action == 'update'
      params = { id: Fabricate(args[:object_name].to_sym).id }
      params[args[:object_name]] = Fabricate.attributes_for(args[:object_name].to_sym)
      params
    elsif action == 'create'
      params = {}
      params[args[:object_name]] = Fabricate.attributes_for(args[:object_name].to_sym)
      params
    elsif action == 'destroy'
      { id: Fabricate(args[:object_name].to_sym).id }
    else
      {}
    end
  end

  it 'Should not allow user to perform the action without required permission' do
    permission[nil]
    process action, method: method, params: request_params
    expect(error_response).to eq(['Access Denied: you are not authorized to perform this action'])
  end

  if action == 'index'
    it 'Should allow user to perform the action with required read permission' do
      permission["read_#{current_rights}"]

      process action, method: method, params: request_params
      success_response
    end
  else
    it 'Should not allow user to perform the action without required read permission' do
      permission["read_#{current_rights}"]

      process action, method: method, params: request_params
      expect(error_response).to eq(['Access Denied: you are not authorized to perform this action'])
    end
  end

  it 'Should allow user to perform the action with required write permission' do
    permission["write_#{current_rights}"]

    process action, method: method, params: request_params
    success_response
  end
end
